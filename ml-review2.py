#!/usr/bin/env python
# coding: utf-8

"""
Audio Classification Project: Scream Detection
Optimized version with improved data handling, model architecture, and training procedures.
"""

import numpy as np
import pandas as pd
import os
import warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
import torch
import random
np.random.seed(42)
torch.manual_seed(42)
random.seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)


# Import required libraries
import torchaudio
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
from pathlib import Path
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# Configuration class for better parameter management
class Config:
    # Audio processing parameters
    SAMPLE_RATE = 22050  # Standardized sample rate
    DURATION = 3.0  # Fixed duration in seconds
    N_MELS = 128  # Increased mel bands for better frequency resolution
    N_FFT = 2048  # Increased FFT size
    HOP_LENGTH = 512

    # Training parameters
    BATCH_SIZE = 32
    LEARNING_RATE = 0.001
    EPOCHS = 50
    EARLY_STOPPING_PATIENCE = 10

    # Data paths (update these for your local environment)
    POSITIVE_PATH = '/kaggle/input/ml-review-2-2/positive'
    NEGATIVE_PATH = '/kaggle/input/ml-review-2-2/negative'

def load_audio_files(path: str, label: str, max_files: int = None):
    """
    Improved audio loading function with error handling and standardization.
    """
    dataset = []
    walker = sorted(str(p) for p in Path(path).glob('*.wav'))

    if max_files:
        walker = walker[:max_files]

    print(f"Loading {len(walker)} files from {path}")

    for i, file_path in enumerate(walker):
        try:
            # Load audio with error handling
            waveform, sample_rate = torchaudio.load(file_path)

            # Convert to mono if stereo
            if waveform.shape[0] > 1:
                waveform = torch.mean(waveform, dim=0, keepdim=True)

            # Resample to standard sample rate if needed
            if sample_rate != Config.SAMPLE_RATE:
                resampler = torchaudio.transforms.Resample(sample_rate, Config.SAMPLE_RATE)
                waveform = resampler(waveform)

            # Standardize duration
            target_length = int(Config.SAMPLE_RATE * Config.DURATION)
            current_length = waveform.shape[1]

            if current_length > target_length:
                # Trim to target length
                waveform = waveform[:, :target_length]
            elif current_length < target_length:
                # Pad with zeros
                padding = target_length - current_length
                waveform = F.pad(waveform, (0, padding))

            entry = {
                'waveform': waveform,
                'sample_rate': Config.SAMPLE_RATE,
                'label': label,
                'file_path': file_path
            }
            dataset.append(entry)

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            continue

    print(f"Successfully loaded {len(dataset)} files")
    return dataset


# Custom Dataset class for better data handling
class AudioDataset(Dataset):
    def __init__(self, data, transform=None):
        self.data = data
        self.transform = transform

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        item = self.data[idx]
        waveform = item['waveform']
        label = 1 if item['label'] == 'yes' else 0

        # Convert to mel-spectrogram
        mel_spec = torchaudio.transforms.MelSpectrogram(
            sample_rate=Config.SAMPLE_RATE,
            n_mels=Config.N_MELS,
            n_fft=Config.N_FFT,
            hop_length=Config.HOP_LENGTH
        )(waveform)

        # Convert to log scale and add small epsilon to avoid log(0)
        mel_spec = torch.log(mel_spec + 1e-8)

        # Normalize
        mel_spec = (mel_spec - mel_spec.mean()) / (mel_spec.std() + 1e-8)

        if self.transform:
            mel_spec = self.transform(mel_spec)

        return mel_spec.squeeze(0), label

# Data augmentation transforms
class AudioAugmentation:
    @staticmethod
    def time_mask(spec, max_mask_pct=0.1):
        """Apply time masking to spectrogram"""
        _, time_steps = spec.shape
        mask_size = int(time_steps * max_mask_pct)
        mask_start = torch.randint(0, time_steps - mask_size, (1,))
        spec[:, mask_start:mask_start + mask_size] = spec.mean()
        return spec

    @staticmethod
    def freq_mask(spec, max_mask_pct=0.1):
        """Apply frequency masking to spectrogram"""
        freq_bins, _ = spec.shape
        mask_size = int(freq_bins * max_mask_pct)
        mask_start = torch.randint(0, freq_bins - mask_size, (1,))
        spec[mask_start:mask_start + mask_size, :] = spec.mean()
        return spec

def get_augmentation_transform():
    """Returns augmentation transform for training data"""
    def augment(spec):
        if torch.rand(1) > 0.5:
            spec = AudioAugmentation.time_mask(spec)
        if torch.rand(1) > 0.5:
            spec = AudioAugmentation.freq_mask(spec)
        return spec
    return augment

# Load and prepare data
print("Loading datasets...")
screaming_dataset = load_audio_files(Config.POSITIVE_PATH, 'yes')
not_screaming_dataset = load_audio_files(Config.NEGATIVE_PATH, 'not')

print(f"Screaming samples: {len(screaming_dataset)}")
print(f"Non-screaming samples: {len(not_screaming_dataset)}")

# Combine datasets
all_data = screaming_dataset + not_screaming_dataset
print(f"Total samples: {len(all_data)}")


# Improved CNN model specifically designed for audio spectrograms
class AudioCNN(nn.Module):
    def __init__(self, num_classes=2):
        super(AudioCNN, self).__init__()

        # Convolutional layers with batch normalization and dropout
        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(32)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(64)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.bn3 = nn.BatchNorm2d(128)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.bn4 = nn.BatchNorm2d(256)

        self.pool = nn.MaxPool2d(2, 2)
        self.dropout = nn.Dropout(0.3)
        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))

        # Fully connected layers
        self.fc1 = nn.Linear(256, 128)
        self.fc2 = nn.Linear(128, num_classes)

    def forward(self, x):
        # Add channel dimension if needed
        if x.dim() == 3:
            x = x.unsqueeze(1)

        x = self.pool(F.relu(self.bn1(self.conv1(x))))
        x = self.dropout(x)
        x = self.pool(F.relu(self.bn2(self.conv2(x))))
        x = self.dropout(x)
        x = self.pool(F.relu(self.bn3(self.conv3(x))))
        x = self.dropout(x)
        x = self.pool(F.relu(self.bn4(self.conv4(x))))

        x = self.global_pool(x)
        x = x.view(x.size(0), -1)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)

        return x

# Data splitting and preparation
from sklearn.model_selection import train_test_split

# Split data into train/validation/test
train_data, temp_data = train_test_split(all_data, test_size=0.3, random_state=42,
                                        stratify=[item['label'] for item in all_data])
val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42,
                                      stratify=[item['label'] for item in temp_data])

print(f"Train samples: {len(train_data)}")
print(f"Validation samples: {len(val_data)}")
print(f"Test samples: {len(test_data)}")

# Create datasets with augmentation for training
train_dataset = AudioDataset(train_data, transform=get_augmentation_transform())
val_dataset = AudioDataset(val_data)
test_dataset = AudioDataset(test_data)

# Create data loaders
train_loader = DataLoader(train_dataset, batch_size=Config.BATCH_SIZE, shuffle=True, num_workers=2)
val_loader = DataLoader(val_dataset, batch_size=Config.BATCH_SIZE, shuffle=False, num_workers=2)
test_loader = DataLoader(test_dataset, batch_size=Config.BATCH_SIZE, shuffle=False, num_workers=2)


# Training and evaluation functions
def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0

    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        running_loss += loss.item()
        _, predicted = output.max(1)
        total += target.size(0)
        correct += predicted.eq(target).sum().item()

        if batch_idx % 10 == 0:
            print(f'Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}, Acc: {100.*correct/total:.2f}%')

    epoch_loss = running_loss / len(train_loader)
    epoch_acc = 100. * correct / total
    return epoch_loss, epoch_acc

def validate_epoch(model, val_loader, criterion, device):
    model.eval()
    val_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            val_loss += criterion(output, target).item()

            _, predicted = output.max(1)
            total += target.size(0)
            correct += predicted.eq(target).sum().item()

    val_loss /= len(val_loader)
    val_acc = 100. * correct / total
    return val_loss, val_acc

def evaluate_model(model, test_loader, device):
    model.eval()
    y_true = []
    y_pred = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            _, predicted = output.max(1)

            y_true.extend(target.cpu().numpy())
            y_pred.extend(predicted.cpu().numpy())

    return y_true, y_pred


# Main training function
def train_model():
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Initialize model
    model = AudioCNN(num_classes=2).to(device)

    # Loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=Config.LEARNING_RATE, weight_decay=1e-4)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)

    # Training loop with early stopping
    best_val_loss = float('inf')
    patience_counter = 0
    train_losses, val_losses = [], []
    train_accs, val_accs = [], []

    print("Starting training...")
    for epoch in range(Config.EPOCHS):
        print(f'\nEpoch {epoch+1}/{Config.EPOCHS}')
        print('-' * 50)

        # Training
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)

        # Validation
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)

        # Learning rate scheduling
        scheduler.step(val_loss)

        # Store metrics
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        train_accs.append(train_acc)
        val_accs.append(val_acc)

        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')

        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # Save best model
            torch.save(model.state_dict(), 'best_audio_model.pth')
            print("New best model saved!")
        else:
            patience_counter += 1

        if patience_counter >= Config.EARLY_STOPPING_PATIENCE:
            print(f"Early stopping triggered after {epoch+1} epochs")
            break

    # Load best model for evaluation
    model.load_state_dict(torch.load('best_audio_model.pth'))

    return model, train_losses, val_losses, train_accs, val_accs


# Visualization functions
def plot_training_history(train_losses, val_losses, train_accs, val_accs):
    """Plot training history"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # Plot losses
    ax1.plot(train_losses, label='Train Loss')
    ax1.plot(val_losses, label='Validation Loss')
    ax1.set_title('Training and Validation Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True)

    # Plot accuracies
    ax2.plot(train_accs, label='Train Accuracy')
    ax2.plot(val_accs, label='Validation Accuracy')
    ax2.set_title('Training and Validation Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy (%)')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()
    plt.show()

def plot_confusion_matrix(y_true, y_pred, class_names=['Non-Scream', 'Scream']):
    """Plot confusion matrix"""
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()

def show_sample_spectrograms(dataset, num_samples=4):
    """Show sample spectrograms from dataset"""
    fig, axes = plt.subplots(2, num_samples//2, figsize=(15, 8))
    axes = axes.flatten()

    for i in range(num_samples):
        spec, label = dataset[i]
        axes[i].imshow(spec.numpy(), cmap='viridis', aspect='auto')
        axes[i].set_title(f'{"Scream" if label == 1 else "Non-Scream"}')
        axes[i].axis('off')

    plt.tight_layout()
    plt.show()


# Main execution function
def main():
    """Main function to run the complete pipeline"""
    print("=" * 60)
    print("AUDIO CLASSIFICATION: SCREAM DETECTION")
    print("=" * 60)

    # Show sample spectrograms
    print("\nShowing sample spectrograms...")
    show_sample_spectrograms(train_dataset)

    # Train the model
    model, train_losses, val_losses, train_accs, val_accs = train_model()

    # Plot training history
    print("\nPlotting training history...")
    plot_training_history(train_losses, val_losses, train_accs, val_accs)

    # Evaluate on test set
    print("\nEvaluating on test set...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    y_true, y_pred = evaluate_model(model, test_loader, device)

    # Print classification report
    print("\nClassification Report:")
    print(classification_report(y_true, y_pred, target_names=['Non-Scream', 'Scream']))

    # Plot confusion matrix
    print("\nPlotting confusion matrix...")
    plot_confusion_matrix(y_true, y_pred)

    # Calculate and print final metrics
    test_acc = 100 * sum([1 for i, j in zip(y_true, y_pred) if i == j]) / len(y_true)
    print(f"\nFinal Test Accuracy: {test_acc:.2f}%")

    return model

# Prediction function for new audio files
def predict_audio_file(model, file_path, device):
    """Predict whether an audio file contains screaming"""
    try:
        # Load and preprocess audio
        waveform, sample_rate = torchaudio.load(file_path)

        # Convert to mono if stereo
        if waveform.shape[0] > 1:
            waveform = torch.mean(waveform, dim=0, keepdim=True)

        # Resample if needed
        if sample_rate != Config.SAMPLE_RATE:
            resampler = torchaudio.transforms.Resample(sample_rate, Config.SAMPLE_RATE)
            waveform = resampler(waveform)

        # Standardize duration
        target_length = int(Config.SAMPLE_RATE * Config.DURATION)
        current_length = waveform.shape[1]

        if current_length > target_length:
            waveform = waveform[:, :target_length]
        elif current_length < target_length:
            padding = target_length - current_length
            waveform = F.pad(waveform, (0, padding))

        # Convert to mel-spectrogram
        mel_spec = torchaudio.transforms.MelSpectrogram(
            sample_rate=Config.SAMPLE_RATE,
            n_mels=Config.N_MELS,
            n_fft=Config.N_FFT,
            hop_length=Config.HOP_LENGTH
        )(waveform)

        mel_spec = torch.log(mel_spec + 1e-8)
        mel_spec = (mel_spec - mel_spec.mean()) / (mel_spec.std() + 1e-8)

        # Add batch dimension
        mel_spec = mel_spec.unsqueeze(0).to(device)

        # Make prediction
        model.eval()
        with torch.no_grad():
            output = model(mel_spec)
            probabilities = F.softmax(output, dim=1)
            predicted_class = output.argmax(dim=1).item()
            confidence = probabilities[0][predicted_class].item()

        result = {
            'prediction': 'Scream' if predicted_class == 1 else 'Non-Scream',
            'confidence': confidence,
            'probabilities': {
                'Non-Scream': probabilities[0][0].item(),
                'Scream': probabilities[0][1].item()
            }
        }

        return result

    except Exception as e:
        return {'error': str(e)}


# Example usage and testing
if __name__ == "__main__":
    # Run the main training pipeline
    print("Starting the audio classification pipeline...")

    try:
        # Train the model
        trained_model = main()

        # Example of how to use the trained model for prediction
        print("\n" + "="*60)
        print("EXAMPLE: Using the trained model for prediction")
        print("="*60)

        # You can test with any audio file like this:
        # result = predict_audio_file(trained_model, 'path/to/your/audio.wav',
        #                           torch.device('cuda' if torch.cuda.is_available() else 'cpu'))
        # print(f"Prediction: {result}")

        print("\nTraining completed successfully!")
        print("Model saved as 'best_audio_model.pth'")
        print("\nTo use the model for prediction on new audio files:")
        print("result = predict_audio_file(model, 'audio_file.wav', device)")

    except Exception as e:
        print(f"An error occurred: {e}")
        print("Please check your data paths and ensure all dependencies are installed.")




