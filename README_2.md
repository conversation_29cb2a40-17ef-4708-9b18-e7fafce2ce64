# Audio Classification Code Documentation

## 📄 About This Code

This is an optimized audio classification system that detects screaming vs non-screaming sounds using deep learning. The code converts audio files into mel-spectrograms and uses a custom CNN model for classification.

## 🏗️ Code Architecture

### **Configuration Class**
```python
class Config:
    SAMPLE_RATE = 22050      # Standard audio sample rate
    DURATION = 3.0           # Fixed audio duration in seconds
    N_MELS = 128            # Number of mel frequency bands
    N_FFT = 2048            # FFT window size
    BATCH_SIZE = 32         # Training batch size
    LEARNING_RATE = 0.001   # Initial learning rate
    EPOCHS = 50             # Maximum training epochs
```

### **Core Components**

#### 1. **Audio Loading & Preprocessing**
- `load_audio_files()`: Loads WAV files with error handling
- Converts stereo to mono automatically
- Resamples all audio to 22050 Hz
- Standardizes duration to 3 seconds
- <PERSON>les padding and trimming intelligently

#### 2. **Custom Dataset Class**
- `AudioDataset`: PyTorch dataset for audio data
- Converts audio to mel-spectrograms on-the-fly
- Applies log scaling and normalization
- Supports data augmentation during training

#### 3. **Data Augmentation**
- `AudioAugmentation`: Time and frequency masking
- `time_mask()`: Masks random time segments
- `freq_mask()`: Masks random frequency bands
- Applied randomly during training for robustness

#### 4. **CNN Model Architecture**
```python
class AudioCNN(nn.Module):
    # 4 Convolutional layers with increasing channels: 32→64→128→256
    # Batch normalization after each conv layer
    # MaxPooling and Dropout for regularization
    # Global Average Pooling to reduce parameters
    # 2 Fully connected layers for classification
```

#### 5. **Training Functions**
- `train_epoch()`: Single epoch training with progress tracking
- `validate_epoch()`: Validation without gradient updates
- `train_model()`: Complete training pipeline with:
  - Learning rate scheduling
  - Early stopping
  - Model checkpointing
  - Progress monitoring

#### 6. **Evaluation & Visualization**
- `evaluate_model()`: Comprehensive model testing
- `plot_training_history()`: Loss and accuracy curves
- `plot_confusion_matrix()`: Classification performance matrix
- `show_sample_spectrograms()`: Visual data samples

#### 7. **Prediction Interface**
- `predict_audio_file()`: Easy prediction for new audio files
- Returns prediction, confidence, and probabilities
- Handles all preprocessing automatically

## 🔄 Data Flow

```
Audio File (.wav) 
    ↓
Load & Validate
    ↓
Convert to Mono + Resample to 22050 Hz
    ↓
Standardize to 3 seconds (pad/trim)
    ↓
Convert to Mel-Spectrogram (128 mel bands)
    ↓
Apply Log Scaling + Normalization
    ↓
Optional Data Augmentation (training only)
    ↓
Feed to CNN Model
    ↓
Output: [Non-Scream, Scream] probabilities
```

## 🎯 Key Features

### **Robust Audio Processing**
- Handles various audio formats and sample rates
- Automatic mono conversion for stereo files
- Intelligent duration standardization
- Error handling for corrupted files

### **Modern CNN Architecture**
- Designed specifically for audio spectrograms
- Batch normalization for training stability
- Dropout layers prevent overfitting
- Global average pooling reduces parameters

### **Professional Training Pipeline**
- Proper train/validation/test splits
- Learning rate scheduling
- Early stopping with patience
- Model checkpointing
- Comprehensive monitoring

### **Advanced Data Augmentation**
- Time masking: Simulates temporal variations
- Frequency masking: Simulates frequency variations
- Applied randomly during training only

## 📊 Model Specifications

- **Input**: Mel-spectrogram (128 x variable_time)
- **Architecture**: 4-layer CNN + 2 FC layers
- **Parameters**: ~500K (lightweight and efficient)
- **Output**: Binary classification (Scream/Non-Scream)
- **Training**: Adam optimizer with ReduceLROnPlateau

## 🚀 Usage Examples

### **Training the Model**
```python
# Simply run the script
python ml-review2.py

# Or call main function
if __name__ == "__main__":
    trained_model = main()
```

### **Making Predictions**
```python
# Load trained model
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = torch.load('best_audio_model.pth')

# Predict on new audio
result = predict_audio_file(model, 'test_audio.wav', device)
print(f"Prediction: {result['prediction']}")
print(f"Confidence: {result['confidence']:.2f}")
```

### **Customizing Configuration**
```python
# Modify Config class for different settings
Config.SAMPLE_RATE = 16000  # Different sample rate
Config.DURATION = 5.0       # Longer audio clips
Config.N_MELS = 64         # Fewer mel bands
Config.BATCH_SIZE = 64     # Larger batches
```

## 🔧 Dependencies

- **PyTorch**: Deep learning framework
- **torchaudio**: Audio processing
- **numpy/pandas**: Data manipulation
- **matplotlib/seaborn**: Visualization
- **scikit-learn**: Evaluation metrics

## 📈 Performance Monitoring

The code automatically tracks and displays:
- Training/validation loss curves
- Training/validation accuracy curves
- Confusion matrix on test set
- Classification report with precision/recall
- Early stopping progress
- Learning rate adjustments

## 🎵 Audio Requirements

- **Format**: WAV files (other formats may work)
- **Duration**: Any (automatically standardized)
- **Sample Rate**: Any (automatically resampled)
- **Channels**: Mono or stereo (converted to mono)
- **Quality**: Standard audio quality sufficient

This code provides a complete, production-ready solution for audio classification with modern deep learning best practices.
