# This Python 3 environment comes with many helpful analytics libraries installed
# It is defined by the kaggle/python Docker image: https://github.com/kaggle/docker-python
# For example, here's several helpful packages to load

import numpy as np # linear algebra
import pandas as pd # data processing, CSV file I/O (e.g. pd.read_csv)

# Input data files are available in the read-only "../input/" directory
# For example, running this (by clicking run or pressing Shift+Enter) will list all files under the input directory

import os
for dirname, _, filenames in os.walk('/kaggle/input'):
    for filename in filenames:
        print(os.path.join(dirname, filename))

# You can write up to 20GB to the current directory (/kaggle/working/) that gets preserved as output when you create a version using "Save & Run All" 
# You can also write temporary files to /kaggle/temp/, but they won't be saved outside of the current session


import os

import torchaudio
import torch
import matplotlib.pyplot as plt
from pathlib import Path

def load_audio_files(path: str, label: str):
    # Initialize an empty list to store the dataset
    dataset = []

    # List all WAV files in the specified path
    walker = sorted(str(p) for p in Path(path).glob('*.wav'))

    # Iterate over the list of audio file paths
    for i, file_path in enumerate(walker):
        # Split the file path into directory and filename
        path, filename = os.path.split(file_path)
        name, _ = os.path.splitext(filename)

        # Load the audio waveform and sample rate using torchaudio
        waveform, sample_rate = torchaudio.load(file_path)

        # Create an entry for the dataset, including waveform, sample rate, label, and ID
        entry = {'waveform': waveform, 'sample_rate': sample_rate, 'label': label, 'id': i}

        # Append the entry to the dataset list
        dataset.append(entry)

    # Return the populated dataset
    return dataset

# Load audio files from the 'Data/Screaming' directory with the label 'yes'
screaming_dataset = load_audio_files('/kaggle/input/ml-review-2-2/positive', 'yes')

# Display the 'screaming_dataset' which contains loaded audio data
screaming_dataset[:5]

# Load audio files from the 'Data/NotScreaming' directory with the label 'not'
not_screaming_dataset = load_audio_files('/kaggle/input/ml-review-2-2/negative', 'not')

# Display the 'screaming_dataset' which contains loaded audio data
not_screaming_dataset[:5]

display(len(screaming_dataset), len(not_screaming_dataset))

scream_id = 5
no_scream_id = 0

# Access the waveform and sample rate of the first entry in the 'screaming_dataset'
screaming_waveform = screaming_dataset[scream_id]['waveform']
screaming_sample_rate = screaming_dataset[scream_id]['sample_rate']

# Print the waveform, sample rate, label, and ID of the first entry
print(f'Screaming Waveform: {screaming_waveform}')
print(f'Screaming Sample Rate: {screaming_sample_rate}')
print(f'Screaming Label: {screaming_dataset[scream_id]["label"]}')
print(f'Screaming ID: {screaming_dataset[scream_id]["id"]} \n')

# Access the waveform and sample rate of the first entry in the 'screaming_dataset'
not_screaming_waveform = not_screaming_dataset[no_scream_id]['waveform']
not_screaming_sample_rate = not_screaming_dataset[no_scream_id]['sample_rate']

# Print the waveform, sample rate, label, and ID of the first entry
print(f'Screaming Waveform: {not_screaming_waveform}')
print(f'Screaming Sample Rate: {not_screaming_sample_rate}')
print(f'Screaming Label: {not_screaming_dataset[no_scream_id]["label"]}')
print(f'Screaming ID: {not_screaming_dataset[no_scream_id]["id"]} \n')

def show_waveform(waveform, sample_rate, label):
    # Print information about the waveform, sample rate, and label
    print("Waveform: {}\nSample rate: {}\nLabels: {} \n".format(waveform, sample_rate, label))

    # Calculate the new sample rate by dividing the original sample rate by 10
    new_sample_rate = sample_rate / 10

    # Resample applies to a single channel, so we resample the first channel here
    channel = 0

    # Apply resampling to the waveform
    waveform_transformed = torchaudio.transforms.Resample(sample_rate, new_sample_rate)(waveform[channel, :].view(1, -1))

    # Print the shape of the transformed waveform and the new sample rate
    print("Shape of transformed waveform: {}\nSample rate: {}".format(waveform_transformed.size(), new_sample_rate))

    # Plot the transformed waveform
    plt.figure()
    plt.plot(waveform_transformed[0, :].numpy())

show_waveform(screaming_waveform, screaming_sample_rate, 'yes')

import IPython.display as ipd
ipd.Audio(screaming_waveform.numpy(), rate=screaming_sample_rate)

show_waveform(not_screaming_waveform, not_screaming_sample_rate, 'not')

ipd.Audio(not_screaming_waveform.numpy(), rate=not_screaming_sample_rate)

def show_spectrogram(waveform_classA, waveform_classB):
    # Compute the spectrogram for the first waveform (class A)
    yes_spectrogram = torchaudio.transforms.Spectrogram()(waveform_classA)
    print("\nShape of yes spectrogram: {}".format(yes_spectrogram.size()))

    # Compute the spectrogram for the second waveform (class B)
    no_spectrogram = torchaudio.transforms.Spectrogram()(waveform_classB)
    print("Shape of no spectrogram: {}".format(no_spectrogram.size()))

    # Create a figure with two subplots for visualization
    plt.figure()

    # Plot the spectrogram of class A (left subplot)
    plt.subplot(1, 2, 1)
    plt.title("Features of {}".format('yes'))

    # Set the aspect ratio to 'auto' for the y-axis to elongate it
    plt.imshow(yes_spectrogram.log2()[0, :, :].numpy(), cmap='viridis', aspect='auto')

    # Plot the spectrogram of class B (right subplot)
    plt.subplot(1, 2, 2)
    plt.title("Features of {}".format('no'))

    # Set the aspect ratio to 'auto' for the y-axis to elongate it
    plt.imshow(no_spectrogram.log2()[0, :, :].numpy(), cmap='viridis', aspect='auto')

show_spectrogram(screaming_waveform, not_screaming_waveform)

def show_mel_spectrogram(waveform, sample_rate):
    # Compute the Mel spectrogram from the input waveform
    mel_spectrogram = torchaudio.transforms.MelSpectrogram(
            sample_rate=sample_rate,
            n_mels=64,
            n_fft=1024
    )(waveform)

    # Print the shape of the Mel spectrogram
    print("Shape of spectrogram: {}".format(mel_spectrogram.size()))

    # Create a new figure for visualization
    plt.figure()

    # Display the Mel spectrogram as an image with a color map 'viridis'
    plt.imshow(mel_spectrogram.log2()[0, :, :].numpy(), cmap='viridis', aspect='auto')

show_mel_spectrogram(screaming_waveform, screaming_sample_rate)

show_mel_spectrogram(not_screaming_waveform, not_screaming_sample_rate)

def show_mfcc(waveform, sample_rate):
    # Compute the MFCC spectrogram from the input waveform
    mfcc_spectrogram = torchaudio.transforms.MFCC(
            sample_rate=sample_rate,
            n_mfcc=128
    )(waveform)

    # Print the shape of the MFCC spectrogram
    print("Shape of spectrogram: {}".format(mfcc_spectrogram.size()))

    # Create a new figure for visualization
    plt.figure()

    # Display the MFCC spectrogram as an image with a color map 'viridis'
    plt.imshow(mfcc_spectrogram.log2()[0, :, :].numpy(), cmap='viridis', aspect='auto')

    # Create a separate figure for the MFCC plot with an elongated y-axis
    plt.figure()
    plt.plot(mfcc_spectrogram.log2()[0, :, :].numpy())
    plt.draw()

show_mfcc(screaming_waveform,  screaming_sample_rate)

show_mfcc(not_screaming_waveform,  not_screaming_sample_rate)

def pad_waveform(waveform, target_length):
    _, num_channels, current_length = waveform.shape

    if current_length < target_length:
        # Calculate the amount of padding needed
        padding = target_length - current_length
        # Pad the waveform with zeros on the right side
        waveform = torch.nn.functional.pad(waveform, (0, padding))

    return waveform


def create_images(train_loader, label_dir, amplitude_threshold=0.01):
    # Make directory
    directory = f'Data/Images/{label_dir}/'
    if os.path.isdir(directory):
        print("Data exists for", label_dir)
    else:
        os.makedirs(directory, mode=0o777, exist_ok=True)

        for i, data in enumerate(train_loader):
            waveform = data['waveform']

            # Pad waveform to a consistent length of 44100 samples
            waveform = pad_waveform(waveform, 441000)

            # Check if the waveform has sufficient amplitude
            if torch.max(torch.abs(waveform)) > amplitude_threshold:
                # Create transformed waveforms
                spectrogram_tensor = torchaudio.transforms.MelSpectrogram(
                    sample_rate=int(data['sample_rate']),
                    n_mels=64,
                    n_fft=1024,
                )(waveform)

                plt.imsave(f'Data/Images/{label_dir}/audio_img{i}.png', (spectrogram_tensor[0] + 1e-10).log2()[0, :, :].numpy(), cmap='viridis')
            else:
                print(f'Skipping blank waveform {i} in {label_dir}')

from torch.utils.data import DataLoader

train_loader_scream = DataLoader(screaming_dataset, batch_size=1,
                                              shuffle=False, num_workers=0)
train_loader_not_scream = DataLoader(not_screaming_dataset, batch_size=1,
                                             shuffle=False, num_workers=0)

create_images(train_loader_scream, 'scream')

create_images(train_loader_not_scream, 'not')

from torchvision.transforms import Lambda
import torch

def random_time_shift(audio, max_shift_ms=1000, target_length=441000):
    # Calculate the current length of the audio
    current_length = audio.shape[-1]

    # If the audio is longer than the target length, perform random shift
    if current_length >= target_length:
        shift = torch.randint(-max_shift_ms, max_shift_ms, (1,)).item()
        shift_samples = int(shift * 44100 / 1000)  # Assuming a 44.1 kHz sample rate
        if shift_samples >= 0:
            audio = torch.nn.functional.pad(audio, (shift_samples, 0))
            # Cut the audio from the end to fit the target_length
            audio = audio[:, :target_length]
        else:
            audio = torch.nn.functional.pad(audio, (0, -shift_samples))
            # Cut the audio from the front to fit the target_length
            audio = audio[:, -target_length:]
    else:
        # If the audio is shorter than the target length, pad it on both ends
        padding = target_length - current_length
        # Calculate shift_samples to be greater than 0
        shift_samples = torch.randint(1, padding, (1,)).item()

        # Determine the left padding as a random number within the range [0, shift_samples]
        left_padding = torch.randint(0, shift_samples, (1,)).item()
        
        # Calculate the right padding as the difference between padding and left_padding
        right_padding = padding - left_padding

        audio = torch.nn.functional.pad(audio, (left_padding, right_padding))

    return audio

def add_noise(audio, noise_level=0.005):
    noise = noise_level * torch.randn_like(audio)
    noisy_audio = audio + noise
    return noisy_audio

from torchvision.transforms import Compose

def augment_audio(audio):
    transform_audio = Compose([
        Lambda(lambda x: random_time_shift(x))
    ])

    augmented_audio = transform_audio(audio)

    return augmented_audio

shift_audio = augment_audio(screaming_waveform)
print(shift_audio)

import torch

def augment_audio(audio):
    """
    Augments the audio waveform by applying a shift.
    Placeholder for actual augmentation logic.
    """
    if audio is None:
        raise ValueError("Input audio is None! Ensure the input is valid.")
    
    # Example: Shift the waveform by a fixed number of samples (e.g., 1000)
    shift_amount = 1000
    return torch.roll(audio, shifts=shift_amount, dims=-1)

# Validate input `screaming_waveform`
# Replace this with the actual tensor used in your other notebook
screaming_waveform = torch.randn(2, 441000)  # Simulating a stereo waveform of 10 seconds at 44.1 kHz
print(f"Input waveform shape: {screaming_waveform.shape}")

# Apply augmentation
try:
    shift_audio = augment_audio(screaming_waveform)
    print(f"Shifted audio shape: {shift_audio.shape}")
except ValueError as e:
    print(f"Error: {e}")
except AttributeError as e:
    print(f"Unexpected error: {e}")


show_waveform(shift_audio, screaming_sample_rate, 'Test')

def create_shift_images(train_loader, label_dir, amplitude_threshold=0.01, shift_time=0):
    for i, data in enumerate(train_loader):
        waveform = data['waveform']

        # Generate shifting audio
        waveform = random_time_shift(waveform)

        # Check if the waveform has sufficient amplitude
        if torch.max(torch.abs(waveform)) > amplitude_threshold:
            # Create transformed waveforms
            spectrogram_tensor = torchaudio.transforms.MelSpectrogram(
                sample_rate=int(data['sample_rate']),
                n_mels=64,
                n_fft=1024,
            )(waveform)
            if spectrogram_tensor[0].log2().isnan().any() or spectrogram_tensor[0].log2().isinf().any():
                continue

            plt.imsave(f'Data/Images/{label_dir}/audio_img{i}_shift{shift_time}.png', spectrogram_tensor[0].log2()[0, :, :].numpy(), cmap='viridis')
        else:
            print(f'Skipping blank waveform {i} in {label_dir}')

    return 'Done!'

# Call the function three times with different shift_time values using list comprehension
[create_shift_images(train_loader_scream, 'scream', shift_time=i) for i in range(5)]

import os

def count_files_in_directory(directory):
    return len([name for name in os.listdir(directory) if os.path.isfile(os.path.join(directory, name))])

directory_path = '/kaggle/working/Data/Images/not'
file_count = count_files_in_directory(directory_path)
print(f"Number of files in '{directory_path}': {file_count}")

directory_path = '/kaggle/working/Data/Images/scream'
file_count = count_files_in_directory(directory_path)
print(f"Number of files in '{directory_path}': {file_count}")

from torch.utils.data import DataLoader
import torch
import torch.nn as nn
from torchvision import datasets, transforms

data_path = '/kaggle/working/Data/Images' 

scream_dataset = datasets.ImageFolder(
    root=data_path,
    transform=transforms.Compose([transforms.Resize((64,862)),
                                  transforms.ToTensor()])
)

len(scream_dataset)

class_map=scream_dataset.class_to_idx

print("\nClass category and index of the images: {}\n".format(class_map))

#split data to test and train
train_size = int(0.8 * len(scream_dataset)) # We use 80% as train
test_size = len(scream_dataset) - train_size
scream_train_dataset, scream_test_dataset = torch.utils.data.random_split(scream_dataset, [train_size, test_size])

print("Training size:", len(scream_train_dataset))
print("Testing size:",len(scream_test_dataset))

from collections import Counter

# labels in training set
train_classes = [label for _, label in scream_train_dataset]
Counter(train_classes)

train_dataloader = torch.utils.data.DataLoader(
    scream_train_dataset,
    batch_size=64,
    num_workers=2,
    shuffle=True
)

test_dataloader = torch.utils.data.DataLoader(
    scream_test_dataset,
    batch_size=64,
    num_workers=2,
    shuffle=True
)

td = train_dataloader.dataset[0][0]
td.shape

import os

import torch
import torch.nn as nn
from torchvision.models import resnet34

# Initialize model without downloading weights
model = resnet34(weights=None)  # or pretrained=False for older versions

# Modify the model as needed
model.fc = nn.Linear(512, 2)
model.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3, bias=False)

# Move to device
device = 'cuda' if torch.cuda.is_available() else 'cpu'
model = model.to(device)
print('model is prepared')

# cost function used to determine best parameters
cost = torch.nn.CrossEntropyLoss()

# used to create optimal parameters
learning_rate = 0.001
optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate, weight_decay=0.0001)

def train(dataloader, model, loss, optimizer):
    model.train()
    size = len(dataloader.dataset)
    correct = 0  # Counter for correct predictions
    total = 0  # Counter for total examples

    for batch, (X, Y) in enumerate(dataloader):

        X, Y = X.to(device), Y.to(device)
        optimizer.zero_grad()
        pred = model(X)
        loss = cost(pred, Y)
        loss.backward()
        optimizer.step()
        # Compute accuracy
        _, predicted = pred.max(1)
        total += Y.size(0)
        correct += predicted.eq(Y).sum().item()

        if batch % 10 == 0:
            loss, current = loss.item(), batch * len(X)
            print(f'loss: {loss:>7f}  [{current:>5d}/{size:>5d}]  Train Accuracy: {(100 * correct / total):.2f}%')


# Create the validation/test function

def test(dataloader, model):
    size = len(dataloader.dataset)
    model.eval()
    test_loss, correct = 0, 0
    with torch.no_grad():
        for batch, (X, Y) in enumerate(dataloader):
            X, Y = X.to(device), Y.to(device)
            pred = model(X)

            test_loss += cost(pred, Y).item()
            correct += (pred.argmax(1)==Y).type(torch.float).sum().item()

    test_loss /= size
    correct /= size

    print(f'\nTest Error:\nacc: {(100*correct):>0.1f}%, avg loss: {test_loss:>8f}\n')
    return test_loss

early_stopping_patience = 10  # Number of epochs to wait before early stopping
best_loss = torch.inf
wait = 0  # Counter for patience
epochs = 50

best_model_weights = None

# Training loop
for t in range(epochs):
    print(f'Epoch {t + 1}\n-------------------------------')
    train(train_dataloader, model, cost, optimizer)
    test_loss = test(test_dataloader, model)

    # Check if the test loss has improved
    if test_loss < best_loss:
        best_loss = test_loss
        wait = 0  # Reset patience

        # Save the best model weights
        best_model_weights = model.state_dict()
    else:
        wait += 1

    if wait >= early_stopping_patience:
        print("Early stopping triggered. No improvement in test loss for {} epochs.".format(early_stopping_patience))
        break  # Stop training

# Restore the best model weights
if best_model_weights is not None:
    model.load_state_dict(best_model_weights)

print('Done!')

import torch
from datetime import datetime

# Get the current timestamp in the desired format
timestamp = datetime.now().strftime("%Y-%m-%d--%H-%M-%S")

# Define the file name with the timestamp
file_name = f"Resnet34_Model_{timestamp}.pt"

# Save the entire model (including architecture and weights)
torch.save(model, file_name)

# Print the saved file name
print(f"Model saved as {file_name}")

import os

# Directory path
directory = "/kaggle/working/transform-and-pre-progress-for-audio/Data/TestImages"

# Check if the directory already exists
if not os.path.exists(directory):
    # Create the directory
    os.makedirs(directory)
    print(f"Directory '{directory}' created")
else:
    print(f"Directory '{directory}' already exists")

    # Folder names to create
folders = ["Screaming", "NotScreaming"]
# Create each folder
for folder in folders:
    directory_path = os.path.join(directory, folder)

    # Check if the directory already exists
    if not os.path.exists(directory_path):
        # Create the directory
        os.makedirs(directory_path)
        print(f"Directory '{directory_path}' created")
    else:
        print(f"Directory '{directory_path}' already exists")

import os
import torchaudio
from torchvision import transforms
from PIL import Image
import matplotlib.pyplot as plt

def pad_waveform(waveform, target_length):
    num_channels, current_length = waveform.shape

    if current_length < target_length:
        # Calculate the amount of padding needed
        padding = target_length - current_length
        # Pad the waveform with zeros on the right side
        waveform = torch.nn.functional.pad(waveform, (0, padding))

    return waveform
# Define a function to transform audio data into images
def transform_data_to_image(audio, sample_rate, label, i):
    # Pad waveform to a consistent length of 44100 samples
    audio = pad_waveform(audio, 441000)

    spectrogram_tensor = torchaudio.transforms.MelSpectrogram(sample_rate=sample_rate, n_mels=64, n_fft=1024)(audio)[0] + 1e-10

    # Save the spectrogram as an image
    image_path = f'/kaggle/working/transform-and-pre-progress-for-audio/Data/TestImages/{label}/audio_img{i}.png'

    plt.imsave(image_path, spectrogram_tensor.log2().numpy(), cmap='viridis')
    return image_path

# Define the image transformation pipeline
transform = transforms.Compose([
    transforms.Resize((64, 862)),
    transforms.ToTensor(),
    transforms.Lambda(lambda x: x[:3, :, :])
])

import pandas as pd

# Define the folder containing WAV files
folder_path = '/kaggle/input/ml-review-2-2/positive'
label = 'Screaming'  # Label for the images

# Create an empty list to store data
predictions_data = []

# Iterate through WAV files in the folder
for i, filename in enumerate(os.listdir(folder_path)):
    if filename.endswith('.wav'):
        # Load the audio
        audio, sample_rate = torchaudio.load(os.path.join(folder_path, filename))

        # Transform audio to an image and save it
        image_path = transform_data_to_image(audio, sample_rate, label, i)

        # Load the saved image and apply transformations
        image = Image.open(image_path)
        image = transform(image).unsqueeze(0)  # Add batch dimension

        # Make predictions using the model
        model.eval()
        with torch.no_grad():
            outputs = model(image.to(device))

        predict = outputs.argmax(dim=1).cpu().detach().numpy().ravel()[0]

        # Store the filename and prediction in the DataFrame
        predictions_data.append({'Filename': filename, 'Prediction': predict})

# Create a DataFrame from the list of data
scream_predictions_df = pd.DataFrame(predictions_data)

# Display the DataFrame with predictions
scream_predictions_df

scream_predictions_df['Prediction'].value_counts()

# Define the folder containing WAV files
folder_path = '/kaggle/input/ml-review-2-2/negative'  
label = 'NotScreaming'  # Label for the images
import pandas as pd

# Create an empty list to store data
predictions_data = []

# Iterate through WAV files in the folder
for i, filename in enumerate(os.listdir(folder_path)):
    if filename.endswith('.wav'):
        # Load the audio
        audio, sample_rate = torchaudio.load(os.path.join(folder_path, filename))

        # Transform audio to an image and save it
        image_path = transform_data_to_image(audio, sample_rate, label, i)

        # Load the saved image and apply transformations
        image = Image.open(image_path)
        image = transform(image).unsqueeze(0)  # Add batch dimension
        # Make predictions using the model
        model.eval()
        with torch.no_grad():
            outputs = model(image.to(device))

        predict = outputs.argmax(dim=1).cpu().detach().numpy().ravel()[0]

        # Store the filename and prediction in the DataFrame
        predictions_data.append({'Filename': filename, 'Prediction': predict})

# Create a DataFrame from the list of data
not_scream_predictions_df = pd.DataFrame(predictions_data)

# Display the DataFrame with predictions
not_scream_predictions_df

not_scream_predictions_df['Prediction'].value_counts()

import os

def transform_data_to_image(audio, sample_rate, label, i):
    # Pad waveform to a consistent length of 44100 samples
    audio = pad_waveform(audio, 441000)

    # Generate Mel-spectrogram
    spectrogram_tensor = torchaudio.transforms.MelSpectrogram(sample_rate=sample_rate, n_mels=64, n_fft=1024)(audio)[0] + 1e-10

    # Define the image path
    directory = f'/kaggle/working/transform-and-pre-progress-for-audio/Data/TestImages/{label}'
    os.makedirs(directory, exist_ok=True)  # Create the directory if it doesn't exist
    image_path = f'{directory}/audio_img{i}.png'

    # Save the spectrogram as an image
    plt.imsave(image_path, spectrogram_tensor.log2().numpy(), cmap='viridis')
    return image_path


# Define the folder containing WAV files
folder_path = '/kaggle/input/audio-dataset-of-scream-and-non-scream/Converted_Separately/non_scream'  
label = 'No1Screaming'  # Label for the images
import pandas as pd

# Create an empty list to store data
predictions_data = []

# Iterate through WAV files in the folder
for i, filename in enumerate(os.listdir(folder_path)):
    if filename.endswith('.wav'):
        # Load the audio
        audio, sample_rate = torchaudio.load(os.path.join(folder_path, filename))

        # Transform audio to an image and save it
        image_path = transform_data_to_image(audio, sample_rate, label, i)

        # Load the saved image and apply transformations
        image = Image.open(image_path)
        image = transform(image).unsqueeze(0)  # Add batch dimension
        # Make predictions using the model
        model.eval()
        with torch.no_grad():
            outputs = model(image.to(device))

        predict = outputs.argmax(dim=1).cpu().detach().numpy().ravel()[0]

        # Store the filename and prediction in the DataFrame
        predictions_data.append({'Filename': filename, 'Prediction': predict})

# Create a DataFrame from the list of data
not_scream_predictions_df2 = pd.DataFrame(predictions_data)

# Display the DataFrame with predictions
not_scream_predictions_df2

not_scream_predictions_df2['Prediction'].value_counts()

# Define the folder containing WAV files
folder_path = '/kaggle/input/audio-dataset-of-scream-and-non-scream/Converted_Separately/scream'  
label = 'No1Screaming'  # Label for the images
import pandas as pd

# Create an empty list to store data
predictions_data = []

# Iterate through WAV files in the folder
for i, filename in enumerate(os.listdir(folder_path)):
    if filename.endswith('.wav'):
        # Load the audio
        audio, sample_rate = torchaudio.load(os.path.join(folder_path, filename))

        # Transform audio to an image and save it
        image_path = transform_data_to_image(audio, sample_rate, label, i)

        # Load the saved image and apply transformations
        image = Image.open(image_path)
        image = transform(image).unsqueeze(0)  # Add batch dimension
        # Make predictions using the model
        model.eval()
        with torch.no_grad():
            outputs = model(image.to(device))

        predict = outputs.argmax(dim=1).cpu().detach().numpy().ravel()[0]

        # Store the filename and prediction in the DataFrame
        predictions_data.append({'Filename': filename, 'Prediction': predict})

# Create a DataFrame from the list of data
scream_predictions_df2 = pd.DataFrame(predictions_data)

# Display the DataFrame with predictions
scream_predictions_df2

scream_predictions_df2['Prediction'].value_counts()