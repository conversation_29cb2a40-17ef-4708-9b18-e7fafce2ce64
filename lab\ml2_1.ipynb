{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "XB-lgOT2m8aB"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "RATDxuyqm8aB", "outputId": "a7d056c3-9113-4256-bfa9-1a9b8f6b8b21"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Year  Placement\n", "0   2000         50\n", "1   2001         54\n", "2   2002         62\n", "3   2003         78\n", "4   2004         65\n", "5   2005         69\n", "6   2006         72\n", "7   2007         77\n", "8   2008         85\n", "9   2009         98\n", "10  2010         75\n", "11  2011         80\n", "12  2012         82\n", "13  2013         85\n", "14  2014         88\n", "15  2015         75\n", "16  2016         90\n", "17  2017         93\n", "18  2018         95\n", "19  2019         98\n", "    Year  Placement\n", "0   2000         50\n", "1   2001         50\n", "2   2002         50\n", "3   2003         50\n", "4   2004         50\n", "5   2005         50\n", "6   2006         50\n", "7   2007         50\n", "8   2008         50\n", "9   2009         50\n", "10  2010         50\n", "11  2011         50\n", "12  2012         50\n", "13  2013         50\n", "14  2014         50\n", "15  2015         50\n", "16  2016         50\n", "17  2017         50\n", "18  2018         50\n", "19  2019         50\n"]}], "source": ["datafile = pd.DataFrame()\n", "datafile[0] = np.arange(2000,2020)\n", "datafile[1] = [50,54,62,78,65,69,72,77,85,98,75,80,82,85,88,75,90,93,95,98]\n", "datafile1 = pd.DataFrame()\n", "datafile1[0] = np.arange(2000,2020)\n", "datafile1[1] = [50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50]\n", "datafile.columns =[\"Year\",\"Placement\"]\n", "print(datafile)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Hq7YXfB3m8aC", "outputId": "f988df22-5520-4bdf-d731-a36f5aeae84a"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No handles with labels found to put in legend.\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYUAAAEWCAYAAACJ0YulAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAALEgAACxIB0t1+/AAAADh0RVh0U29mdHdhcmUAbWF0cGxvdGxpYiB2ZXJzaW9uMy4xLjMsIGh0dHA6Ly9tYXRwbG90bGliLm9yZy+AADFEAAAcXElEQVR4nO3dfZRcVZnv8e+PJEgTgp2EhJt0eB1zeZEAgR4GZAYdUBCuSswaFJaMAXHApeP4cs2s5I53OVyWE5DRUa/3qrmChBlehwuBq0JkAuqowNgxQIAYAwGS7sSkeQkECBjwuX+cXZVK6Jeq7qo69fL7rFWrqs45tc/Tp0/3U2fvffZWRGBmZgawR94BmJlZ43BSMDOzIicFMzMrclIwM7MiJwUzMytyUjAzsyInBWt6kn4i6eN5x9EoJIWkt+UdhzUnJwVrCpKekrRd0kuSNkv6vqR98o6r2iS9S1JvnfZ1gaSf12Nf1jycFKyZvD8i9gGOA/4Y+GLO8Zi1HCcFazoR0QfcCRy1+zpJfyTpHknPSnpG0nWSOkvWHyDpVkn9aZtvlaz7mKTVkp6XtEzSQSXrQtInJa2VtE3SZWlf90l6UdLNkvYs2f59kh6UtFXSLyUdXbLuKUlfkPSwpBck3SRpL0nj0881PV0RvSRp+gA/4zWSviPp7hTLT0tj3W3bt0q6Nv28T0v6oqQ9JB0BfAc4Ke1na6W/B2tNTgrWdCQdAJwFrBxoNbAImA4cARwA/H363BjgB8DTwMFAF3BjWjcH+G/AXGAK8O/ADbuV/V7geOBE4G+BxcBH0j6OAs5LZR0HXA1cAkwGvgvcIektJWV9KJV3CHA0cEFEvAycCWyMiH3SY+Mgh+EjwGXAfsCDwHWDbPc/gbcChwLvBD4KXBgRq4FPAPel/XQO8nlrM04K1kyWpm+0Pwd+CvzD7htExOMRcXdEvBYR/cDXyP4ZApxAlizmR8TLEfFqRBTq1C8BFkXE6oh4PZV97G7fwK+IiBcj4lHgEeDHEbEuIl4g+4Y/O233V8B3I+KBiHgjIpYAr5Elk4JvRsTGiHgO+H/AsRUeix9GxM8i4jXg78i+8R9QukFKgh8GFkbEtoh4Cvgq8JcV7svaiJOCNZM5EdEZEQdFxCcjYvvuG0iaKulGSX2SXgT+hezbNGTf6J9O//R3dxDwjVTdsxV4juyqo6tkm80lr7cP8L7Q8H0Q8F8LZaXyDiBLSAW/K3n9Sslny7Wh8CIiXkrx7l7VtB+wJ9mVUcHT7Pozme3CScFazSIggKMjYl/gfLJ/7pD9Iz1Q0tgBPrcBuCQlncKjIyJ+OYIYNgBf3q2svSNi9+qogZQ7bHHxqiD1wpoE7F7V9AywgyxJFRwI9FW4L2sjTgrWaiYALwFbJXUB80vW/QewCbhc0vjUuHtyWvcdYKGkt0OxgfacEcbwf4BPSPoTZcZL+i+SJpTx2c3AZElvHWa7syT9aWrcvgx4ICI2lG4QEW8ANwNfljQhVYV9nuzqqbCvGaUN5GZOCtZqLiXrsvoC8EPg1sKK9E/y/cDbgPVAL1mdOxFxG3AFcGOqdnqErNG3YhHRQ9au8C3geeBx4IIyP/sbsgbudanq6U29j5LrgS+RVRsdT9bwPJBPAy8D68jaYq4nawQHuAd4FPidpGfKic9anzzJjllzkXQN0BsRvk/Dqs5XCmZmVlSzpCDpaklbJD1SsmxSuuFmbXqemJZL0jclPZ5u6DmuVnGZmdnganmlcA3ZzTmlFgDLI2ImsDy9h6zudmZ6XAx8u4ZxmTW1iLjAVUdWKzVLChHxM7JGsFJnA0vS6yXAnJLl10bmfqBT0rRaxWZmZgMbqL92Le0fEZsAImKTpKlpeRclN+OQ9QrpIus+uAtJF5NdTTB+/PjjDz/88NpGbGbWYlasWPFMREwZaF29k8JgNMCyAbtFRcRisjFn6O7ujp6enlrGZWbWciQ9Pdi6evc+2lyoFkrPW9LyXkru0ARm8Oa7M83MrMbqnRTuAOal1/OA20uWfzT1QjoReKFQzWRmZvVTs+ojSTcA7wL2UzaT1JeAy4GbJV1EdkdpYRiBH5ENhfw42eBgF9YqLjMzG1zNkkJEnDfIqtMG2DaAT9UqFrNyLV3Zx5XL1rBx63amd3Yw/4zDmDPbg4pa/gY7N3fs2EFvby+vvvrqmz6z1157MWPGDMaNG1f2fhqlodksd0tX9rHw1lVs3/EGAH1bt7Pw1lUATgyWq6HOzWM6f8+ECRM4+OCDkXb22YkInn32WXp7eznkkEPK3peHuTBLrly2pvhHV7B9xxtcuWxNThGZZYY6N1999VUmT568S0IAkMTkyZMHvIIYipOCWbJx65vm7BlyuVm9DHdu7p4QCgZbPhQnBbNkemdHRcvN6qWe56aTglky/4zD6Bg3ZpdlHePGMP+Mw3KKyCxTz3PTScEsmTO7i0VzZ9HV2ZFNztzZwaK5s9zIbLkb7twcbF6ckcyX09ST7HiYCzNrd08++SQTJkx4U2NzoffRtm3b3tT7SNKKiOgeqDx3STUza2IzZsygt7eX/v7+N60r3KdQCScFM7MmNm7cuIruQxiO2xTMzKzIScHMzIpcfWRmVmPNNKaWk4KZWQ0125harj4yM6uhZhtTy0nBzKyGmm1MLScFM7MaarYxtZwUzMxqqNnG1HJDs5lZDRUak937yMzMgCwxNGoS2J2TgpnZMJrpPoPRclIwMxtCs91nMFpuaDYzG0Kz3WcwWk4KZmZDaLb7DEbLScHMbAjNdp/BaDkpmJkNodnuMxgtNzSbmQ2h2e4zGC0nBTOzYTTTfQaj5eojMzMrclIwM7MiJwUzMytyUjAzs6JckoKkz0h6RNKjkj6blk2SdLektel5Yh6xmVlrWbqyj5Mvv4dDFvyQky+/h6Ur+/IOqaHVPSlIOgr4K+AE4BjgfZJmAguA5RExE1ie3puZjVhh3KK+rdsJdo5b5MQwuDyuFI4A7o+IVyLideCnwAeBs4ElaZslwJwcYjOzFtJu4xZVQx5J4RHgFEmTJe0NnAUcAOwfEZsA0vPUgT4s6WJJPZJ6+vv76xa0mTWfdhu3qBrqnhQiYjVwBXA3cBfwEPB6BZ9fHBHdEdE9ZcqUGkVpZq2g3cYtqoZcGpoj4qqIOC4iTgGeA9YCmyVNA0jPW/KIzcxaR7uNW1QNefU+mpqeDwTmAjcAdwDz0ibzgNvziM3MWsec2V0smjuLrs4OBHR1drBo7qy2GbJiJBQR9d+p9O/AZGAH8PmIWC5pMnAzcCCwHjgnIp4bqpzu7u7o6empebxmZq1E0oqI6B5oXS4D4kXEnw2w7FngtBzCMbMaaKd5jVuJR0k1s6prt3mNW4mHuTCzqvP9Ac3LScHMqs73BzQvJwUzqzrfH9C8nBTMrOp8f0DzckOzmVVdu81r3EqcFMysJtppXuNW4uojMzMrclIwM7MiJwUzMytyUjAzsyI3NFvL8Fg71eXj2Z6cFKwleKyd6vLxbF+uPrKW4LF2qsvHs305KVhL8Fg71eXj2b6cFKwleKyd6vLxbF9OCtYSPNZOdfl4ti83NFtL8Fg71eXj2b5ymaO5WjxHs5lZ5Yaao9nVR2ZmVuSkYGZmRU4KZmZW5KRgZmZF7n1k1oI8bpGNlJOCWYvxuEU2Gq4+MmsxHrfIRsNJwazFeNwiGw0nBbMW43GLbDScFMxajMctstFwQ7NZi/G4RTYauSQFSZ8DPg4EsAq4EJgG3AhMAn4N/GVE/D6P+MzyVI3upHNmdzkJ2IjUvfpIUhfwN0B3RBwFjAHOBa4A/ikiZgLPAxfVOzazvBW6k/Zt3U6wszvp0pV9eYdmbSKvNoWxQIekscDewCbgVOCWtH4JMCen2Mxy4+6klre6J4WI6AP+EVhPlgxeAFYAWyPi9bRZLzDgta+kiyX1SOrp7++vR8hmdePupJa3PKqPJgJnA4cA04HxwJkDbDrgRA8RsTgiuiOie8qUKbUL1CwH7k5qecuj+ujdwJMR0R8RO4BbgXcAnak6CWAGsDGH2Mxy5e6klrc8ksJ64ERJe0sScBrwGHAv8Bdpm3nA7TnEZparObO7WDR3Fl2dHQjo6uxg0dxZ7klkdZPLdJySLgU+DLwOrCTrntrFzi6pK4HzI+K1ocrxdJxmZpUbajrOXO5TiIgvAV/abfE64IQcwsmNhzc2s0ZTVvWRpHPKWWblc390M2tE5bYpLCxzmZXJ/dHNrBENWX0k6UzgLKBL0jdLVu1L1h5gI+T+6GbWiIZrU9gI9AAfILvBrGAb8LlaBdUOpnd20DdAAmjX/uhuXzFrDEMmhYh4CHhI0vXpngKrkvlnHLbLlInQvv3RPX2kWeMot03hBEl3S/qtpHWSnpS0rqaRtTj3R9/J7StmjaPcLqlXkVUXrQDeGGZbK5OHN864fcWscZSbFF6IiDtrGom1LbevmDWOcquP7pV0paSTJB1XeNQ0MmsbHu/HrHGUe6XwJ+m59LboIJsDwWxUPH2kWeMoKylExJ/XOhBrb25fMWsMZSUFSfsD/wBMj4gzJR0JnBQRV9U0OrMm4/stqsvHs/7KbVO4BlhGNikOwG+Bz9YiILNm5fGsqsvHMx/lJoX9IuJm4A8AadpMd001K+H7LarLxzMf5SaFlyVNJk2RKelEsrmVzSzx/RbV5eOZj3KTwueBO4A/kvQL4Frg0zWLyqwJeX7l6vLxzEdZSSEifg28k2wu5UuAt0fEw7UMzKzZ+H6L6vLxzEe5vY/GkA2hfXD6zOmSiIiv1TA2s6bi+y2qy8czH2XN0SzpR8CrwCpSYzNARFxau9CG5zmazcwqV405mmdExNFVjMnMzBpQuQ3Nd0o6vaaRmJlZ7sq9UrgfuE3SHsAOQEBExL41i8zMzOqu3KTwVeAkYFWU0whhZmZNqdyksBZ4xAnBBuMxasxaQ7lJYRPwE0l3Aq8VFrpLqoHnWDZrJeU2ND8JLAf2BCaUPMw8Ro1ZCyl3PoVLASSNj4iXaxuSNRuPUWPWOsq6UkjTcD4GrE7vj5H0v2samTUNj1Fj1jrKrT76OnAG8CxARDwEnFKroKy5eIwas9ZRbkMzEbFBUukiz6dggMeoMWsl5SaFDZLeAYSkPYG/IVUlmYHnWDZrFeVWH30C+BTQBfQCx6b3FZN0mKQHSx4vSvqspEmS7pa0Nj1PHEn5ZmY2cuXOp/BMRHwkIvaPiKkRcX5EPDuSHUbEmog4NiKOBY4HXgFuAxYAyyNiJln31wUjKd/MzEau3N5HSyR1lryfKOnqKuz/NOCJiHgaOBtYkpYvAeZUoXwzM6tAudVHR0fE1sKbiHgemF2F/Z8L3JBe7x8Rm1L5m4CpA31A0sWSeiT19Pf3VyEEMzMrKLeheQ9JE1MyQNKkCj47oNRg/QFgYSWfi4jFwGLIJtkZTQyW8bhFZlZQySipv5R0S3p/DvDlUe77TODXEbE5vd8saVpEbJI0DdgyyvKtDB63yMxKldvQfC3wF8Bmsn/WcyPin0e57/PYWXUEcAcwL72eB9w+yvKtDB63yMxKVXLz2qOS+oG9ACQdGBHrR7JTSXsD7wEuKVl8OXCzpIuA9WRXI1ZjHrfIzEqVlRQkfYCsCmk62ZXCQWQ3r719JDuNiFeAybste5asN5LV0fTODvoGSAAet8isPZXb++gy4ETgtxFxCNk/71/ULCqrG49bZGalyk0KO9I3+T0k7RER95Ld1WxNbs7sLhbNnUVXZwcCujo7WDR3lhuZzdpUuW0KWyXtA/wMuE7SFuD12oVl9eRxi8ysoNwrhbOB7cDngLuAJ4D31yooMzPLR7kzr5XOtrZk0A3NzKypDZkUJG0DAihMpFC4g1hARMS+NYzNzMzqbMikEBET6hWImZnlb7grhb3I5lJ4G/AwcHVEuIHZzKxFDdfQvAToBlYBZ5HdwGZmZi1quIbmIyNiFoCkq4D/qH1IZmaWl+GuFHYUXrjayMys9Q13pXCMpBfTawEd6b17HzUIz4VgZtU0XO+jMUOtt3x5LgQzq7Zy72i2BuS5EMys2pwUmpjnQjCzanNSaGKDzXnguRDMbKScFJqY50Iws2orezpOazyFxmT3PjKzanFSaHKeC8HMqsnVR2ZmVuSkYGZmRU4KZmZW5KRgZmZFbmgeBY87ZGatxklhhDzukJm1IlcfjZDHHTKzVuSkMEIed8jMWpGTwgh53CEza0VOCiPkcYfMrBW5oXmEPO6QmbWiXJKCpE7ge8BRQAAfA9YANwEHA08BH4qI5/OIr1wed8jMWk1e1UffAO6KiMOBY4DVwAJgeUTMBJan92ZmVkd1TwqS9gVOAa4CiIjfR8RW4GxgSdpsCTCn3rGZmbW7PK4UDgX6ge9LWinpe5LGA/tHxCaA9Dx1oA9LulhSj6Se/v7++kVtZtYG8kgKY4HjgG9HxGzgZSqoKoqIxRHRHRHdU6ZMqVWMZmZtKY+k0Av0RsQD6f0tZElis6RpAOl5Sw6xmZm1tbonhYj4HbBBUqFD/2nAY8AdwLy0bB5we71jMzNrd3ndp/Bp4DpJewLrgAvJEtTNki4C1gPn5BSbmVnbyiUpRMSDQPcAq06rdyxmZraTh7kwM7MiJwUzMytyUjAzsyInBTMzK3JSMDOzIicFMzMrclIwM7MiJwUzMytyUjAzsyInBTMzK3JSMDOzIicFMzMrclIwM7MiJwUzMytyUjAzs6K8JtnJ3dKVfVy5bA0bt25nemcH8884jDmzu/IOy8wsV22ZFJau7GPhravYvuMNAPq2bmfhrasAnBjMrK21ZfXRlcvWFBNCwfYdb3DlsjU5RWRm1hjaMils3Lq9ouVmZu2iLZPC9M6OipabmbWLtkwK8884jI5xY3ZZ1jFuDPPPOCyniMzMGkNbNjQXGpPd+8jMbFdtmRQgSwxOAmZmu2rL6iMzMxuYk4KZmRU5KZiZWZGTgpmZFTkpmJlZkZOCmZkVOSmYmVmRk4KZmRXlkhQkPSVplaQHJfWkZZMk3S1pbXqeOFw5q/pe4OTL72Hpyr7aB21m1gbyvFL484g4NiK60/sFwPKImAksT++HVZgLwYnBzGz0Gqn66GxgSXq9BJhT7gc9F4KZWXXklRQC+LGkFZIuTsv2j4hNAOl56kAflHSxpB5JPW+88kJxuedCMDMbvbwGxDs5IjZKmgrcLek35X4wIhYDiwHeMm1mFJZ7LgQzs9HL5UohIjam5y3AbcAJwGZJ0wDS85Zyy/NcCGZm1VH3pCBpvKQJhdfA6cAjwB3AvLTZPOD2csrr6uxg0dxZHgbbzKwK8qg+2h+4TVJh/9dHxF2SfgXcLOkiYD1wznAFzep6K79YcGpNgzUzayd1TwoRsQ44ZoDlzwKn1TseMzPbqZG6pJqZWc6cFMzMrMhJwczMipwUzMysyEnBzMyKnBTMzKzIScHMzIqcFMzMrMhJwczMipwUzMysyEnBzMyKnBTMzKzIScHMzIqcFMzMrEgRMfxWDUrSNmBN3nEA+wHPOAagMeJwDDs1QhyNEAM0RhyNEAPAQRExZaAVec3RXC1rIqI77yAk9eQdRyPE0ChxOIbGiqMRYmiUOBohhuG4+sjMzIqcFMzMrKjZk8LivANIGiGORogBGiMOx7BTI8TRCDFAY8TRCDEMqakbms3MrLqa/UrBzMyqyEnBzMyKGiopSDpA0r2SVkt6VNJn0vJJku6WtDY9T0zLJembkh6X9LCk40rKmpe2XytpXh5xSDpW0n2pjIclfTiPY5HW7yupT9K38jgWad2Bkn6cynpM0sE5xPCVVMbqtI1qeCwOT7//1yR9Ybey3itpTYpxQb1jGKycPI5FWj9G0kpJP8gjBkmdkm6R9JtU3kk5xfG5VMYjkm6QtFe5cVRNRDTMA5gGHJdeTwB+CxwJfAVYkJYvAK5Ir88C7gQEnAg8kJZPAtal54np9cQc4vjPwMz0ejqwCeisZwwl5X0DuB74Vh6/k7TuJ8B70ut9gL3r/Pt4B/ALYEx63Ae8q4bHYirwx8CXgS+UlDMGeAI4FNgTeAg4ss4xDFhOvY9FSXmfT+fnD/KIAVgCfDy93pMy/06r/DvpAp4EOtL7m4ELKvl7rcajrjurODi4HXgP2V3L00p+AWvS6+8C55VsvyatPw/4bsnyXbarVxwDlPMQKUnUMwbgeOBG4AIqTApV/J0cCfw85/PiJGAF0AHsDfQAR9QqjpLt/n63P/6TgGUl7xcCC+sZw2Dl1PtYpGUzgOXAqVSQFKr4+9iX7J+x6nF+DhFHF7CB7MvsWOAHwOnViKmSR0NVH5VKVQuzgQeA/SNiE0B6npo2KxzEgt60bLDl9Y6jtJwTyL6BPFHPGCTtAXwVmF/pfqsZB9lV01ZJt6ZqgisljalnDBFxH3Av2RXbJrJ/zKsrjaGCOAZTlfNzlDEMVk7FqhDH14G/Bf4wkv1XIYZDgX7g++nc/J6k8fWOIyL6gH8E1pOdny9ExI9HEsdoNGRSkLQP8H+Bz0bEi0NtOsCyGGJ5veMolDMN+Gfgwoio6MSvQgyfBH4UERsGWF/POMYCfwZ8gezS+VCyK5e6xSDpbcARZN9Mu4BTJZ1SSQwVxlFRfHWOoSrlVOHz7wO2RMSKSj9brRjIzs3jgG9HxGzgZbLqnrrGkdoczgYOIatuHi/p/ErLGa2GSwqSxpEd2Osi4ta0eHP6x1r4B7slLe8FDij5+Axg4xDL6x0HkvYFfgh8MSLuzyGGk4C/lvQU2beQj0q6PIc4eoGVEbEuIl4HlpL9IdYzhg8C90fESxHxElm7w4nlxjCCOAYzqvOzSjEMVk7ZqhTHycAH0vl5I1mi/pc6x9AL9EZE4UrpFio4N6sYx7uBJyOiPyJ2ALeStYPVVUMlBUkCrgJWR8TXSlbdARR6EM0jq7MrLP+oMieSXW5tApYBp0uamLLv6WlZXeOQtCdwG3BtRPxrufuvZgwR8ZGIODAiDib7ln5tRFTS26Vav5NfARMlFUZmPBV4rM4xrAfeKWls+iN+J1B29dEI4hjMr4CZkg5J58i5qYy6xTBEOWWpVhwRsTAiZqTz81zgnogo69txFWP4HbBB0mFp0WmUeW5WMw6y8/NESXunMk+jgvOzaurdiDHUA/hTssvoh4EH0+MsYDJZQ9Ta9DwpbS/gf5HV068CukvK+hjweHpcmEccwPnAjpIyHgSOrfexKCnzAirvfVTN38l7UjmrgGuAPev8+xhD1gi9muyP/ms1Phb/iexb6IvA1vR637TuLLJeKk8Af1fvGAYrJ49jUVLmu6is91E1fx/HknU8eJjsKraS3orVjONS4DfAI2RVzm+p5BytxsPDXJiZWVFDVR+ZmVm+nBTMzKzIScHMzIqcFMzMrMhJwczMipwUzMqU7nv4uaQzS5Z9SNJdecZlVk3ukmpWAUlHAf9KNr7NGLI+6e+NiIrHtCopc2xkd3mb5c5JwaxCkr5CNj7OeGBbRFymbM6OT5ENevhL4K8j4g+SFpMNmdAB3BQR/yOV0Ut2I917ga9HhXe8m9XK2LwDMGtClwK/Bn4PdKerhw8C74iI11MiOJdsfoAFEfGcpLHAvZJuiYjCEAovR8TJefwAZoNxUjCrUES8LOkm4KWIeE3Su8lGfu3Jhqyhg51DY58n6SKyv7XpZPNKFJLCTfWN3Gx4TgpmI/MHdo7/L+DqiPjvpRtImgl8BjghIram0T9Lp1d8uS6RmlXAvY/MRu/fgA9J2g9A0mRJB5INPLcNeDENnXxGjjGalcVXCmajFBGrJF0K/Juyme52AJ8gG3XzMbIRL9eRzQ9t1tDc+8jMzIpcfWRmZkVOCmZmVuSkYGZmRU4KZmZW5KRgZmZFTgpmZlbkpGBmZkX/H+KvNMv7pbNjAAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(datafile['Year'],datafile['Placement'])\n", "plt.title(\"Placement plot\")\n", "plt.xlim(2000,2020)\n", "plt.ylim(45,100)\n", "plt.xticks(np.arange(2000,2020,2))\n", "plt.legend()\n", "plt.xlabel(\"Year\")\n", "plt.ylabel(\"Placement\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Mc_qITlBm8aC", "outputId": "ba76df0f-cc88-41f0-d922-26bf2f56d507"}, "outputs": [{"data": {"text/plain": ["<matplotlib.axes._subplots.AxesSubplot at 0x1a950e1f048>"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import seaborn as sb\n", "data_corr = datafile.corr()\n", "sb.heatmap(data_corr,annot=True)"]}, {"cell_type": "markdown", "metadata": {"id": "1uurWjo1m8aC"}, "source": ["### Modeling"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "026HMKhnm8aC", "outputId": "f2c2599c-90c9-43b0-c219-ba5a72d07cba"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Coefficients:  [[1.94661654]]\n", "Intercept:  [-3833.17593985]\n", "Coefficients:  [[0.]]\n", "Intercept:  [50.]\n"]}], "source": ["from sklearn import linear_model\n", "regress = linear_model.LinearRegression()\n", "\n", "train_x = np.asanyarray(datafile[['Year']])\n", "train_y = np.asanyarray(datafile[['Placement']])\n", "regress.fit (train_x,train_y)\n", "\n", "print ('Coefficients: ', regress.coef_)\n", "print ('Intercept: ',regress.intercept_)\n", "\n", "regress1 = linear_model.LinearRegression()\n", "train_x1 = np.asanyarray(datafile1[['Year']])\n", "train_y1 = np.asanyarray(datafile1[['Placement']])\n", "\n", "regress1.fit (train_x1,train_y1)\n", "\n", "print ('Coefficients: ', regress1.coef_)\n", "print ('Intercept: ',regress1.intercept_)"]}, {"cell_type": "markdown", "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "diJaDLFlm8aC"}, "source": ["#### Plot outputs"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "bzA8Jy4Mm8aC", "outputId": "e935998a-f8e3-413a-81db-d0b6978ac08e"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(datafile.Year, datafile.Placement,  color='blue',label=\"Actual\")\n", "plt.plot(train_x, regress.coef_[0][0]*train_x + regress.intercept_[0], '-r',label=\"Predicted\")\n", "plt.title(\"Placement plot\")\n", "plt.xlim(2000,2020)\n", "plt.ylim(45,100)\n", "plt.xticks(np.arange(2000,2020,2))\n", "plt.legend()\n", "plt.xlabel(\"Year\")\n", "plt.ylabel(\"Placement\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "id": "O5FPUz63m8aC", "outputId": "ec5b02ad-4e13-4e84-cdef-461a10da32f9"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.scatter(datafile1.Year, datafile1.Placement,  color='blue',label=\"Actual\")\n", "plt.plot(train_x, regress1.coef_[0][0]*train_x1 + regress1.intercept_[0], '-r',label=\"Predicted\")\n", "plt.title(\"Placement plot\")\n", "plt.xlim(2000,2020)\n", "plt.ylim(45,100)\n", "plt.xticks(np.arange(2000,2020,2))\n", "plt.legend()\n", "plt.xlabel(\"Year\")\n", "plt.ylabel(\"Placement\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6ZMYJ8Ysm8aD", "outputId": "0363aade-b31d-4ec6-a4d4-26cb90c28c1f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[50] [60.05714286]\n", "[54] [62.0037594]\n", "[62] [63.95037594]\n", "[78] [65.89699248]\n", "[65] [67.84360902]\n", "[69] [69.79022556]\n", "[72] [71.73684211]\n", "[77] [73.68345865]\n", "[85] [75.63007519]\n", "[98] [77.57669173]\n", "[75] [79.52330827]\n", "[80] [81.46992481]\n", "[82] [83.41654135]\n", "[85] [85.36315789]\n", "[88] [87.30977444]\n", "[75] [89.25639098]\n", "[90] [91.20300752]\n", "[93] [93.14962406]\n", "[95] [95.0962406]\n", "[98] [97.04285714]\n", "[[60.05714286]\n", " [62.0037594 ]\n", " [63.95037594]\n", " [65.89699248]\n", " [67.84360902]\n", " [69.79022556]\n", " [71.73684211]\n", " [73.68345865]\n", " [75.63007519]\n", " [77.57669173]\n", " [79.52330827]\n", " [81.46992481]\n", " [83.41654135]\n", " [85.36315789]\n", " [87.30977444]\n", " [89.25639098]\n", " [91.20300752]\n", " [93.14962406]\n", " [95.0962406 ]\n", " [97.04285714]]\n", "   Year  Placement  Predicted\n", "0  2000         50  60.057143\n", "1  2001         54  62.003759\n", "2  2002         62  63.950376\n", "3  2003         78  65.896992\n", "4  2004         65  67.843609\n"]}], "source": ["y_predicted = regress.predict(train_x)\n", "for i in range(0,len(train_x)):\n", "    print(train_y[i],y_predicted[i])\n", "datafile['Predicted'] = y_predicted\n", "print(y_predicted)\n", "print(datafile.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6QjB6xNVm8aD", "outputId": "8557766a-0989-4f08-be27-875a46711ea0"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[50] [50.]\n", "[[50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]\n", " [50.]]\n", "   Year  Placement  Predicted\n", "0  2000         50       50.0\n", "1  2001         50       50.0\n", "2  2002         50       50.0\n", "3  2003         50       50.0\n", "4  2004         50       50.0\n"]}], "source": ["y1_predicted = regress1.predict(train_x1)\n", "for i in range(0,len(train_x1)):\n", "    print(train_y1[i],y1_predicted[i])\n", "datafile1['Predicted'] = y1_predicted\n", "print(y1_predicted)\n", "print(datafile1.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kzXmY2QBm8aD", "outputId": "5c86a333-72b4-4531-d165-41b18f5bda50"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Absolute Error: 4.712330827067649\n", "Mean Squared Error: 53.55274436090161\n", "Root Mean Squared Error: 7.317974061234544\n"]}], "source": ["from sklearn import metrics\n", "print('Mean Absolute Error:', metrics.mean_absolute_error(train_y, y_predicted))\n", "print('Mean Squared Error:', metrics.mean_squared_error(train_y, y_predicted))\n", "print('Root Mean Squared Error:', np.sqrt(metrics.mean_squared_error(train_y, y_predicted)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vaWEJsYCm8aD", "outputId": "1be33427-480a-4903-a472-fedbc5460d85"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Absolute Error: 0.0\n", "Mean Squared Error: 0.0\n", "Root Mean Squared Error: 0.0\n"]}], "source": ["print('Mean Absolute Error:', metrics.mean_absolute_error(train_y1, y1_predicted))\n", "print('Mean Squared Error:', metrics.mean_squared_error(train_y1, y1_predicted))\n", "print('Root Mean Squared Error:', np.sqrt(metrics.mean_squared_error(train_y1, y1_predicted)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "scrolled": true, "id": "H9_ao0yAm8aD", "outputId": "2ab07127-748a-412c-b920-eaca86fd803f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean absolute error (MAE): 4.712330827067649\n", "Mean square error (MSE):  53.55274436090161\n", "R2-score: %.2f (RMSE): 0.7017349483512629\n"]}], "source": ["from sklearn.metrics import r2_score\n", "\n", "test_x = np.asanyarray(datafile[['Year']])\n", "test_y = np.asanyarray(datafile[['Placement']])\n", "test_y_predicted = regress.predict(test_x)\n", "\n", "print(\"Mean absolute error (MAE):\" , np.mean(np.absolute(test_y_predicted - test_y)))\n", "print(\"Mean square error (MSE): \" , np.mean((test_y_predicted - test_y) ** 2))\n", "print(\"R2-score: %.2f (RMSE):\" , r2_score(test_y, test_y_predicted) )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"button": false, "new_sheet": false, "run_control": {"read_only": false}, "scrolled": true, "id": "vBLf-gk3m8aD", "outputId": "03eed241-e3ea-4b13-a3e3-029516dfe47e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean absolute error (MAE): 1.814210526315901\n", "Mean square error (MSE):  5.01755639097796\n", "R2-score: %.2f (RMSE): 0.975395391053301\n"]}], "source": ["from sklearn.metrics import r2_score\n", "\n", "test_x1 = np.asanyarray(datafile1[['Year']])\n", "test_y1 = np.asanyarray(datafile1[['Placement']])\n", "test_y1_predicted = regress1.predict(test_x1)\n", "\n", "print(\"Mean absolute error (MAE):\" , np.mean(np.absolute(test_y1_predicted - test_y1)))\n", "print(\"Mean square error (MSE): \" , np.mean((test_y1_predicted - test_y1) ** 2))\n", "print(\"R2-score: %.2f (RMSE):\" , r2_score(test_y1, test_y1_predicted) )"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Vr5VCUkMm8aD", "outputId": "b6fdfdd2-e41b-4f74-98a1-d9a9f08e3501"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Year\n", "11  2011\n", "3   2003\n", "18  2018\n", "16  2016\n", "13  2013\n", "2   2002\n", "9   2009\n", "19  2019\n", "4   2004\n", "12  2012\n", "7   2007\n", "10  2010\n", "14  2014\n", "6   2006\n", "Testing\n", "    Year\n", "0   2000\n", "17  2017\n", "15  2015\n", "1   2001\n", "8   2008\n", "5   2005\n", "Coefficients:  [[1.70675575]]\n", "Intercept:  [-3349.2809948]\n"]}], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "train_x, test_x, train_y, test_y = train_test_split(datafile[['Year']], datafile[['Placement']], test_size=0.30, random_state=42)\n", "print(train_x)\n", "print(\"Testing\")\n", "print(test_x)\n", "\n", "from sklearn import linear_model\n", "regress = linear_model.LinearRegression()\n", "regress.fit (train_x,train_y)\n", "# The coefficients\n", "print ('Coefficients: ', regress.coef_)\n", "print ('Intercept: ',regress.intercept_)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "p22LCeMsm8aD", "outputId": "d001357a-fc39-49be-99c6-3fd7f014275d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["    Year\n", "0   2000\n", "17  2017\n", "15  2015\n", "1   2001\n", "8   2008\n", "5   2005\n", "    Placement\n", "0          50\n", "17         93\n", "15         75\n", "1          54\n", "8          85\n", "5          69\n", "[[64.23051225]\n", " [93.24536006]\n", " [89.83184855]\n", " [65.937268  ]\n", " [77.88455828]\n", " [72.76429102]]\n"]}], "source": ["y_predicted = regress.predict(test_x)\n", "print(test_x)\n", "print(test_y)\n", "print(y_predicted)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WxlwIWx4m8aE", "outputId": "d232637b-20b2-4759-c7eb-2005c1ea4071"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mean Absolute Error: 8.687453600594003\n", "Mean Squared Error: 104.9748628433119\n", "Root Mean Squared Error: 10.245724124887996\n"]}], "source": ["from sklearn import metrics\n", "print('Mean Absolute Error:', metrics.mean_absolute_error(test_y, y_predicted))\n", "print('Mean Squared Error:', metrics.mean_squared_error(test_y, y_predicted))\n", "print('Root Mean Squared Error:', np.sqrt(metrics.mean_squared_error(test_y, y_predicted)))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6PQvgeMTm8aE", "outputId": "c79d8f73-ff05-468f-a7dc-03d81d47dfcd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[-0.00562185  0.73731733 -1.08086996 -2.00601522  0.92745586]\n"]}], "source": ["from sklearn.model_selection import cross_val_score\n", "accuracy = cross_val_score(regress, datafile[['Year']], datafile[['Placement']], cv = 5,scoring='r2')\n", "print(accuracy)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "akPoXk_Gm8aE", "outputId": "f88d99f5-a73b-4319-8410-b5be8b9592cf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['explained_variance', 'r2', 'neg_median_absolute_error', 'neg_mean_absolute_error', 'neg_mean_squared_error', 'neg_mean_squared_log_error', 'accuracy', 'roc_auc', 'balanced_accuracy', 'average_precision', 'neg_log_loss', 'brier_score_loss', 'adjusted_rand_score', 'homogeneity_score', 'completeness_score', 'v_measure_score', 'mutual_info_score', 'adjusted_mutual_info_score', 'normalized_mutual_info_score', 'fowlkes_mallows_score', 'precision', 'precision_macro', 'precision_micro', 'precision_samples', 'precision_weighted', 'recall', 'recall_macro', 'recall_micro', 'recall_samples', 'recall_weighted', 'f1', 'f1_macro', 'f1_micro', 'f1_samples', 'f1_weighted'])\n"]}], "source": ["import sklearn\n", "print(sklearn.metrics.SCORERS.keys())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "colab": {"provenance": []}}, "nbformat": 4, "nbformat_minor": 0}