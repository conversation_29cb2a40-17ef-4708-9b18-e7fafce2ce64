{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: sounddevice in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (0.5.1)Note: you may need to restart the kernel to use updated packages.\n", "\n", "Requirement already satisfied: CFFI>=1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sounddevice) (1.17.0)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from CFFI>=1.0->sounddevice) (2.22)\n"]}], "source": ["pip install sounddevice"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5520\\**********.py:15: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  model = torch.load(file_path)\n"]}], "source": ["import torch\n", "import os\n", "\n", "# Check if the file exists\n", "file_path = 'resnet34-b627a593.pth'\n", "if not os.path.exists(file_path):\n", "    raise FileNotFoundError(f\"File not found: {file_path}\")\n", "\n", "# Check if the file is not empty\n", "if os.stat(file_path).st_size == 0:\n", "    raise ValueError(f\"File is empty: {file_path}\")\n", "\n", "# Assuming the file is a PyTorch model file\n", "# If it's corrupted, this line might still raise an error.\n", "model = torch.load(file_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tkinter as tk\n", "from tkinter import simpledialog, messagebox\n", "import sounddevice as sd\n", "import wave\n", "import threading\n", "import time\n", "\n", "\n", "class AudioRecorderApp:\n", "    def __init__(self, root):\n", "        self.root = root\n", "        self.root.title(\"Audio Recorder\")\n", "        self.recording = False\n", "        self.filename = \"output.wav\"\n", "        self.fs = 44100  # Sample rate\n", "        self.audio_data = []\n", "        self.recording_duration = 0\n", "        self.pin_timer = None\n", "        self.pin_time_remaining = 20  # Time for PIN entry\n", "        self.correct_pin = \"1104\"\n", "        self.recording_thread = None\n", "\n", "        # UI Elements\n", "        self.start_stop_button = tk.But<PERSON>(\n", "            root, text=\"Start Recording\", command=self.toggle_recording\n", "        )\n", "        self.start_stop_button.pack(pady=10)\n", "\n", "        self.status_label = tk.Label(root, text=\"Idle\", font=(\"Arial\", 14))\n", "        self.status_label.pack(pady=5)\n", "\n", "        self.timer_label = tk.Label(root, text=\"\", font=(\"Monospace\", 16))\n", "        self.timer_label.pack(pady=5)\n", "\n", "    def toggle_recording(self):\n", "        if self.recording:\n", "            self.stop_recording()\n", "        else:\n", "            self.start_recording()\n", "\n", "    def start_recording(self):\n", "        self.recording = True\n", "        self.recording_duration = 0\n", "        self.audio_data = []\n", "        self.status_label.config(text=\"Recording...\")\n", "        self.start_stop_button.config(text=\"Stop Recording\")\n", "\n", "        # Start recording in a separate thread\n", "        self.recording_thread = threading.Thread(target=self.record_audio)\n", "        self.recording_thread.start()\n", "\n", "        # Start the timer\n", "        self.update_timer()\n", "\n", "        #  noise detection\n", "        threading.Timer(40, self.simulate_noise_detection).start()\n", "\n", "    def record_audio(self):\n", "        def callback(indata, frames, time, status):\n", "            self.audio_data.append(indata.copy())\n", "\n", "        with sd.InputStream(samplerate=self.fs, channels=1, callback=callback):\n", "            while self.recording:\n", "                sd.sleep(100)\n", "\n", "    def stop_recording(self):\n", "        self.recording = False\n", "        self.status_label.config(text=\"Idle\")\n", "        self.start_stop_button.config(text=\"Start Recording\")\n", "        self.save_audio()\n", "\n", "    def save_audio(self):\n", "        with wave.open(self.filename, \"wb\") as wf:\n", "            wf.<PERSON><PERSON><PERSON><PERSON>(1)\n", "            wf.setsampwidth(2)  # 16-bit audio\n", "            wf.setframerate(self.fs)\n", "            wf.writeframes(b\"\".join(self.audio_data))\n", "\n", "    def update_timer(self):\n", "        if self.recording:\n", "            self.recording_duration += 1\n", "            self.timer_label.config(text=self.format_duration(self.recording_duration))\n", "            self.root.after(1000, self.update_timer)\n", "\n", "    @staticmethod\n", "    def format_duration(total_seconds):\n", "        minutes, seconds = divmod(total_seconds, 60)\n", "        return f\"{minutes:02}:{seconds:02}\"\n", "\n", "    def simulate_noise_detection(self):\n", "        if self.recording:\n", "            self.show_pin_dialog()\n", "\n", "    def show_pin_dialog(self):\n", "        def on_submit():\n", "            entered_pin = pin_entry.get()\n", "            if entered_pin == self.correct_pin:\n", "                messagebox.showinfo(\"Success\", \"P<PERSON> entered successfully!\")\n", "                self.pin_timer = None\n", "                pin_dialog.destroy()\n", "            else:\n", "                self.show_sos_message()\n", "                pin_dialog.destroy()\n", "\n", "        def update_pin_timer():\n", "            nonlocal time_remaining\n", "            if time_remaining > 0:\n", "                time_remaining -= 1\n", "                timer_label.config(text=f\"Time Remaining: {time_remaining} seconds\")\n", "                self.pin_timer = pin_dialog.after(1000, update_pin_timer)\n", "            else:\n", "                pin_dialog.destroy()\n", "                self.show_sos_message()\n", "\n", "        pin_dialog = tk.<PERSON>(self.root)\n", "        pin_dialog.title(\"Enter PIN\")\n", "        pin_dialog.geometry(\"300x150\")\n", "\n", "        tk.Label(pin_dialog, text=\"Enter your PIN:\").pack(pady=5)\n", "        pin_entry = tk.Entry(pin_dialog, show=\"*\")\n", "        pin_entry.pack(pady=5)\n", "\n", "        timer_label = tk.Label(pin_dialog, text=\"\")\n", "        timer_label.pack(pady=5)\n", "\n", "        submit_button = tk.<PERSON><PERSON>(pin_dialog, text=\"Submit\", command=on_submit)\n", "        submit_button.pack(pady=5)\n", "\n", "        time_remaining = 20\n", "        update_pin_timer()\n", "\n", "    def show_sos_message(self):\n", "        messagebox.showerror(\"SOS Sent\", \"An SOS message has been sent to emergency services.\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    root = tk.Tk()\n", "    app = AudioRecorderApp(root)\n", "    root.mainloop()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["model"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:52: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\S'\n", "<>:52: <PERSON><PERSON>tax<PERSON>arning: invalid escape sequence '\\S'\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5520\\*********.py:52: SyntaxWarning: invalid escape sequence '\\S'\n", "  model.load_state_dict(torch.load('D:\\SEM5\\ml-pro\\resnet34-b627a593.pth', map_location=torch.device('cpu')))\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_5520\\*********.py:52: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  model.load_state_dict(torch.load('D:\\SEM5\\ml-pro\\resnet34-b627a593.pth', map_location=torch.device('cpu')))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error loading model: [Errno 22] Invalid argument: 'D:\\\\SEM5\\\\ml-pro\\resnet34-b627a593.pth'\n", "Noise detection model not loaded\n"]}], "source": ["import tkinter as tk\n", "from tkinter import simpledialog, messagebox\n", "import sounddevice as sd\n", "import wave\n", "import threading\n", "import time\n", "import torch\n", "import torchvision.models as models\n", "import torchaudio\n", "import numpy as np\n", "\n", "\n", "class AudioRecorderApp:\n", "    def __init__(self, root):\n", "        self.root = root\n", "        self.root.title(\"Audio Recorder\")\n", "        self.recording = False\n", "        self.filename = \"output.wav\"\n", "        self.fs = 44100  # Sample rate\n", "        self.audio_data = []\n", "        self.recording_duration = 0\n", "        self.pin_timer = None\n", "        self.pin_time_remaining = 20  # Time for PIN entry\n", "        self.correct_pin = \"1104\"\n", "        self.recording_thread = None\n", "        \n", "        # Load noise detection model\n", "        self.noise_model = self.load_noise_detection_model()\n", "\n", "        # UI Elements\n", "        self.start_stop_button = tk.But<PERSON>(\n", "            root, text=\"Start Recording\", command=self.toggle_recording\n", "        )\n", "        self.start_stop_button.pack(pady=10)\n", "\n", "        self.status_label = tk.Label(root, text=\"Idle\", font=(\"Arial\", 14))\n", "        self.status_label.pack(pady=5)\n", "\n", "        self.timer_label = tk.Label(root, text=\"\", font=(\"Monospace\", 16))\n", "        self.timer_label.pack(pady=5)\n", "\n", "    def load_noise_detection_model(self):\n", "        \"\"\"\n", "        Load the ResNet34 model for noise/scream detection with pre-trained weights.\n", "        \"\"\"\n", "        try:\n", "            # Load ResNet34 architecture\n", "            model = models.resnet34()\n", "            # Adjust the final layer for binary classification\n", "            model.fc = torch.nn.Linear(model.fc.in_features, 1)  # Assuming binary output\n", "            # Load pre-trained weights\n", "            model.load_state_dict(torch.load('D:\\SEM5\\ml-pro\\resnet34-b627a593.pth', map_location=torch.device('cpu')))\n", "            model.eval()  # Set model to evaluation mode\n", "            return model\n", "        except Exception as e:\n", "            print(f\"Error loading model: {e}\")\n", "            return None\n", "\n", "    def detect_noise(self, audio_data):\n", "        \"\"\"\n", "        Detect if the audio clip contains a scream or noise using the loaded model.\n", "        \"\"\"\n", "        if self.noise_model is None:\n", "            print(\"Noise detection model not loaded\")\n", "            return False\n", "        \n", "        try:\n", "            # Preprocess audio data into a spectrogram (example using torchaudio)\n", "            waveform = torch.tensor(audio_data, dtype=torch.float32)\n", "            spectrogram = torchaudio.transforms.MelSpectrogram(sample_rate=self.fs)(waveform)\n", "            \n", "            # Reshape to fit ResNet input (1, 3, H, W)\n", "            input_tensor = torch.stack([spectrogram, spectrogram, spectrogram], dim=0).unsqueeze(0)\n", "            \n", "            # Run inference\n", "            with torch.no_grad():\n", "                prediction = self.noise_model(input_tensor)\n", "            \n", "            # Interpret model output (assuming binary classification with threshold 0.5)\n", "            return prediction.item() > 0.5\n", "        except Exception as e:\n", "            print(f\"Noise detection error: {e}\")\n", "            return False\n", "\n", "    def toggle_recording(self):\n", "        if self.recording:\n", "            self.stop_recording()\n", "        else:\n", "            self.start_recording()\n", "\n", "    def start_recording(self):\n", "        self.recording = True\n", "        self.recording_duration = 0\n", "        self.audio_data = []\n", "        self.status_label.config(text=\"Recording...\")\n", "        self.start_stop_button.config(text=\"Stop Recording\")\n", "\n", "        # Start recording in a separate thread\n", "        self.recording_thread = threading.Thread(target=self.record_audio)\n", "        self.recording_thread.start()\n", "\n", "        # Start the timer\n", "        self.update_timer()\n", "\n", "        # Check for noise after 20 seconds\n", "        threading.Timer(20, self.check_noise_detection).start()\n", "\n", "    def record_audio(self):\n", "        def callback(indata, frames, time, status):\n", "            self.audio_data.append(indata.copy())\n", "\n", "        with sd.InputStream(samplerate=self.fs, channels=1, callback=callback):\n", "            while self.recording:\n", "                sd.sleep(100)\n", "\n", "    def stop_recording(self):\n", "        self.recording = False\n", "        self.status_label.config(text=\"Idle\")\n", "        self.start_stop_button.config(text=\"Start Recording\")\n", "        self.save_audio()\n", "\n", "    def save_audio(self):\n", "        with wave.open(self.filename, \"wb\") as wf:\n", "            wf.<PERSON><PERSON><PERSON><PERSON>(1)\n", "            wf.setsampwidth(2)  # 16-bit audio\n", "            wf.setframerate(self.fs)\n", "            wf.writeframes(b\"\".join(self.audio_data))\n", "\n", "    def update_timer(self):\n", "        if self.recording:\n", "            self.recording_duration += 1\n", "            self.timer_label.config(text=self.format_duration(self.recording_duration))\n", "            self.root.after(1000, self.update_timer)\n", "\n", "    @staticmethod\n", "    def format_duration(total_seconds):\n", "        minutes, seconds = divmod(total_seconds, 60)\n", "        return f\"{minutes:02}:{seconds:02}\"\n", "\n", "    def check_noise_detection(self):\n", "        \"\"\"\n", "        Check for noise using the model.\n", "        \"\"\"\n", "        if self.recording:\n", "            # Convert accumulated audio data to numpy array\n", "            audio_array = np.concatenate(self.audio_data)\n", "            \n", "            # Run noise detection\n", "            if self.detect_noise(audio_array):\n", "                self.show_pin_dialog()\n", "\n", "    def show_pin_dialog(self):\n", "        def on_submit():\n", "            entered_pin = pin_entry.get()\n", "            if entered_pin == self.correct_pin:\n", "                messagebox.showinfo(\"Success\", \"P<PERSON> entered successfully!\")\n", "                self.pin_timer = None\n", "                pin_dialog.destroy()\n", "            else:\n", "                self.show_sos_message()\n", "                pin_dialog.destroy()\n", "\n", "        def update_pin_timer():\n", "            nonlocal time_remaining\n", "            if time_remaining > 0:\n", "                time_remaining -= 1\n", "                timer_label.config(text=f\"Time Remaining: {time_remaining} seconds\")\n", "                self.pin_timer = pin_dialog.after(1000, update_pin_timer)\n", "            else:\n", "                pin_dialog.destroy()\n", "                self.show_sos_message()\n", "\n", "        pin_dialog = tk.<PERSON>(self.root)\n", "        pin_dialog.title(\"Enter PIN\")\n", "        pin_dialog.geometry(\"300x150\")\n", "\n", "        tk.Label(pin_dialog, text=\"Enter your PIN:\").pack(pady=5)\n", "        pin_entry = tk.Entry(pin_dialog, show=\"*\")\n", "        pin_entry.pack(pady=5)\n", "\n", "        timer_label = tk.Label(pin_dialog, text=\"\")\n", "        timer_label.pack(pady=5)\n", "\n", "        submit_button = tk.<PERSON><PERSON>(pin_dialog, text=\"Submit\", command=on_submit)\n", "        submit_button.pack(pady=5)\n", "\n", "        time_remaining = 20\n", "        update_pin_timer()\n", "\n", "    def show_sos_message(self):\n", "        messagebox.showerror(\"SOS Sent\", \"An SOS message has been sent to emergency services.\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    root = tk.Tk()\n", "    app = AudioRecorderApp(root)\n", "    root.mainloop()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_27852\\**********.py:49: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  model.load_state_dict(torch.load('/mnt/data/resnet34-b627a593.pth', map_location=torch.device('cpu')))\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Error loading model: [Errno 2] No such file or directory: '/mnt/data/resnet34-b627a593.pth'\n"]}], "source": ["import tkinter as tk\n", "from tkinter import messagebox\n", "import sounddevice as sd\n", "import wave\n", "import threading\n", "import torch\n", "import torchvision.models as models\n", "import torchaudio\n", "import numpy as np\n", "\n", "\n", "class AudioRecorderApp:\n", "    def __init__(self, root):\n", "        self.root = root\n", "        self.root.title(\"Audio Recorder\")\n", "        self.recording = False\n", "        self.filename = \"output.wav\"\n", "        self.fs = 44100  # Sample rate\n", "        self.audio_data = []\n", "        self.pin_time_remaining = 20  # Time for PIN entry\n", "        self.correct_pin = \"1104\"\n", "        self.recording_thread = None\n", "        \n", "        # Load noise detection model\n", "        self.noise_model = self.load_noise_detection_model()\n", "\n", "        # UI Elements\n", "        self.start_stop_button = tk.But<PERSON>(\n", "            root, text=\"Start Recording\", command=self.toggle_recording\n", "        )\n", "        self.start_stop_button.pack(pady=10)\n", "\n", "        self.status_label = tk.Label(root, text=\"Idle\", font=(\"Arial\", 14))\n", "        self.status_label.pack(pady=5)\n", "\n", "        self.timer_label = tk.Label(root, text=\"\", font=(\"Monospace\", 16))\n", "        self.timer_label.pack(pady=5)\n", "\n", "    def load_noise_detection_model(self):\n", "        \"\"\"\n", "        Load the ResNet34 model for noise/scream detection with pre-trained weights.\n", "        \"\"\"\n", "        try:\n", "            # Load ResNet34 architecture\n", "            model = models.resnet34()\n", "            # Adjust the final layer for binary classification\n", "            model.fc = torch.nn.Linear(model.fc.in_features, 1)  # Assuming binary output\n", "            # Load pre-trained weights\n", "            model.load_state_dict(torch.load('/mnt/data/resnet34-b627a593.pth', map_location=torch.device('cpu')))\n", "            model.eval()  # Set model to evaluation mode\n", "            return model\n", "        except Exception as e:\n", "            print(f\"Error loading model: {e}\")\n", "            return None\n", "\n", "    def detect_noise(self, audio_data):\n", "        \"\"\"\n", "        Detect if the audio clip contains a scream or noise using the loaded model.\n", "        \"\"\"\n", "        if self.noise_model is None:\n", "            print(\"Noise detection model not loaded\")\n", "            return False\n", "        \n", "        try:\n", "            # Preprocess audio data into a spectrogram (example using torchaudio)\n", "            waveform = torch.tensor(audio_data, dtype=torch.float32)\n", "            spectrogram = torchaudio.transforms.MelSpectrogram(sample_rate=self.fs)(waveform)\n", "            \n", "            # Reshape to fit ResNet input (1, 3, H, W)\n", "            input_tensor = torch.stack([spectrogram, spectrogram, spectrogram], dim=0).unsqueeze(0)\n", "            \n", "            # Run inference\n", "            with torch.no_grad():\n", "                prediction = self.noise_model(input_tensor)\n", "            \n", "            # Interpret model output (assuming binary classification with threshold 0.5)\n", "            return prediction.item() > 0.5\n", "        except Exception as e:\n", "            print(f\"Noise detection error: {e}\")\n", "            return False\n", "\n", "    def toggle_recording(self):\n", "        if self.recording:\n", "            self.stop_recording()\n", "        else:\n", "            self.start_recording()\n", "\n", "    def start_recording(self):\n", "        self.recording = True\n", "        self.audio_data = []\n", "        self.status_label.config(text=\"Recording...\")\n", "        self.start_stop_button.config(text=\"Stop Recording\")\n", "\n", "        # Start recording in a separate thread\n", "        self.recording_thread = threading.Thread(target=self.record_audio)\n", "        self.recording_thread.start()\n", "\n", "        # Check for noise after 20 seconds\n", "        threading.Timer(20, self.check_noise_detection).start()\n", "\n", "    def record_audio(self):\n", "        def callback(indata, frames, time, status):\n", "            self.audio_data.append(indata.copy())\n", "\n", "        with sd.InputStream(samplerate=self.fs, channels=1, callback=callback):\n", "            while self.recording:\n", "                sd.sleep(100)\n", "\n", "    def check_noise_detection(self):\n", "        \"\"\"\n", "        Check for noise using the model.\n", "        \"\"\"\n", "        if self.recording:\n", "            # Convert accumulated audio data to numpy array\n", "            audio_array = np.concatenate(self.audio_data)\n", "            \n", "            # Run noise detection\n", "            if self.detect_noise(audio_array):\n", "                self.show_pin_dialog()\n", "\n", "    def stop_recording(self):\n", "        self.recording = False\n", "        self.status_label.config(text=\"Idle\")\n", "        self.start_stop_button.config(text=\"Start Recording\")\n", "        self.save_audio()\n", "\n", "    def save_audio(self):\n", "        with wave.open(self.filename, \"wb\") as wf:\n", "            wf.<PERSON><PERSON><PERSON><PERSON>(1)\n", "            wf.setsampwidth(2)  # 16-bit audio\n", "            wf.setframerate(self.fs)\n", "            wf.writeframes(b\"\".join(self.audio_data))\n", "\n", "    def show_pin_dialog(self):\n", "        \"\"\"\n", "        Show a PIN entry dialog when noise is detected.\n", "        \"\"\"\n", "        pin = tk.simpledialog.askstring(\"PIN Required\", \"Enter the PIN:\")\n", "        if pin == self.correct_pin:\n", "            messagebox.showinfo(\"Access Granted\", \"Correct PIN!\")\n", "        else:\n", "            messagebox.showerror(\"Access Denied\", \"Incorrect PIN.\")\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    root = tk.Tk()\n", "    app = AudioRecorderApp(root)\n", "    root.mainloop()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: tensorflow in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (2.17.0)Note: you may need to restart the kernel to use updated packages.\n", "\n", "Requirement already satisfied: sounddevice in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (0.5.1)\n", "Requirement already satisfied: numpy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (1.26.4)\n", "Requirement already satisfied: tensorflow-intel==2.17.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow) (2.17.0)\n", "Requirement already satisfied: absl-py>=1.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (2.1.0)\n", "Requirement already satisfied: astunparse>=1.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (1.6.3)\n", "Requirement already satisfied: flatbuffers>=24.3.25 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (24.3.25)\n", "Requirement already satisfied: gast!=0.5.0,!=0.5.1,!=0.5.2,>=0.2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (0.6.0)\n", "Requirement already satisfied: google-pasta>=0.1.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (0.2.0)\n", "Requirement already satisfied: h5py>=3.10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (3.12.1)\n", "Requirement already satisfied: libclang>=13.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (18.1.1)\n", "Requirement already satisfied: ml-dtypes<0.5.0,>=0.3.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (0.4.1)\n", "Requirement already satisfied: opt-einsum>=2.3.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (3.4.0)\n", "Requirement already satisfied: packaging in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (24.1)\n", "Requirement already satisfied: protobuf!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<5.0.0dev,>=3.20.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (4.25.5)\n", "Requirement already satisfied: requests<3,>=2.21.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (2.32.3)\n", "Requirement already satisfied: setuptools in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (74.1.0)\n", "Requirement already satisfied: six>=1.12.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (1.16.0)\n", "Requirement already satisfied: termcolor>=1.1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (2.5.0)\n", "Requirement already satisfied: typing-extensions>=3.6.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (4.12.2)\n", "Requirement already satisfied: wrapt>=1.11.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (1.16.0)\n", "Requirement already satisfied: grpcio<2.0,>=1.24.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (1.66.2)\n", "Requirement already satisfied: tensorboard<2.18,>=2.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (2.17.1)\n", "Requirement already satisfied: keras>=3.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorflow-intel==2.17.0->tensorflow) (3.6.0)\n", "Requirement already satisfied: CFFI>=1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from sounddevice) (1.17.0)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from CFFI>=1.0->sounddevice) (2.22)\n", "Requirement already satisfied: wheel<1.0,>=0.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from astunparse>=1.6.0->tensorflow-intel==2.17.0->tensorflow) (0.44.0)\n", "Requirement already satisfied: rich in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (13.9.2)\n", "Requirement already satisfied: namex in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (0.0.8)\n", "Requirement already satisfied: optree in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (0.13.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow-intel==2.17.0->tensorflow) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow-intel==2.17.0->tensorflow) (3.8)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow-intel==2.17.0->tensorflow) (2.2.2)\n", "Requirement already satisfied: certifi>=2017.4.17 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3,>=2.21.0->tensorflow-intel==2.17.0->tensorflow) (2024.8.30)\n", "Requirement already satisfied: markdown>=2.6.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorboard<2.18,>=2.17->tensorflow-intel==2.17.0->tensorflow) (3.7)\n", "Requirement already satisfied: tensorboard-data-server<0.8.0,>=0.7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorboard<2.18,>=2.17->tensorflow-intel==2.17.0->tensorflow) (0.7.2)\n", "Requirement already satisfied: werkzeug>=1.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tensorboard<2.18,>=2.17->tensorflow-intel==2.17.0->tensorflow) (3.1.3)\n", "Requirement already satisfied: MarkupSafe>=2.1.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from werkzeug>=1.0.1->tensorboard<2.18,>=2.17->tensorflow-intel==2.17.0->tensorflow) (2.1.5)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich->keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich->keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (2.18.0)\n", "Requirement already satisfied: mdurl~=0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from markdown-it-py>=2.2.0->rich->keras>=3.2.0->tensorflow-intel==2.17.0->tensorflow) (0.1.2)\n"]}], "source": ["pip install tensorflow sounddevice numpy"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}