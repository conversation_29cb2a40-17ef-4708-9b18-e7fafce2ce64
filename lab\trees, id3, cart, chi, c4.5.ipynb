{"cells": [{"cell_type": "markdown", "metadata": {"id": "G7qks1BXx7jf"}, "source": ["22MIA1082\n", "<PERSON><PERSON>"]}, {"cell_type": "markdown", "source": ["TREES"], "metadata": {"id": "Vqk50PIdCbWz"}}, {"cell_type": "code", "execution_count": 26, "metadata": {"id": "4htfAvMKyEHN"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import six\n", "import sys\n", "from sklearn.metrics import mean_squared_error, r2_score\n", "sys.modules['sklearn.externals.six']=six\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.tree import export_graphviz\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.externals.six import StringIO\n", "from sklearn.tree import DecisionTreeClassifier, plot_tree\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.model_selection import train_test_split\n", "from IPython.display import Image\n", "import pydotplus\n", "from sklearn.tree import DecisionTreeClassifier, export_text\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.tree import DecisionTreeRegressor\n", "from sklearn.metrics import mean_squared_error\n", "import math\n", "import graphviz\n", "from sklearn import tree\n", "from sklearn.feature_selection import SelectKBest, chi2\n", "from sklearn.tree import plot_tree\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "gjSlqkKuyGIW", "outputId": "efd9ad7a-11e9-4f62-903a-546f9c982566"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0             6      148             72             35        0  33.6   \n", "1             1       85             66             29        0  26.6   \n", "2             8      183             64              0        0  23.3   \n", "3             1       89             66             23       94  28.1   \n", "4             0      137             40             35      168  43.1   \n", "5             5      116             74              0        0  25.6   \n", "6             3       78             50             32       88  31.0   \n", "7            10      115              0              0        0  35.3   \n", "8             2      197             70             45      543  30.5   \n", "9             8      125             96              0        0   0.0   \n", "10            4      110             92              0        0  37.6   \n", "11           10      168             74              0        0  38.0   \n", "12           10      139             80              0        0  27.1   \n", "13            1      189             60             23      846  30.1   \n", "14            5      166             72             19      175  25.8   \n", "15            7      100              0              0        0  30.0   \n", "16            0      118             84             47      230  45.8   \n", "17            7      107             74              0        0  29.6   \n", "18            1      103             30             38       83  43.3   \n", "19            1      115             70             30       96  34.6   \n", "20            3      126             88             41      235  39.3   \n", "21            8       99             84              0        0  35.4   \n", "22            7      196             90              0        0  39.8   \n", "23            9      119             80             35        0  29.0   \n", "24           11      143             94             33      146  36.6   \n", "25           10      125             70             26      115  31.1   \n", "26            7      147             76              0        0  39.4   \n", "27            1       97             66             15      140  23.2   \n", "28           13      145             82             19      110  22.2   \n", "29            5      117             92              0        0  34.1   \n", "30            5      109             75             26        0  36.0   \n", "31            3      158             76             36      245  31.6   \n", "32            3       88             58             11       54  24.8   \n", "33            6       92             92              0        0  19.9   \n", "34           10      122             78             31        0  27.6   \n", "35            4      103             60             33      192  24.0   \n", "36           11      138             76              0        0  33.2   \n", "37            9      102             76             37        0  32.9   \n", "38            2       90             68             42        0  38.2   \n", "39            4      111             72             47      207  37.1   \n", "40            3      180             64             25       70  34.0   \n", "41            7      133             84              0        0  40.2   \n", "42            7      106             92             18        0  22.7   \n", "43            9      171            110             24      240  45.4   \n", "44            7      159             64              0        0  27.4   \n", "45            0      180             66             39        0  42.0   \n", "46            1      146             56              0        0  29.7   \n", "47            2       71             70             27        0  28.0   \n", "48            7      103             66             32        0  39.1   \n", "49            7      105              0              0        0   0.0   \n", "50            1      103             80             11       82  19.4   \n", "51            1      101             50             15       36  24.2   \n", "52            5       88             66             21       23  24.4   \n", "53            8      176             90             34      300  33.7   \n", "54            7      150             66             42      342  34.7   \n", "55            1       73             50             10        0  23.0   \n", "56            7      187             68             39      304  37.7   \n", "57            0      100             88             60      110  46.8   \n", "58            0      146             82              0        0  40.5   \n", "\n", "    DiabetesPedigreeFunction  Age  Outcome  \n", "0                      0.627   50        1  \n", "1                      0.351   31        0  \n", "2                      0.672   32        1  \n", "3                      0.167   21        0  \n", "4                      2.288   33        1  \n", "5                      0.201   30        0  \n", "6                      0.248   26        1  \n", "7                      0.134   29        0  \n", "8                      0.158   53        1  \n", "9                      0.232   54        1  \n", "10                     0.191   30        0  \n", "11                     0.537   34        1  \n", "12                     1.441   57        0  \n", "13                     0.398   59        1  \n", "14                     0.587   51        1  \n", "15                     0.484   32        1  \n", "16                     0.551   31        1  \n", "17                     0.254   31        1  \n", "18                     0.183   33        0  \n", "19                     0.529   32        1  \n", "20                     0.704   27        0  \n", "21                     0.388   50        0  \n", "22                     0.451   41        1  \n", "23                     0.263   29        1  \n", "24                     0.254   51        1  \n", "25                     0.205   41        1  \n", "26                     0.257   43        1  \n", "27                     0.487   22        0  \n", "28                     0.245   57        0  \n", "29                     0.337   38        0  \n", "30                     0.546   60        0  \n", "31                     0.851   28        1  \n", "32                     0.267   22        0  \n", "33                     0.188   28        0  \n", "34                     0.512   45        0  \n", "35                     0.966   33        0  \n", "36                     0.420   35        0  \n", "37                     0.665   46        1  \n", "38                     0.503   27        1  \n", "39                     1.390   56        1  \n", "40                     0.271   26        0  \n", "41                     0.696   37        0  \n", "42                     0.235   48        0  \n", "43                     0.721   54        1  \n", "44                     0.294   40        0  \n", "45                     1.893   25        1  \n", "46                     0.564   29        0  \n", "47                     0.586   22        0  \n", "48                     0.344   31        1  \n", "49                     0.305   24        0  \n", "50                     0.491   22        0  \n", "51                     0.526   26        0  \n", "52                     0.342   30        0  \n", "53                     0.467   58        1  \n", "54                     0.718   42        0  \n", "55                     0.248   21        0  \n", "56                     0.254   41        1  \n", "57                     0.962   31        0  \n", "58                     1.781   44        0  "], "text/html": ["\n", "  <div id=\"df-876365b3-16a0-4ec1-8ea5-6ca574e28de0\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "      <th>Outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>148</td>\n", "      <td>72</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>33.6</td>\n", "      <td>0.627</td>\n", "      <td>50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>66</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>26.6</td>\n", "      <td>0.351</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>183</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>23.3</td>\n", "      <td>0.672</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>66</td>\n", "      <td>23</td>\n", "      <td>94</td>\n", "      <td>28.1</td>\n", "      <td>0.167</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>137</td>\n", "      <td>40</td>\n", "      <td>35</td>\n", "      <td>168</td>\n", "      <td>43.1</td>\n", "      <td>2.288</td>\n", "      <td>33</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>116</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25.6</td>\n", "      <td>0.201</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>78</td>\n", "      <td>50</td>\n", "      <td>32</td>\n", "      <td>88</td>\n", "      <td>31.0</td>\n", "      <td>0.248</td>\n", "      <td>26</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>35.3</td>\n", "      <td>0.134</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2</td>\n", "      <td>197</td>\n", "      <td>70</td>\n", "      <td>45</td>\n", "      <td>543</td>\n", "      <td>30.5</td>\n", "      <td>0.158</td>\n", "      <td>53</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>8</td>\n", "      <td>125</td>\n", "      <td>96</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.232</td>\n", "      <td>54</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>4</td>\n", "      <td>110</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>37.6</td>\n", "      <td>0.191</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>10</td>\n", "      <td>168</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>38.0</td>\n", "      <td>0.537</td>\n", "      <td>34</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>10</td>\n", "      <td>139</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27.1</td>\n", "      <td>1.441</td>\n", "      <td>57</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>1</td>\n", "      <td>189</td>\n", "      <td>60</td>\n", "      <td>23</td>\n", "      <td>846</td>\n", "      <td>30.1</td>\n", "      <td>0.398</td>\n", "      <td>59</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>5</td>\n", "      <td>166</td>\n", "      <td>72</td>\n", "      <td>19</td>\n", "      <td>175</td>\n", "      <td>25.8</td>\n", "      <td>0.587</td>\n", "      <td>51</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>7</td>\n", "      <td>100</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>30.0</td>\n", "      <td>0.484</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0</td>\n", "      <td>118</td>\n", "      <td>84</td>\n", "      <td>47</td>\n", "      <td>230</td>\n", "      <td>45.8</td>\n", "      <td>0.551</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>7</td>\n", "      <td>107</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>29.6</td>\n", "      <td>0.254</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>1</td>\n", "      <td>103</td>\n", "      <td>30</td>\n", "      <td>38</td>\n", "      <td>83</td>\n", "      <td>43.3</td>\n", "      <td>0.183</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>1</td>\n", "      <td>115</td>\n", "      <td>70</td>\n", "      <td>30</td>\n", "      <td>96</td>\n", "      <td>34.6</td>\n", "      <td>0.529</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3</td>\n", "      <td>126</td>\n", "      <td>88</td>\n", "      <td>41</td>\n", "      <td>235</td>\n", "      <td>39.3</td>\n", "      <td>0.704</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>8</td>\n", "      <td>99</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>35.4</td>\n", "      <td>0.388</td>\n", "      <td>50</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>7</td>\n", "      <td>196</td>\n", "      <td>90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>39.8</td>\n", "      <td>0.451</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>9</td>\n", "      <td>119</td>\n", "      <td>80</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>29.0</td>\n", "      <td>0.263</td>\n", "      <td>29</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>11</td>\n", "      <td>143</td>\n", "      <td>94</td>\n", "      <td>33</td>\n", "      <td>146</td>\n", "      <td>36.6</td>\n", "      <td>0.254</td>\n", "      <td>51</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>10</td>\n", "      <td>125</td>\n", "      <td>70</td>\n", "      <td>26</td>\n", "      <td>115</td>\n", "      <td>31.1</td>\n", "      <td>0.205</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>7</td>\n", "      <td>147</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>39.4</td>\n", "      <td>0.257</td>\n", "      <td>43</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>1</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>15</td>\n", "      <td>140</td>\n", "      <td>23.2</td>\n", "      <td>0.487</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>13</td>\n", "      <td>145</td>\n", "      <td>82</td>\n", "      <td>19</td>\n", "      <td>110</td>\n", "      <td>22.2</td>\n", "      <td>0.245</td>\n", "      <td>57</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>5</td>\n", "      <td>117</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>34.1</td>\n", "      <td>0.337</td>\n", "      <td>38</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>5</td>\n", "      <td>109</td>\n", "      <td>75</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>36.0</td>\n", "      <td>0.546</td>\n", "      <td>60</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>3</td>\n", "      <td>158</td>\n", "      <td>76</td>\n", "      <td>36</td>\n", "      <td>245</td>\n", "      <td>31.6</td>\n", "      <td>0.851</td>\n", "      <td>28</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>3</td>\n", "      <td>88</td>\n", "      <td>58</td>\n", "      <td>11</td>\n", "      <td>54</td>\n", "      <td>24.8</td>\n", "      <td>0.267</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>6</td>\n", "      <td>92</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>19.9</td>\n", "      <td>0.188</td>\n", "      <td>28</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>10</td>\n", "      <td>122</td>\n", "      <td>78</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "      <td>27.6</td>\n", "      <td>0.512</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>4</td>\n", "      <td>103</td>\n", "      <td>60</td>\n", "      <td>33</td>\n", "      <td>192</td>\n", "      <td>24.0</td>\n", "      <td>0.966</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>11</td>\n", "      <td>138</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>33.2</td>\n", "      <td>0.420</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>9</td>\n", "      <td>102</td>\n", "      <td>76</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>32.9</td>\n", "      <td>0.665</td>\n", "      <td>46</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2</td>\n", "      <td>90</td>\n", "      <td>68</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "      <td>38.2</td>\n", "      <td>0.503</td>\n", "      <td>27</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>4</td>\n", "      <td>111</td>\n", "      <td>72</td>\n", "      <td>47</td>\n", "      <td>207</td>\n", "      <td>37.1</td>\n", "      <td>1.390</td>\n", "      <td>56</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>3</td>\n", "      <td>180</td>\n", "      <td>64</td>\n", "      <td>25</td>\n", "      <td>70</td>\n", "      <td>34.0</td>\n", "      <td>0.271</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>7</td>\n", "      <td>133</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.2</td>\n", "      <td>0.696</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>7</td>\n", "      <td>106</td>\n", "      <td>92</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>22.7</td>\n", "      <td>0.235</td>\n", "      <td>48</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>9</td>\n", "      <td>171</td>\n", "      <td>110</td>\n", "      <td>24</td>\n", "      <td>240</td>\n", "      <td>45.4</td>\n", "      <td>0.721</td>\n", "      <td>54</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>7</td>\n", "      <td>159</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27.4</td>\n", "      <td>0.294</td>\n", "      <td>40</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>0</td>\n", "      <td>180</td>\n", "      <td>66</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>42.0</td>\n", "      <td>1.893</td>\n", "      <td>25</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>1</td>\n", "      <td>146</td>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>29.7</td>\n", "      <td>0.564</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2</td>\n", "      <td>71</td>\n", "      <td>70</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>28.0</td>\n", "      <td>0.586</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>7</td>\n", "      <td>103</td>\n", "      <td>66</td>\n", "      <td>32</td>\n", "      <td>0</td>\n", "      <td>39.1</td>\n", "      <td>0.344</td>\n", "      <td>31</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>7</td>\n", "      <td>105</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.305</td>\n", "      <td>24</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>1</td>\n", "      <td>103</td>\n", "      <td>80</td>\n", "      <td>11</td>\n", "      <td>82</td>\n", "      <td>19.4</td>\n", "      <td>0.491</td>\n", "      <td>22</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>1</td>\n", "      <td>101</td>\n", "      <td>50</td>\n", "      <td>15</td>\n", "      <td>36</td>\n", "      <td>24.2</td>\n", "      <td>0.526</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>5</td>\n", "      <td>88</td>\n", "      <td>66</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>24.4</td>\n", "      <td>0.342</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>8</td>\n", "      <td>176</td>\n", "      <td>90</td>\n", "      <td>34</td>\n", "      <td>300</td>\n", "      <td>33.7</td>\n", "      <td>0.467</td>\n", "      <td>58</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>7</td>\n", "      <td>150</td>\n", "      <td>66</td>\n", "      <td>42</td>\n", "      <td>342</td>\n", "      <td>34.7</td>\n", "      <td>0.718</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>1</td>\n", "      <td>73</td>\n", "      <td>50</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>23.0</td>\n", "      <td>0.248</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>7</td>\n", "      <td>187</td>\n", "      <td>68</td>\n", "      <td>39</td>\n", "      <td>304</td>\n", "      <td>37.7</td>\n", "      <td>0.254</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>88</td>\n", "      <td>60</td>\n", "      <td>110</td>\n", "      <td>46.8</td>\n", "      <td>0.962</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>0</td>\n", "      <td>146</td>\n", "      <td>82</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.5</td>\n", "      <td>1.781</td>\n", "      <td>44</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-876365b3-16a0-4ec1-8ea5-6ca574e28de0')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-876365b3-16a0-4ec1-8ea5-6ca574e28de0 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-876365b3-16a0-4ec1-8ea5-6ca574e28de0');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-f3d7a962-5ce2-4e73-bc0e-c9a0ab6a8cd2\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-f3d7a962-5ce2-4e73-bc0e-c9a0ab6a8cd2')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-f3d7a962-5ce2-4e73-bc0e-c9a0ab6a8cd2 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_c16a6363-f9d6-400a-a4ce-00a16552e528\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_c16a6363-f9d6-400a-a4ce-00a16552e528 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 59,\n  \"fields\": [\n    {\n      \"column\": \"Pregnancies\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3,\n        \"min\": 0,\n        \"max\": 13,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          11,\n          7,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Glucose\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 33,\n        \"min\": 71,\n        \"max\": 197,\n        \"num_unique_values\": 50,\n        \"samples\": [\n          189,\n          106,\n          88\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BloodPressure\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21,\n        \"min\": 0,\n        \"max\": 110,\n        \"num_unique_values\": 25,\n        \"samples\": [\n          96,\n          94,\n          72\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SkinThickness\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 17,\n        \"min\": 0,\n        \"max\": 60,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          30,\n          34,\n          38\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Insulin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 151,\n        \"min\": 0,\n        \"max\": 846,\n        \"num_unique_values\": 27,\n        \"samples\": [\n          83,\n          140,\n          96\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BMI\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9.064114444358726,\n        \"min\": 0.0,\n        \"max\": 46.8,\n        \"num_unique_values\": 58,\n        \"samples\": [\n          33.6,\n          25.6,\n          27.6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"DiabetesPedigreeFunction\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.4349965268542028,\n        \"min\": 0.134,\n        \"max\": 2.288,\n        \"num_unique_values\": 56,\n        \"samples\": [\n          0.627,\n          0.201,\n          0.512\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11,\n        \"min\": 21,\n        \"max\": 60,\n        \"num_unique_values\": 33,\n        \"samples\": [\n          42,\n          41,\n          48\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Outcome\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 27}], "source": ["df=pd.read_csv('/content/diabetes_dataset .csv')\n", "df"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 363}, "id": "KwAJllaYy073", "outputId": "02d237ed-dd19-44f9-ebe7-62517822513b"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0            6      148             72             35        0  33.6   \n", "1            1       85             66             29        0  26.6   \n", "2            8      183             64              0        0  23.3   \n", "3            1       89             66             23       94  28.1   \n", "4            0      137             40             35      168  43.1   \n", "5            5      116             74              0        0  25.6   \n", "6            3       78             50             32       88  31.0   \n", "7           10      115              0              0        0  35.3   \n", "8            2      197             70             45      543  30.5   \n", "9            8      125             96              0        0   0.0   \n", "\n", "   DiabetesPedigreeFunction  Age  Outcome  \n", "0                     0.627   50        1  \n", "1                     0.351   31        0  \n", "2                     0.672   32        1  \n", "3                     0.167   21        0  \n", "4                     2.288   33        1  \n", "5                     0.201   30        0  \n", "6                     0.248   26        1  \n", "7                     0.134   29        0  \n", "8                     0.158   53        1  \n", "9                     0.232   54        1  "], "text/html": ["\n", "  <div id=\"df-3a711f95-9c09-4405-9d8d-42f8a28646fc\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "      <th>Outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>148</td>\n", "      <td>72</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>33.6</td>\n", "      <td>0.627</td>\n", "      <td>50</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>66</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>26.6</td>\n", "      <td>0.351</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>183</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>23.3</td>\n", "      <td>0.672</td>\n", "      <td>32</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>66</td>\n", "      <td>23</td>\n", "      <td>94</td>\n", "      <td>28.1</td>\n", "      <td>0.167</td>\n", "      <td>21</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>137</td>\n", "      <td>40</td>\n", "      <td>35</td>\n", "      <td>168</td>\n", "      <td>43.1</td>\n", "      <td>2.288</td>\n", "      <td>33</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>116</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25.6</td>\n", "      <td>0.201</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>78</td>\n", "      <td>50</td>\n", "      <td>32</td>\n", "      <td>88</td>\n", "      <td>31.0</td>\n", "      <td>0.248</td>\n", "      <td>26</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>35.3</td>\n", "      <td>0.134</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2</td>\n", "      <td>197</td>\n", "      <td>70</td>\n", "      <td>45</td>\n", "      <td>543</td>\n", "      <td>30.5</td>\n", "      <td>0.158</td>\n", "      <td>53</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>8</td>\n", "      <td>125</td>\n", "      <td>96</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.232</td>\n", "      <td>54</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-3a711f95-9c09-4405-9d8d-42f8a28646fc')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-3a711f95-9c09-4405-9d8d-42f8a28646fc button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-3a711f95-9c09-4405-9d8d-42f8a28646fc');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-b30ba283-861f-4e0c-a3fe-9a46d1a197f9\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-b30ba283-861f-4e0c-a3fe-9a46d1a197f9')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-b30ba283-861f-4e0c-a3fe-9a46d1a197f9 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 59,\n  \"fields\": [\n    {\n      \"column\": \"Pregnancies\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3,\n        \"min\": 0,\n        \"max\": 13,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          11,\n          7,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Glucose\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 33,\n        \"min\": 71,\n        \"max\": 197,\n        \"num_unique_values\": 50,\n        \"samples\": [\n          189,\n          106,\n          88\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BloodPressure\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21,\n        \"min\": 0,\n        \"max\": 110,\n        \"num_unique_values\": 25,\n        \"samples\": [\n          96,\n          94,\n          72\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SkinThickness\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 17,\n        \"min\": 0,\n        \"max\": 60,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          30,\n          34,\n          38\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Insulin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 151,\n        \"min\": 0,\n        \"max\": 846,\n        \"num_unique_values\": 27,\n        \"samples\": [\n          83,\n          140,\n          96\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BMI\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9.064114444358726,\n        \"min\": 0.0,\n        \"max\": 46.8,\n        \"num_unique_values\": 58,\n        \"samples\": [\n          33.6,\n          25.6,\n          27.6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"DiabetesPedigreeFunction\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.4349965268542028,\n        \"min\": 0.134,\n        \"max\": 2.288,\n        \"num_unique_values\": 56,\n        \"samples\": [\n          0.627,\n          0.201,\n          0.512\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11,\n        \"min\": 21,\n        \"max\": 60,\n        \"num_unique_values\": 33,\n        \"samples\": [\n          42,\n          41,\n          48\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Outcome\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 28}], "source": ["df.head(10)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "m5kqZY2Gzq62", "outputId": "f82e88d5-1ec2-4d9e-d797-c25bf3d9e476"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    Pregnancies  Glucose  BloodPressure  SkinThickness  Insulin   BMI  \\\n", "0             6      148             72             35        0  33.6   \n", "1             1       85             66             29        0  26.6   \n", "2             8      183             64              0        0  23.3   \n", "3             1       89             66             23       94  28.1   \n", "4             0      137             40             35      168  43.1   \n", "5             5      116             74              0        0  25.6   \n", "6             3       78             50             32       88  31.0   \n", "7            10      115              0              0        0  35.3   \n", "8             2      197             70             45      543  30.5   \n", "9             8      125             96              0        0   0.0   \n", "10            4      110             92              0        0  37.6   \n", "11           10      168             74              0        0  38.0   \n", "12           10      139             80              0        0  27.1   \n", "13            1      189             60             23      846  30.1   \n", "14            5      166             72             19      175  25.8   \n", "15            7      100              0              0        0  30.0   \n", "16            0      118             84             47      230  45.8   \n", "17            7      107             74              0        0  29.6   \n", "18            1      103             30             38       83  43.3   \n", "19            1      115             70             30       96  34.6   \n", "20            3      126             88             41      235  39.3   \n", "21            8       99             84              0        0  35.4   \n", "22            7      196             90              0        0  39.8   \n", "23            9      119             80             35        0  29.0   \n", "24           11      143             94             33      146  36.6   \n", "25           10      125             70             26      115  31.1   \n", "26            7      147             76              0        0  39.4   \n", "27            1       97             66             15      140  23.2   \n", "28           13      145             82             19      110  22.2   \n", "29            5      117             92              0        0  34.1   \n", "30            5      109             75             26        0  36.0   \n", "31            3      158             76             36      245  31.6   \n", "32            3       88             58             11       54  24.8   \n", "33            6       92             92              0        0  19.9   \n", "34           10      122             78             31        0  27.6   \n", "35            4      103             60             33      192  24.0   \n", "36           11      138             76              0        0  33.2   \n", "37            9      102             76             37        0  32.9   \n", "38            2       90             68             42        0  38.2   \n", "39            4      111             72             47      207  37.1   \n", "40            3      180             64             25       70  34.0   \n", "41            7      133             84              0        0  40.2   \n", "42            7      106             92             18        0  22.7   \n", "43            9      171            110             24      240  45.4   \n", "44            7      159             64              0        0  27.4   \n", "45            0      180             66             39        0  42.0   \n", "46            1      146             56              0        0  29.7   \n", "47            2       71             70             27        0  28.0   \n", "48            7      103             66             32        0  39.1   \n", "49            7      105              0              0        0   0.0   \n", "50            1      103             80             11       82  19.4   \n", "51            1      101             50             15       36  24.2   \n", "52            5       88             66             21       23  24.4   \n", "53            8      176             90             34      300  33.7   \n", "54            7      150             66             42      342  34.7   \n", "55            1       73             50             10        0  23.0   \n", "56            7      187             68             39      304  37.7   \n", "57            0      100             88             60      110  46.8   \n", "58            0      146             82              0        0  40.5   \n", "\n", "    DiabetesPedigreeFunction  Age  \n", "0                      0.627   50  \n", "1                      0.351   31  \n", "2                      0.672   32  \n", "3                      0.167   21  \n", "4                      2.288   33  \n", "5                      0.201   30  \n", "6                      0.248   26  \n", "7                      0.134   29  \n", "8                      0.158   53  \n", "9                      0.232   54  \n", "10                     0.191   30  \n", "11                     0.537   34  \n", "12                     1.441   57  \n", "13                     0.398   59  \n", "14                     0.587   51  \n", "15                     0.484   32  \n", "16                     0.551   31  \n", "17                     0.254   31  \n", "18                     0.183   33  \n", "19                     0.529   32  \n", "20                     0.704   27  \n", "21                     0.388   50  \n", "22                     0.451   41  \n", "23                     0.263   29  \n", "24                     0.254   51  \n", "25                     0.205   41  \n", "26                     0.257   43  \n", "27                     0.487   22  \n", "28                     0.245   57  \n", "29                     0.337   38  \n", "30                     0.546   60  \n", "31                     0.851   28  \n", "32                     0.267   22  \n", "33                     0.188   28  \n", "34                     0.512   45  \n", "35                     0.966   33  \n", "36                     0.420   35  \n", "37                     0.665   46  \n", "38                     0.503   27  \n", "39                     1.390   56  \n", "40                     0.271   26  \n", "41                     0.696   37  \n", "42                     0.235   48  \n", "43                     0.721   54  \n", "44                     0.294   40  \n", "45                     1.893   25  \n", "46                     0.564   29  \n", "47                     0.586   22  \n", "48                     0.344   31  \n", "49                     0.305   24  \n", "50                     0.491   22  \n", "51                     0.526   26  \n", "52                     0.342   30  \n", "53                     0.467   58  \n", "54                     0.718   42  \n", "55                     0.248   21  \n", "56                     0.254   41  \n", "57                     0.962   31  \n", "58                     1.781   44  "], "text/html": ["\n", "  <div id=\"df-edc46baf-f939-4682-9747-1982911fd71a\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pregnancies</th>\n", "      <th>Glucose</th>\n", "      <th>BloodPressure</th>\n", "      <th>SkinThickness</th>\n", "      <th>Insulin</th>\n", "      <th>BMI</th>\n", "      <th>DiabetesPedigreeFunction</th>\n", "      <th>Age</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>6</td>\n", "      <td>148</td>\n", "      <td>72</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>33.6</td>\n", "      <td>0.627</td>\n", "      <td>50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>85</td>\n", "      <td>66</td>\n", "      <td>29</td>\n", "      <td>0</td>\n", "      <td>26.6</td>\n", "      <td>0.351</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>183</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>23.3</td>\n", "      <td>0.672</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>89</td>\n", "      <td>66</td>\n", "      <td>23</td>\n", "      <td>94</td>\n", "      <td>28.1</td>\n", "      <td>0.167</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>137</td>\n", "      <td>40</td>\n", "      <td>35</td>\n", "      <td>168</td>\n", "      <td>43.1</td>\n", "      <td>2.288</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>116</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25.6</td>\n", "      <td>0.201</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>78</td>\n", "      <td>50</td>\n", "      <td>32</td>\n", "      <td>88</td>\n", "      <td>31.0</td>\n", "      <td>0.248</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>115</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>35.3</td>\n", "      <td>0.134</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2</td>\n", "      <td>197</td>\n", "      <td>70</td>\n", "      <td>45</td>\n", "      <td>543</td>\n", "      <td>30.5</td>\n", "      <td>0.158</td>\n", "      <td>53</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>8</td>\n", "      <td>125</td>\n", "      <td>96</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.232</td>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>4</td>\n", "      <td>110</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>37.6</td>\n", "      <td>0.191</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>10</td>\n", "      <td>168</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>38.0</td>\n", "      <td>0.537</td>\n", "      <td>34</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>10</td>\n", "      <td>139</td>\n", "      <td>80</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27.1</td>\n", "      <td>1.441</td>\n", "      <td>57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>1</td>\n", "      <td>189</td>\n", "      <td>60</td>\n", "      <td>23</td>\n", "      <td>846</td>\n", "      <td>30.1</td>\n", "      <td>0.398</td>\n", "      <td>59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>5</td>\n", "      <td>166</td>\n", "      <td>72</td>\n", "      <td>19</td>\n", "      <td>175</td>\n", "      <td>25.8</td>\n", "      <td>0.587</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>7</td>\n", "      <td>100</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>30.0</td>\n", "      <td>0.484</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0</td>\n", "      <td>118</td>\n", "      <td>84</td>\n", "      <td>47</td>\n", "      <td>230</td>\n", "      <td>45.8</td>\n", "      <td>0.551</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>7</td>\n", "      <td>107</td>\n", "      <td>74</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>29.6</td>\n", "      <td>0.254</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>1</td>\n", "      <td>103</td>\n", "      <td>30</td>\n", "      <td>38</td>\n", "      <td>83</td>\n", "      <td>43.3</td>\n", "      <td>0.183</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>1</td>\n", "      <td>115</td>\n", "      <td>70</td>\n", "      <td>30</td>\n", "      <td>96</td>\n", "      <td>34.6</td>\n", "      <td>0.529</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>3</td>\n", "      <td>126</td>\n", "      <td>88</td>\n", "      <td>41</td>\n", "      <td>235</td>\n", "      <td>39.3</td>\n", "      <td>0.704</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>8</td>\n", "      <td>99</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>35.4</td>\n", "      <td>0.388</td>\n", "      <td>50</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>7</td>\n", "      <td>196</td>\n", "      <td>90</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>39.8</td>\n", "      <td>0.451</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>9</td>\n", "      <td>119</td>\n", "      <td>80</td>\n", "      <td>35</td>\n", "      <td>0</td>\n", "      <td>29.0</td>\n", "      <td>0.263</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>11</td>\n", "      <td>143</td>\n", "      <td>94</td>\n", "      <td>33</td>\n", "      <td>146</td>\n", "      <td>36.6</td>\n", "      <td>0.254</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>10</td>\n", "      <td>125</td>\n", "      <td>70</td>\n", "      <td>26</td>\n", "      <td>115</td>\n", "      <td>31.1</td>\n", "      <td>0.205</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>7</td>\n", "      <td>147</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>39.4</td>\n", "      <td>0.257</td>\n", "      <td>43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>1</td>\n", "      <td>97</td>\n", "      <td>66</td>\n", "      <td>15</td>\n", "      <td>140</td>\n", "      <td>23.2</td>\n", "      <td>0.487</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>13</td>\n", "      <td>145</td>\n", "      <td>82</td>\n", "      <td>19</td>\n", "      <td>110</td>\n", "      <td>22.2</td>\n", "      <td>0.245</td>\n", "      <td>57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>5</td>\n", "      <td>117</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>34.1</td>\n", "      <td>0.337</td>\n", "      <td>38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>5</td>\n", "      <td>109</td>\n", "      <td>75</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>36.0</td>\n", "      <td>0.546</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>3</td>\n", "      <td>158</td>\n", "      <td>76</td>\n", "      <td>36</td>\n", "      <td>245</td>\n", "      <td>31.6</td>\n", "      <td>0.851</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>3</td>\n", "      <td>88</td>\n", "      <td>58</td>\n", "      <td>11</td>\n", "      <td>54</td>\n", "      <td>24.8</td>\n", "      <td>0.267</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>6</td>\n", "      <td>92</td>\n", "      <td>92</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>19.9</td>\n", "      <td>0.188</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>10</td>\n", "      <td>122</td>\n", "      <td>78</td>\n", "      <td>31</td>\n", "      <td>0</td>\n", "      <td>27.6</td>\n", "      <td>0.512</td>\n", "      <td>45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>4</td>\n", "      <td>103</td>\n", "      <td>60</td>\n", "      <td>33</td>\n", "      <td>192</td>\n", "      <td>24.0</td>\n", "      <td>0.966</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>11</td>\n", "      <td>138</td>\n", "      <td>76</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>33.2</td>\n", "      <td>0.420</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>9</td>\n", "      <td>102</td>\n", "      <td>76</td>\n", "      <td>37</td>\n", "      <td>0</td>\n", "      <td>32.9</td>\n", "      <td>0.665</td>\n", "      <td>46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>2</td>\n", "      <td>90</td>\n", "      <td>68</td>\n", "      <td>42</td>\n", "      <td>0</td>\n", "      <td>38.2</td>\n", "      <td>0.503</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>4</td>\n", "      <td>111</td>\n", "      <td>72</td>\n", "      <td>47</td>\n", "      <td>207</td>\n", "      <td>37.1</td>\n", "      <td>1.390</td>\n", "      <td>56</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>3</td>\n", "      <td>180</td>\n", "      <td>64</td>\n", "      <td>25</td>\n", "      <td>70</td>\n", "      <td>34.0</td>\n", "      <td>0.271</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41</th>\n", "      <td>7</td>\n", "      <td>133</td>\n", "      <td>84</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.2</td>\n", "      <td>0.696</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42</th>\n", "      <td>7</td>\n", "      <td>106</td>\n", "      <td>92</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>22.7</td>\n", "      <td>0.235</td>\n", "      <td>48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43</th>\n", "      <td>9</td>\n", "      <td>171</td>\n", "      <td>110</td>\n", "      <td>24</td>\n", "      <td>240</td>\n", "      <td>45.4</td>\n", "      <td>0.721</td>\n", "      <td>54</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>7</td>\n", "      <td>159</td>\n", "      <td>64</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>27.4</td>\n", "      <td>0.294</td>\n", "      <td>40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>0</td>\n", "      <td>180</td>\n", "      <td>66</td>\n", "      <td>39</td>\n", "      <td>0</td>\n", "      <td>42.0</td>\n", "      <td>1.893</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>1</td>\n", "      <td>146</td>\n", "      <td>56</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>29.7</td>\n", "      <td>0.564</td>\n", "      <td>29</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47</th>\n", "      <td>2</td>\n", "      <td>71</td>\n", "      <td>70</td>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>28.0</td>\n", "      <td>0.586</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48</th>\n", "      <td>7</td>\n", "      <td>103</td>\n", "      <td>66</td>\n", "      <td>32</td>\n", "      <td>0</td>\n", "      <td>39.1</td>\n", "      <td>0.344</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>7</td>\n", "      <td>105</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>0.305</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>1</td>\n", "      <td>103</td>\n", "      <td>80</td>\n", "      <td>11</td>\n", "      <td>82</td>\n", "      <td>19.4</td>\n", "      <td>0.491</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>1</td>\n", "      <td>101</td>\n", "      <td>50</td>\n", "      <td>15</td>\n", "      <td>36</td>\n", "      <td>24.2</td>\n", "      <td>0.526</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>5</td>\n", "      <td>88</td>\n", "      <td>66</td>\n", "      <td>21</td>\n", "      <td>23</td>\n", "      <td>24.4</td>\n", "      <td>0.342</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>8</td>\n", "      <td>176</td>\n", "      <td>90</td>\n", "      <td>34</td>\n", "      <td>300</td>\n", "      <td>33.7</td>\n", "      <td>0.467</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>7</td>\n", "      <td>150</td>\n", "      <td>66</td>\n", "      <td>42</td>\n", "      <td>342</td>\n", "      <td>34.7</td>\n", "      <td>0.718</td>\n", "      <td>42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>1</td>\n", "      <td>73</td>\n", "      <td>50</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>23.0</td>\n", "      <td>0.248</td>\n", "      <td>21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56</th>\n", "      <td>7</td>\n", "      <td>187</td>\n", "      <td>68</td>\n", "      <td>39</td>\n", "      <td>304</td>\n", "      <td>37.7</td>\n", "      <td>0.254</td>\n", "      <td>41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57</th>\n", "      <td>0</td>\n", "      <td>100</td>\n", "      <td>88</td>\n", "      <td>60</td>\n", "      <td>110</td>\n", "      <td>46.8</td>\n", "      <td>0.962</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>0</td>\n", "      <td>146</td>\n", "      <td>82</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>40.5</td>\n", "      <td>1.781</td>\n", "      <td>44</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-edc46baf-f939-4682-9747-1982911fd71a')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-edc46baf-f939-4682-9747-1982911fd71a button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-edc46baf-f939-4682-9747-1982911fd71a');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-86d558ba-fe7b-424a-ae10-8425654910dc\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-86d558ba-fe7b-424a-ae10-8425654910dc')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-86d558ba-fe7b-424a-ae10-8425654910dc button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_4ff4ce36-3dd8-4363-856e-51c0eb1b8c41\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('x')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_4ff4ce36-3dd8-4363-856e-51c0eb1b8c41 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('x');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "x", "summary": "{\n  \"name\": \"x\",\n  \"rows\": 59,\n  \"fields\": [\n    {\n      \"column\": \"Pregnancies\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3,\n        \"min\": 0,\n        \"max\": 13,\n        \"num_unique_values\": 13,\n        \"samples\": [\n          11,\n          7,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Glucose\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 33,\n        \"min\": 71,\n        \"max\": 197,\n        \"num_unique_values\": 50,\n        \"samples\": [\n          189,\n          106,\n          88\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BloodPressure\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21,\n        \"min\": 0,\n        \"max\": 110,\n        \"num_unique_values\": 25,\n        \"samples\": [\n          96,\n          94,\n          72\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SkinThickness\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 17,\n        \"min\": 0,\n        \"max\": 60,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          30,\n          34,\n          38\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Insulin\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 151,\n        \"min\": 0,\n        \"max\": 846,\n        \"num_unique_values\": 27,\n        \"samples\": [\n          83,\n          140,\n          96\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"BMI\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 9.064114444358726,\n        \"min\": 0.0,\n        \"max\": 46.8,\n        \"num_unique_values\": 58,\n        \"samples\": [\n          33.6,\n          25.6,\n          27.6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"DiabetesPedigreeFunction\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.4349965268542028,\n        \"min\": 0.134,\n        \"max\": 2.288,\n        \"num_unique_values\": 56,\n        \"samples\": [\n          0.627,\n          0.201,\n          0.512\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 11,\n        \"min\": 21,\n        \"max\": 60,\n        \"num_unique_values\": 33,\n        \"samples\": [\n          42,\n          41,\n          48\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 29}], "source": ["x=df.drop(['Outcome'], axis=1)\n", "x"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OuGHkap_z5Gu", "outputId": "ec4ab053-8e94-486e-a86e-4a2aa8982880"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Index(['Pregnancies', 'Glucose', 'BloodPressure', 'SkinThickness', 'Insulin',\n", "       'BMI', 'DiabetesPedigreeFunction', 'Age'],\n", "      dtype='object')"]}, "metadata": {}, "execution_count": 30}], "source": ["x=df.drop(['Outcome'], axis=1)\n", "x\n", "y=df.Outcome\n", "y\n", "features=x.columns\n", "features"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"id": "b91kgHCO7AtU"}, "outputs": [], "source": ["mean_age=df['Age'].mean()\n", "desired_split_age=25.0\n", "shift=desired_split_age - mean_age\n", "df['Age'] += shift\n", "\n", "x_train, x_test, y_train, y_test=train_test_split(x, y, test_size=0.2, random_state=32)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"id": "rPFkHeoc64PH"}, "outputs": [], "source": ["model=DecisionTreeClassifier()\n", "model=model.fit(x_train,y_train)\n", "y_pred=model.predict(x_test)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "eekeSl518jGX", "outputId": "172de93b-6d9c-428a-bdab-00818a46cc9b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Entropy of the dataset: 0.9948131754904235\n"]}], "source": ["def calculate_entropy(data, target_column):\n", "    total_rows=len(data)\n", "    target_values=data[target_column].unique()\n", "\n", "    entropy=0\n", "    for value in target_values:\n", "        value_count=len(data[data[target_column] == value])\n", "        proportion=value_count / total_rows\n", "        entropy -= proportion * math.log2(proportion)\n", "\n", "    return entropy\n", "\n", "entropy_outcome=calculate_entropy(df, 'Outcome')\n", "print(f\"Entropy of the dataset: {entropy_outcome}\")"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"id": "eE_QQ776LJ7Q"}, "outputs": [], "source": [" def calculate_information_gain(data, feature, target_column):\n", "    unique_values=data[feature].unique()\n", "    weighted_entropy=0\n", "\n", "    for value in unique_values:\n", "        subset=data[data[feature] == value]\n", "        proportion=len(subset) / len(data)\n", "        weighted_entropy += proportion * calculate_entropy(subset, target_column)\n", "\n", "    information_gain=entropy_outcome - weighted_entropy\n", "\n", "    return information_gain"]}, {"cell_type": "markdown", "metadata": {"id": "-2euFDrl83sv"}, "source": ["**Entropy of all colunms**"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r_shRbPl8T7X", "outputId": "cf98e7b5-85d6-4912-ad7f-5d0f13ed8ba6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Pregnancies - Entropy: 3.442, Information Gain: 0.163\n", "Glucose - Entropy: 5.544, Information Gain: 0.838\n", "BloodPressure - Entropy: 4.409, Information Gain: 0.468\n", "SkinThickness - Entropy: 3.998, Information Gain: 0.509\n", "Insulin - Entropy: 3.137, Information Gain: 0.466\n", "BMI - Entropy: 5.849, Information Gain: 0.961\n", "DiabetesPedigreeFunction - Entropy: 5.768, Information Gain: 0.961\n", "Age - Entropy: 4.829, Information Gain: 0.671\n"]}], "source": ["for column in df.columns[:-1]:\n", "    entropy=calculate_entropy(df, column)\n", "    information_gain=calculate_information_gain(df, column, 'Outcome')\n", "    print(f\"{column} - Entropy: {entropy:.3f}, Information Gain: {information_gain:.3f}\")"]}, {"cell_type": "markdown", "metadata": {"id": "ZO-xWRvAKGj9"}, "source": ["**ID3 tree**"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 912}, "id": "JTqH4d9x9aR_", "outputId": "a4907e62-f51a-421c-cbcf-4b814f9d4bb2"}, "outputs": [{"output_type": "execute_result", "data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "execution_count": 36}], "source": ["dot_data=StringIO()\n", "export_graphviz(model, out_file=dot_data,filled=True, rounded=True,special_characters=True,feature_names=features,class_names=['0','1'])\n", "graph=pydotplus.graph_from_dot_data(dot_data.getvalue())\n", "graph.write_png('diabetes_set.png')\n", "Image(graph.create_png())"]}, {"cell_type": "code", "source": ["def display_tree(clf, feature_names):\n", "    dot_data=tree.export_graphviz(clf, out_file=None,\n", "                                    feature_names=feature_names,\n", "                                    class_names=['0', '1'],\n", "                                    filled=True, rounded=True,\n", "                                    special_characters=True)\n", "    graph=graphviz.Source(dot_data)\n", "    display(graph)"], "metadata": {"id": "gbgp2Y7J4ITd"}, "execution_count": 37, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "eeZpaNzHREHB"}, "source": ["**C4.5 tree**"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 599}, "id": "754UVKH30SZ_", "outputId": "5f8e84bd-d0c9-46ed-a7cc-98d02eceac61"}, "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"478pt\" height=\"433pt\"\n viewBox=\"0.00 0.00 477.50 433.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 429)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-429 473.5,-429 473.5,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#fefaf7\" stroke=\"black\" d=\"M223.5,-425C223.5,-425 137.5,-425 137.5,-425 131.5,-425 125.5,-419 125.5,-413 125.5,-413 125.5,-354 125.5,-354 125.5,-348 131.5,-342 137.5,-342 137.5,-342 223.5,-342 223.5,-342 229.5,-342 235.5,-348 235.5,-354 235.5,-354 235.5,-413 235.5,-413 235.5,-419 229.5,-425 223.5,-425\"/>\n<text text-anchor=\"start\" x=\"147.5\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 24.5</text>\n<text text-anchor=\"start\" x=\"140.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 1.0</text>\n<text text-anchor=\"start\" x=\"139.5\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"133.5\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [24, 23]</text>\n<text text-anchor=\"start\" x=\"153\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M155.5,-298.5C155.5,-298.5 83.5,-298.5 83.5,-298.5 77.5,-298.5 71.5,-292.5 71.5,-286.5 71.5,-286.5 71.5,-242.5 71.5,-242.5 71.5,-236.5 77.5,-230.5 83.5,-230.5 83.5,-230.5 155.5,-230.5 155.5,-230.5 161.5,-230.5 167.5,-236.5 167.5,-242.5 167.5,-242.5 167.5,-286.5 167.5,-286.5 167.5,-292.5 161.5,-298.5 155.5,-298.5\"/>\n<text text-anchor=\"start\" x=\"79.5\" y=\"-283.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.0</text>\n<text text-anchor=\"start\" x=\"82\" y=\"-268.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"80\" y=\"-253.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 0]</text>\n<text text-anchor=\"start\" x=\"92\" y=\"-238.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M159.34,-341.91C153.58,-330.87 147.34,-318.9 141.54,-307.77\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"144.52,-305.92 136.79,-298.67 138.31,-309.15 144.52,-305.92\"/>\n<text text-anchor=\"middle\" x=\"129.29\" y=\"-318.81\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#d4eaf9\" stroke=\"black\" d=\"M285,-306C285,-306 198,-306 198,-306 192,-306 186,-300 186,-294 186,-294 186,-235 186,-235 186,-229 192,-223 198,-223 198,-223 285,-223 285,-223 291,-223 297,-229 297,-235 297,-235 297,-294 297,-294 297,-300 291,-306 285,-306\"/>\n<text text-anchor=\"start\" x=\"208.5\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 49.0</text>\n<text text-anchor=\"start\" x=\"194\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.989</text>\n<text text-anchor=\"start\" x=\"200.5\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 41</text>\n<text text-anchor=\"start\" x=\"194.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [18, 23]</text>\n<text text-anchor=\"start\" x=\"214\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 0&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>0&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M201.66,-341.91C206.21,-333.2 211.05,-323.9 215.75,-314.89\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"218.85,-316.51 220.37,-306.02 212.65,-313.27 218.85,-316.51\"/>\n<text text-anchor=\"middle\" x=\"227.88\" y=\"-326.17\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#fcf0e8\" stroke=\"black\" d=\"M229.5,-187C229.5,-187 107.5,-187 107.5,-187 101.5,-187 95.5,-181 95.5,-175 95.5,-175 95.5,-116 95.5,-116 95.5,-110 101.5,-104 107.5,-104 107.5,-104 229.5,-104 229.5,-104 235.5,-104 241.5,-110 241.5,-116 241.5,-116 241.5,-175 241.5,-175 241.5,-181 235.5,-187 229.5,-187\"/>\n<text text-anchor=\"start\" x=\"103.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BloodPressure ≤ 86.0</text>\n<text text-anchor=\"start\" x=\"121\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.997</text>\n<text text-anchor=\"start\" x=\"127.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 32</text>\n<text text-anchor=\"start\" x=\"121.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 15]</text>\n<text text-anchor=\"start\" x=\"141\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 2&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>2&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M216.17,-222.91C210.68,-214.1 204.81,-204.7 199.14,-195.61\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"202.05,-193.65 193.78,-187.02 196.11,-197.36 202.05,-193.65\"/>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#52a9e8\" stroke=\"black\" d=\"M359,-187C359,-187 272,-187 272,-187 266,-187 260,-181 260,-175 260,-175 260,-116 260,-116 260,-110 266,-104 272,-104 272,-104 359,-104 359,-104 365,-104 371,-110 371,-116 371,-116 371,-175 371,-175 371,-181 365,-187 359,-187\"/>\n<text text-anchor=\"start\" x=\"282.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 59.5</text>\n<text text-anchor=\"start\" x=\"268\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.503</text>\n<text text-anchor=\"start\" x=\"278\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"276\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 8]</text>\n<text text-anchor=\"start\" x=\"288\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 2&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>2&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M267.17,-222.91C272.74,-214.1 278.69,-204.7 284.44,-195.61\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"287.48,-197.34 289.87,-187.02 281.57,-193.6 287.48,-197.34\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#cae5f8\" stroke=\"black\" d=\"M99,-68C99,-68 12,-68 12,-68 6,-68 0,-62 0,-56 0,-56 0,-12 0,-12 0,-6 6,0 12,0 12,0 99,0 99,0 105,0 111,-6 111,-12 111,-12 111,-56 111,-56 111,-62 105,-68 99,-68\"/>\n<text text-anchor=\"start\" x=\"8\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.983</text>\n<text text-anchor=\"start\" x=\"14.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 26</text>\n<text text-anchor=\"start\" x=\"8.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [11, 15]</text>\n<text text-anchor=\"start\" x=\"28\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 3&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>3&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M126.42,-103.73C116.82,-94.42 106.63,-84.54 97.05,-75.26\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"99.48,-72.75 89.86,-68.3 94.61,-77.77 99.48,-72.75\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M213.5,-68C213.5,-68 141.5,-68 141.5,-68 135.5,-68 129.5,-62 129.5,-56 129.5,-56 129.5,-12 129.5,-12 129.5,-6 135.5,0 141.5,0 141.5,0 213.5,0 213.5,0 219.5,0 225.5,-6 225.5,-12 225.5,-12 225.5,-56 225.5,-56 225.5,-62 219.5,-68 213.5,-68\"/>\n<text text-anchor=\"start\" x=\"137.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.0</text>\n<text text-anchor=\"start\" x=\"140\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"138\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 0]</text>\n<text text-anchor=\"start\" x=\"150\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 3&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>3&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M171.85,-103.73C172.53,-95.43 173.25,-86.67 173.94,-78.28\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"177.43,-78.55 174.76,-68.3 170.46,-77.98 177.43,-78.55\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M343.5,-68C343.5,-68 271.5,-68 271.5,-68 265.5,-68 259.5,-62 259.5,-56 259.5,-56 259.5,-12 259.5,-12 259.5,-6 265.5,0 271.5,0 271.5,0 343.5,0 343.5,0 349.5,0 355.5,-6 355.5,-12 355.5,-12 355.5,-56 355.5,-56 355.5,-62 349.5,-68 343.5,-68\"/>\n<text text-anchor=\"start\" x=\"267.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.0</text>\n<text text-anchor=\"start\" x=\"270\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 8</text>\n<text text-anchor=\"start\" x=\"268\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 8]</text>\n<text text-anchor=\"start\" x=\"280\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 6&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>6&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M312.52,-103.73C311.91,-95.43 311.27,-86.67 310.66,-78.28\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"314.15,-78.02 309.93,-68.3 307.17,-78.53 314.15,-78.02\"/>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M457.5,-68C457.5,-68 385.5,-68 385.5,-68 379.5,-68 373.5,-62 373.5,-56 373.5,-56 373.5,-12 373.5,-12 373.5,-6 379.5,0 385.5,0 385.5,0 457.5,0 457.5,0 463.5,0 469.5,-6 469.5,-12 469.5,-12 469.5,-56 469.5,-56 469.5,-62 463.5,-68 457.5,-68\"/>\n<text text-anchor=\"start\" x=\"381.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">entropy = 0.0</text>\n<text text-anchor=\"start\" x=\"384\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"382\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 0]</text>\n<text text-anchor=\"start\" x=\"394\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 6&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>6&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M354.97,-103.73C363.89,-94.51 373.35,-84.74 382.26,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"384.82,-77.92 389.26,-68.3 379.79,-73.05 384.82,-77.92\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3aa398d0>"]}, "metadata": {}}], "source": ["c45 = DecisionTreeClassifier(criterion='entropy', max_depth=3, random_state=27)\n", "c45.fit(x_train, y_train)\n", "display_tree(c45, x.columns)"]}, {"cell_type": "markdown", "metadata": {"id": "_l1qcMX--acf"}, "source": ["**CHIsquared tree**"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 599}, "id": "yA9XXzpH5_S-", "outputId": "1b7a715e-9a60-4686-b5e2-381187529d36"}, "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"695pt\" height=\"433pt\"\n viewBox=\"0.00 0.00 694.50 433.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 429)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-429 690.5,-429 690.5,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#fefaf7\" stroke=\"black\" d=\"M390,-425C390,-425 298,-425 298,-425 292,-425 286,-419 286,-413 286,-413 286,-354 286,-354 286,-348 292,-342 298,-342 298,-342 390,-342 390,-342 396,-342 402,-348 402,-354 402,-354 402,-413 402,-413 402,-419 396,-425 390,-425\"/>\n<text text-anchor=\"start\" x=\"294\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 117.5</text>\n<text text-anchor=\"start\" x=\"316\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.5</text>\n<text text-anchor=\"start\" x=\"303\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"297\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [24, 23]</text>\n<text text-anchor=\"start\" x=\"316.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#f0b58b\" stroke=\"black\" d=\"M320,-306C320,-306 240,-306 240,-306 234,-306 228,-300 228,-294 228,-294 228,-235 228,-235 228,-229 234,-223 240,-223 240,-223 320,-223 320,-223 326,-223 332,-229 332,-235 332,-235 332,-294 332,-294 332,-300 326,-306 320,-306\"/>\n<text text-anchor=\"start\" x=\"236\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Insulin ≤ 199.5</text>\n<text text-anchor=\"start\" x=\"244.5\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.413</text>\n<text text-anchor=\"start\" x=\"239\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 24</text>\n<text text-anchor=\"start\" x=\"237\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 7]</text>\n<text text-anchor=\"start\" x=\"252.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M321.8,-341.91C317.03,-333.2 311.94,-323.9 307.02,-314.89\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"310.03,-313.11 302.17,-306.02 303.89,-316.47 310.03,-313.11\"/>\n<text text-anchor=\"middle\" x=\"295.14\" y=\"-326.31\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#90c8f0\" stroke=\"black\" d=\"M454,-306C454,-306 362,-306 362,-306 356,-306 350,-300 350,-294 350,-294 350,-235 350,-235 350,-229 356,-223 362,-223 362,-223 454,-223 454,-223 460,-223 466,-229 466,-235 466,-235 466,-294 466,-294 466,-300 460,-306 454,-306\"/>\n<text text-anchor=\"start\" x=\"358\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 158.0</text>\n<text text-anchor=\"start\" x=\"372.5\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.423</text>\n<text text-anchor=\"start\" x=\"367\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 23</text>\n<text text-anchor=\"start\" x=\"365\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 16]</text>\n<text text-anchor=\"start\" x=\"380.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 0&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>0&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M366.2,-341.91C370.97,-333.2 376.06,-323.9 380.98,-314.89\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"384.11,-316.47 385.83,-306.02 377.97,-313.11 384.11,-316.47\"/>\n<text text-anchor=\"middle\" x=\"392.86\" y=\"-326.31\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#eead7f\" stroke=\"black\" d=\"M205,-187C205,-187 113,-187 113,-187 107,-187 101,-181 101,-175 101,-175 101,-116 101,-116 101,-110 107,-104 113,-104 113,-104 205,-104 205,-104 211,-104 217,-110 217,-116 217,-116 217,-175 217,-175 217,-181 211,-187 205,-187\"/>\n<text text-anchor=\"start\" x=\"109\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 102.5</text>\n<text text-anchor=\"start\" x=\"123.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.386</text>\n<text text-anchor=\"start\" x=\"118\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 23</text>\n<text text-anchor=\"start\" x=\"116\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 6]</text>\n<text text-anchor=\"start\" x=\"131.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>1&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M238.02,-222.91C228.45,-213.65 218.19,-203.73 208.34,-194.21\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"210.53,-191.46 200.91,-187.02 205.66,-196.49 210.53,-191.46\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M318.5,-179.5C318.5,-179.5 247.5,-179.5 247.5,-179.5 241.5,-179.5 235.5,-173.5 235.5,-167.5 235.5,-167.5 235.5,-123.5 235.5,-123.5 235.5,-117.5 241.5,-111.5 247.5,-111.5 247.5,-111.5 318.5,-111.5 318.5,-111.5 324.5,-111.5 330.5,-117.5 330.5,-123.5 330.5,-123.5 330.5,-167.5 330.5,-167.5 330.5,-173.5 324.5,-179.5 318.5,-179.5\"/>\n<text text-anchor=\"start\" x=\"255\" y=\"-164.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"245.5\" y=\"-149.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"243.5\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 1]</text>\n<text text-anchor=\"start\" x=\"255.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 1&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>1&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M281.04,-222.91C281.32,-212.2 281.61,-200.62 281.89,-189.78\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"285.39,-189.75 282.15,-179.67 278.39,-189.57 285.39,-189.75\"/>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#f4c9aa\" stroke=\"black\" d=\"M86,-68C86,-68 12,-68 12,-68 6,-68 0,-62 0,-56 0,-56 0,-12 0,-12 0,-6 6,0 12,0 12,0 86,0 86,0 92,0 98,-6 98,-12 98,-12 98,-56 98,-56 98,-62 92,-68 86,-68\"/>\n<text text-anchor=\"start\" x=\"13.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.463</text>\n<text text-anchor=\"start\" x=\"8\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 11</text>\n<text text-anchor=\"start\" x=\"9.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 4]</text>\n<text text-anchor=\"start\" x=\"21.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 2&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>2&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M118.04,-103.73C108.79,-94.51 98.96,-84.74 89.72,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"92.01,-72.87 82.45,-68.3 87.07,-77.84 92.01,-72.87\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#ea9a61\" stroke=\"black\" d=\"M206,-68C206,-68 128,-68 128,-68 122,-68 116,-62 116,-56 116,-56 116,-12 116,-12 116,-6 122,0 128,0 128,0 206,0 206,0 212,0 218,-6 218,-12 218,-12 218,-56 218,-56 218,-62 212,-68 206,-68\"/>\n<text text-anchor=\"start\" x=\"131.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.278</text>\n<text text-anchor=\"start\" x=\"126\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 12</text>\n<text text-anchor=\"start\" x=\"124\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [10, 2]</text>\n<text text-anchor=\"start\" x=\"139.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 2&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>2&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M161.98,-103.73C162.59,-95.43 163.23,-86.67 163.84,-78.28\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"167.33,-78.53 164.57,-68.3 160.35,-78.02 167.33,-78.53\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#cee6f8\" stroke=\"black\" d=\"M446,-187C446,-187 366,-187 366,-187 360,-187 354,-181 354,-175 354,-175 354,-116 354,-116 354,-110 360,-104 366,-104 366,-104 446,-104 446,-104 452,-104 458,-110 458,-116 458,-116 458,-175 458,-175 458,-181 452,-187 446,-187\"/>\n<text text-anchor=\"start\" x=\"362\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Insulin ≤ 232.5</text>\n<text text-anchor=\"start\" x=\"374\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.49</text>\n<text text-anchor=\"start\" x=\"365\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 14</text>\n<text text-anchor=\"start\" x=\"366.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 8]</text>\n<text text-anchor=\"start\" x=\"378.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 6&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>6&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M407.31,-222.91C407.16,-214.56 407.01,-205.67 406.86,-197.02\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"410.36,-196.96 406.69,-187.02 403.36,-197.08 410.36,-196.96\"/>\n</g>\n<!-- 10 -->\n<g id=\"node11\" class=\"node\">\n<title>10</title>\n<path fill=\"#52a9e8\" stroke=\"black\" d=\"M568,-187C568,-187 488,-187 488,-187 482,-187 476,-181 476,-175 476,-175 476,-116 476,-116 476,-110 482,-104 488,-104 488,-104 568,-104 568,-104 574,-104 580,-110 580,-116 580,-116 580,-175 580,-175 580,-181 574,-187 568,-187\"/>\n<text text-anchor=\"start\" x=\"484\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Insulin ≤ 122.5</text>\n<text text-anchor=\"start\" x=\"492.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.198</text>\n<text text-anchor=\"start\" x=\"490.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"488.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 8]</text>\n<text text-anchor=\"start\" x=\"500.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 6&#45;&gt;10 -->\n<g id=\"edge10\" class=\"edge\">\n<title>6&#45;&gt;10</title>\n<path fill=\"none\" stroke=\"black\" d=\"M449.63,-222.91C459.13,-213.65 469.3,-203.73 479.07,-194.21\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"481.72,-196.51 486.44,-187.02 476.84,-191.5 481.72,-196.51\"/>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#9ccef2\" stroke=\"black\" d=\"M331,-68C331,-68 257,-68 257,-68 251,-68 245,-62 245,-56 245,-56 245,-12 245,-12 245,-6 251,0 257,0 257,0 331,0 331,0 337,0 343,-6 343,-12 343,-12 343,-56 343,-56 343,-62 337,-68 331,-68\"/>\n<text text-anchor=\"start\" x=\"258.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"253\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 12</text>\n<text text-anchor=\"start\" x=\"254.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [4, 8]</text>\n<text text-anchor=\"start\" x=\"266.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 7&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>7&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M364.3,-103.73C354.87,-94.51 344.87,-84.74 335.46,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"337.66,-72.79 328.06,-68.3 332.76,-77.79 337.66,-72.79\"/>\n</g>\n<!-- 9 -->\n<g id=\"node10\" class=\"node\">\n<title>9</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M444.5,-68C444.5,-68 373.5,-68 373.5,-68 367.5,-68 361.5,-62 361.5,-56 361.5,-56 361.5,-12 361.5,-12 361.5,-6 367.5,0 373.5,0 373.5,0 444.5,0 444.5,0 450.5,0 456.5,-6 456.5,-12 456.5,-12 456.5,-56 456.5,-56 456.5,-62 450.5,-68 444.5,-68\"/>\n<text text-anchor=\"start\" x=\"381\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"371.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 2</text>\n<text text-anchor=\"start\" x=\"369.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 0]</text>\n<text text-anchor=\"start\" x=\"381.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 7&#45;&gt;9 -->\n<g id=\"edge9\" class=\"edge\">\n<title>7&#45;&gt;9</title>\n<path fill=\"none\" stroke=\"black\" d=\"M407.12,-103.73C407.34,-95.52 407.58,-86.86 407.81,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"411.31,-78.39 408.09,-68.3 404.32,-78.2 411.31,-78.39\"/>\n</g>\n<!-- 11 -->\n<g id=\"node12\" class=\"node\">\n<title>11</title>\n<path fill=\"#7bbeee\" stroke=\"black\" d=\"M561.5,-68C561.5,-68 490.5,-68 490.5,-68 484.5,-68 478.5,-62 478.5,-56 478.5,-56 478.5,-12 478.5,-12 478.5,-6 484.5,0 490.5,0 490.5,0 561.5,0 561.5,0 567.5,0 573.5,-6 573.5,-12 573.5,-12 573.5,-56 573.5,-56 573.5,-62 567.5,-68 561.5,-68\"/>\n<text text-anchor=\"start\" x=\"490.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.375</text>\n<text text-anchor=\"start\" x=\"488.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"486.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 3]</text>\n<text text-anchor=\"start\" x=\"498.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 10&#45;&gt;11 -->\n<g id=\"edge11\" class=\"edge\">\n<title>10&#45;&gt;11</title>\n<path fill=\"none\" stroke=\"black\" d=\"M527.26,-103.73C527.11,-95.52 526.95,-86.86 526.8,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"530.29,-78.23 526.61,-68.3 523.29,-78.36 530.29,-78.23\"/>\n</g>\n<!-- 12 -->\n<g id=\"node13\" class=\"node\">\n<title>12</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M674.5,-68C674.5,-68 603.5,-68 603.5,-68 597.5,-68 591.5,-62 591.5,-56 591.5,-56 591.5,-12 591.5,-12 591.5,-6 597.5,0 603.5,0 603.5,0 674.5,0 674.5,0 680.5,0 686.5,-6 686.5,-12 686.5,-12 686.5,-56 686.5,-56 686.5,-62 680.5,-68 674.5,-68\"/>\n<text text-anchor=\"start\" x=\"611\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"601.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 5</text>\n<text text-anchor=\"start\" x=\"599.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 5]</text>\n<text text-anchor=\"start\" x=\"611.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 10&#45;&gt;12 -->\n<g id=\"edge12\" class=\"edge\">\n<title>10&#45;&gt;12</title>\n<path fill=\"none\" stroke=\"black\" d=\"M569.33,-103.73C578.67,-94.51 588.58,-84.74 597.91,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"600.58,-77.81 605.24,-68.3 595.67,-72.83 600.58,-77.81\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3a802dd0>"]}, "metadata": {}}], "source": ["chi2_scores, p_values=chi2(x_train, y_train)\n", "top_features_idx=np.argsort(chi2_scores)[-2:]\n", "x_train_chi2=x_train.iloc[:, top_features_idx]\n", "x_test_chi2=x_test.iloc[:, top_features_idx]\n", "chi2_tree=DecisionTreeClassifier(max_depth=3, random_state=27)\n", "chi2_tree.fit(x_train_chi2, y_train)\n", "dot_data=tree.export_graphviz(chi2_tree, out_file=None,\n", "                                feature_names=x.columns[top_features_idx],\n", "                                class_names=['0', '1'],\n", "                                filled=True, rounded=True,\n", "                                special_characters=True)\n", "graph=graphviz.Source(dot_data)\n", "display(graph)"]}, {"cell_type": "markdown", "metadata": {"id": "Wi1hQS_E-4_0"}, "source": ["**CART(classification and regression tree)**"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 599}, "id": "Q-TRLLDx-pUQ", "outputId": "6be814f3-2705-4402-dddc-5989737cf592"}, "outputs": [{"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"902pt\" height=\"433pt\"\n viewBox=\"0.00 0.00 902.00 433.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 429)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-429 898,-429 898,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#fefaf7\" stroke=\"black\" d=\"M486,-425C486,-425 379,-425 379,-425 373,-425 367,-419 367,-413 367,-413 367,-354 367,-354 367,-348 373,-342 379,-342 379,-342 486,-342 486,-342 492,-342 498,-348 498,-354 498,-354 498,-413 498,-413 498,-419 492,-425 486,-425\"/>\n<text text-anchor=\"start\" x=\"375\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 126.734</text>\n<text text-anchor=\"start\" x=\"404.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.5</text>\n<text text-anchor=\"start\" x=\"391.5\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"385.5\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [24, 23]</text>\n<text text-anchor=\"start\" x=\"405\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#f4caac\" stroke=\"black\" d=\"M385,-306C385,-306 276,-306 276,-306 270,-306 264,-300 264,-294 264,-294 264,-235 264,-235 264,-229 270,-223 276,-223 276,-223 385,-223 385,-223 391,-223 397,-229 397,-235 397,-235 397,-294 397,-294 397,-300 391,-306 385,-306\"/>\n<text text-anchor=\"start\" x=\"272\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Pregnancies ≤ 7.08</text>\n<text text-anchor=\"start\" x=\"295\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.464</text>\n<text text-anchor=\"start\" x=\"289.5\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 30</text>\n<text text-anchor=\"start\" x=\"283.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [19, 11]</text>\n<text text-anchor=\"start\" x=\"303\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M397.11,-341.91C389.2,-332.83 380.73,-323.12 372.58,-313.77\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"375.04,-311.26 365.83,-306.02 369.76,-315.86 375.04,-311.26\"/>\n<text text-anchor=\"middle\" x=\"364.12\" y=\"-327.26\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#8bc6f0\" stroke=\"black\" d=\"M605,-306C605,-306 498,-306 498,-306 492,-306 486,-300 486,-294 486,-294 486,-235 486,-235 486,-229 492,-223 498,-223 498,-223 605,-223 605,-223 611,-223 617,-229 617,-235 617,-235 617,-294 617,-294 617,-300 611,-306 605,-306\"/>\n<text text-anchor=\"start\" x=\"494\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 156.364</text>\n<text text-anchor=\"start\" x=\"516\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.415</text>\n<text text-anchor=\"start\" x=\"510.5\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 17</text>\n<text text-anchor=\"start\" x=\"508.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [5, 12]</text>\n<text text-anchor=\"start\" x=\"524\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 0&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>0&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M473.79,-341.91C483.2,-332.65 493.29,-322.73 502.98,-313.21\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"505.61,-315.53 510.29,-306.02 500.7,-310.54 505.61,-315.53\"/>\n<text text-anchor=\"middle\" x=\"510.07\" y=\"-327.32\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#f0b58b\" stroke=\"black\" d=\"M203.5,-187C203.5,-187 125.5,-187 125.5,-187 119.5,-187 113.5,-181 113.5,-175 113.5,-175 113.5,-116 113.5,-116 113.5,-110 119.5,-104 125.5,-104 125.5,-104 203.5,-104 203.5,-104 209.5,-104 215.5,-110 215.5,-116 215.5,-116 215.5,-175 215.5,-175 215.5,-181 209.5,-187 203.5,-187\"/>\n<text text-anchor=\"start\" x=\"123.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 25.481</text>\n<text text-anchor=\"start\" x=\"129\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.413</text>\n<text text-anchor=\"start\" x=\"123.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 24</text>\n<text text-anchor=\"start\" x=\"121.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 7]</text>\n<text text-anchor=\"start\" x=\"137\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>1&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M272.91,-222.91C257.04,-211.72 239.8,-199.57 223.83,-188.32\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"225.81,-185.43 215.61,-182.53 221.77,-191.15 225.81,-185.43\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#9ccef2\" stroke=\"black\" d=\"M388.5,-187C388.5,-187 272.5,-187 272.5,-187 266.5,-187 260.5,-181 260.5,-175 260.5,-175 260.5,-116 260.5,-116 260.5,-110 266.5,-104 272.5,-104 272.5,-104 388.5,-104 388.5,-104 394.5,-104 400.5,-110 400.5,-116 400.5,-116 400.5,-175 400.5,-175 400.5,-181 394.5,-187 388.5,-187\"/>\n<text text-anchor=\"start\" x=\"268.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Pregnancies ≤ 9.353</text>\n<text text-anchor=\"start\" x=\"295\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"293\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"291\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 4]</text>\n<text text-anchor=\"start\" x=\"303\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 1&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>1&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M330.5,-222.91C330.5,-214.65 330.5,-205.86 330.5,-197.3\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"334,-197.02 330.5,-187.02 327,-197.02 334,-197.02\"/>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M83,-68C83,-68 12,-68 12,-68 6,-68 0,-62 0,-56 0,-56 0,-12 0,-12 0,-6 6,0 12,0 12,0 83,0 83,0 89,0 95,-6 95,-12 95,-12 95,-56 95,-56 95,-62 89,-68 83,-68\"/>\n<text text-anchor=\"start\" x=\"19.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"10\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 7</text>\n<text text-anchor=\"start\" x=\"8\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 0]</text>\n<text text-anchor=\"start\" x=\"20\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 2&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>2&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M120.93,-103.73C110.99,-94.42 100.44,-84.54 90.52,-75.26\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"92.77,-72.58 83.08,-68.3 87.99,-77.69 92.77,-72.58\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#f7d9c4\" stroke=\"black\" d=\"M203.5,-68C203.5,-68 125.5,-68 125.5,-68 119.5,-68 113.5,-62 113.5,-56 113.5,-56 113.5,-12 113.5,-12 113.5,-6 119.5,0 125.5,0 125.5,0 203.5,0 203.5,0 209.5,0 215.5,-6 215.5,-12 215.5,-12 215.5,-56 215.5,-56 215.5,-62 209.5,-68 203.5,-68\"/>\n<text text-anchor=\"start\" x=\"129\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.484</text>\n<text text-anchor=\"start\" x=\"123.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 17</text>\n<text text-anchor=\"start\" x=\"121.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [10, 7]</text>\n<text text-anchor=\"start\" x=\"137\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 2&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>2&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M164.5,-103.73C164.5,-95.52 164.5,-86.86 164.5,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"168,-78.3 164.5,-68.3 161,-78.3 168,-78.3\"/>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M317,-68C317,-68 246,-68 246,-68 240,-68 234,-62 234,-56 234,-56 234,-12 234,-12 234,-6 240,0 246,0 246,0 317,0 317,0 323,0 329,-6 329,-12 329,-12 329,-56 329,-56 329,-62 323,-68 317,-68\"/>\n<text text-anchor=\"start\" x=\"253.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"244\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 3</text>\n<text text-anchor=\"start\" x=\"242\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 3]</text>\n<text text-anchor=\"start\" x=\"254\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 5&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>5&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M312.25,-103.73C308.42,-95.15 304.36,-86.09 300.5,-77.46\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"303.68,-76 296.4,-68.3 297.29,-78.86 303.68,-76\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#f2c09c\" stroke=\"black\" d=\"M430,-68C430,-68 359,-68 359,-68 353,-68 347,-62 347,-56 347,-56 347,-12 347,-12 347,-6 353,0 359,0 359,0 430,0 430,0 436,0 442,-6 442,-12 442,-12 442,-56 442,-56 442,-62 436,-68 430,-68\"/>\n<text text-anchor=\"start\" x=\"359\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"357\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 3</text>\n<text text-anchor=\"start\" x=\"355\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 1]</text>\n<text text-anchor=\"start\" x=\"367\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 5&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>5&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M354.33,-103.73C359.4,-95.06 364.75,-85.9 369.85,-77.18\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"373.01,-78.7 375.04,-68.3 366.97,-75.17 373.01,-78.7\"/>\n</g>\n<!-- 9 -->\n<g id=\"node10\" class=\"node\">\n<title>9</title>\n<path fill=\"#ffffff\" stroke=\"black\" d=\"M588,-187C588,-187 515,-187 515,-187 509,-187 503,-181 503,-175 503,-175 503,-116 503,-116 503,-110 509,-104 515,-104 515,-104 588,-104 588,-104 594,-104 600,-110 600,-116 600,-116 600,-175 600,-175 600,-181 594,-187 588,-187\"/>\n<text text-anchor=\"start\" x=\"511\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 45.975</text>\n<text text-anchor=\"start\" x=\"523.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.5</text>\n<text text-anchor=\"start\" x=\"514\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 8</text>\n<text text-anchor=\"start\" x=\"512\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [4, 4]</text>\n<text text-anchor=\"start\" x=\"524\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 8&#45;&gt;9 -->\n<g id=\"edge9\" class=\"edge\">\n<title>8&#45;&gt;9</title>\n<path fill=\"none\" stroke=\"black\" d=\"M551.5,-222.91C551.5,-214.65 551.5,-205.86 551.5,-197.3\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"555,-197.02 551.5,-187.02 548,-197.02 555,-197.02\"/>\n</g>\n<!-- 12 -->\n<g id=\"node13\" class=\"node\">\n<title>12</title>\n<path fill=\"#52a9e8\" stroke=\"black\" d=\"M791.5,-187C791.5,-187 675.5,-187 675.5,-187 669.5,-187 663.5,-181 663.5,-175 663.5,-175 663.5,-116 663.5,-116 663.5,-110 669.5,-104 675.5,-104 675.5,-104 791.5,-104 791.5,-104 797.5,-104 803.5,-110 803.5,-116 803.5,-116 803.5,-175 803.5,-175 803.5,-181 797.5,-187 791.5,-187\"/>\n<text text-anchor=\"start\" x=\"671.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Pregnancies ≤ 3.518</text>\n<text text-anchor=\"start\" x=\"698\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.198</text>\n<text text-anchor=\"start\" x=\"696\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"694\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 8]</text>\n<text text-anchor=\"start\" x=\"706\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 8&#45;&gt;12 -->\n<g id=\"edge12\" class=\"edge\">\n<title>8&#45;&gt;12</title>\n<path fill=\"none\" stroke=\"black\" d=\"M614.64,-222.91C629.89,-213.11 646.29,-202.56 661.89,-192.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"663.95,-195.37 670.47,-187.02 660.16,-189.48 663.95,-195.37\"/>\n</g>\n<!-- 10 -->\n<g id=\"node11\" class=\"node\">\n<title>10</title>\n<path fill=\"#f2c09c\" stroke=\"black\" d=\"M543,-68C543,-68 472,-68 472,-68 466,-68 460,-62 460,-56 460,-56 460,-12 460,-12 460,-6 466,0 472,0 472,0 543,0 543,0 549,0 555,-6 555,-12 555,-12 555,-56 555,-56 555,-62 549,-68 543,-68\"/>\n<text text-anchor=\"start\" x=\"472\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"470\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"468\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [4, 2]</text>\n<text text-anchor=\"start\" x=\"480\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 9&#45;&gt;10 -->\n<g id=\"edge10\" class=\"edge\">\n<title>9&#45;&gt;10</title>\n<path fill=\"none\" stroke=\"black\" d=\"M535.12,-103.73C531.71,-95.24 528.11,-86.28 524.67,-77.73\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"527.86,-76.27 520.88,-68.3 521.36,-78.88 527.86,-76.27\"/>\n</g>\n<!-- 11 -->\n<g id=\"node12\" class=\"node\">\n<title>11</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M656,-68C656,-68 585,-68 585,-68 579,-68 573,-62 573,-56 573,-56 573,-12 573,-12 573,-6 579,0 585,0 585,0 656,0 656,0 662,0 668,-6 668,-12 668,-12 668,-56 668,-56 668,-62 662,-68 656,-68\"/>\n<text text-anchor=\"start\" x=\"592.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"583\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 2</text>\n<text text-anchor=\"start\" x=\"581\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 2]</text>\n<text text-anchor=\"start\" x=\"593\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 9&#45;&gt;11 -->\n<g id=\"edge11\" class=\"edge\">\n<title>9&#45;&gt;11</title>\n<path fill=\"none\" stroke=\"black\" d=\"M577.19,-103.73C582.71,-94.97 588.55,-85.7 594.09,-76.91\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"597.15,-78.63 599.52,-68.3 591.22,-74.89 597.15,-78.63\"/>\n</g>\n<!-- 13 -->\n<g id=\"node14\" class=\"node\">\n<title>13</title>\n<path fill=\"#7bbeee\" stroke=\"black\" d=\"M769,-68C769,-68 698,-68 698,-68 692,-68 686,-62 686,-56 686,-56 686,-12 686,-12 686,-6 692,0 698,0 698,0 769,0 769,0 775,0 781,-6 781,-12 781,-12 781,-56 781,-56 781,-62 775,-68 769,-68\"/>\n<text text-anchor=\"start\" x=\"698\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.375</text>\n<text text-anchor=\"start\" x=\"696\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"694\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 3]</text>\n<text text-anchor=\"start\" x=\"706\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 12&#45;&gt;13 -->\n<g id=\"edge13\" class=\"edge\">\n<title>12&#45;&gt;13</title>\n<path fill=\"none\" stroke=\"black\" d=\"M733.5,-103.73C733.5,-95.52 733.5,-86.86 733.5,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"737,-78.3 733.5,-68.3 730,-78.3 737,-78.3\"/>\n</g>\n<!-- 14 -->\n<g id=\"node15\" class=\"node\">\n<title>14</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M882,-68C882,-68 811,-68 811,-68 805,-68 799,-62 799,-56 799,-56 799,-12 799,-12 799,-6 805,0 811,0 811,0 882,0 882,0 888,0 894,-6 894,-12 894,-12 894,-56 894,-56 894,-62 888,-68 882,-68\"/>\n<text text-anchor=\"start\" x=\"818.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"809\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 5</text>\n<text text-anchor=\"start\" x=\"807\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 5]</text>\n<text text-anchor=\"start\" x=\"819\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 12&#45;&gt;14 -->\n<g id=\"edge14\" class=\"edge\">\n<title>12&#45;&gt;14</title>\n<path fill=\"none\" stroke=\"black\" d=\"M775.58,-103.73C785.18,-94.42 795.37,-84.54 804.95,-75.26\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"807.39,-77.77 812.14,-68.3 802.52,-72.75 807.39,-77.77\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3a801750>"]}, "metadata": {}}], "source": ["cart=DecisionTreeClassifier(max_depth=3, random_state=27,splitter='random')\n", "cart.fit(x_train, y_train)\n", "display_tree(cart, x.columns)"]}, {"cell_type": "code", "source": ["y_pred = model.predict(x_test)\n", "accuracy_id3 = accuracy_score(y_test, y_pred)\n", "print(f\"ID3 Accuracy: {accuracy_id3:.2f}\")\n", "y_pred_c45 = c45.predict(x_test)\n", "accuracy_c45 = accuracy_score(y_test, y_pred_c45)\n", "print(f\"C4.5  Accuracy: {accuracy_c45:.2f}\")\n", "y_pred_chi2=chi2_tree.predict(x_test_chi2)\n", "accuracy_chi2=accuracy_score(y_test, y_pred_chi2)\n", "print(f\"CHIsquared Tree Accuracy: {accuracy_chi2:.2f}\")\n", "y_pred_cart = cart.predict(x_test)\n", "accuracy_cart = accuracy_score(y_test, y_pred_cart)\n", "print(f\"CART Accuracy: {accuracy_cart:.2f}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Fxdx6WuzRQq_", "outputId": "5f50ce32-e4d8-4958-f2f4-88aadec5d6e7"}, "execution_count": 41, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["ID3 Accuracy: 0.67\n", "C4.5  Accuracy: 0.33\n", "CHIsquared Tree Accuracy: 0.50\n", "CART Accuracy: 0.58\n"]}]}, {"cell_type": "markdown", "source": ["**Pre-prune and Post-prune of data**"], "metadata": {"id": "8rD-LhZj5aOH"}}, {"cell_type": "code", "source": ["cart_prepruned = DecisionTreeClassifier(max_depth=4, random_state=27)\n", "cart_prepruned.fit(x_train, y_train)\n", "y_pred_cart_prepruned = cart_prepruned.predict(x_test)\n", "accuracy_cart_prepruned = accuracy_score(y_test, y_pred_cart_prepruned)\n", "print(f\"Pre-pruned CART Accuracy: {accuracy_cart_prepruned:.2f}\")\n", "display_tree(cart_prepruned, x.columns)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 775}, "id": "gZ3qoq-qiyd6", "outputId": "23dc4245-eb4d-4ab7-d9ca-8a39d396f0f9"}, "execution_count": 42, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Pre-pruned CART Accuracy: 0.83\n"]}, {"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"791pt\" height=\"552pt\"\n viewBox=\"0.00 0.00 790.50 552.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 548)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-548 786.5,-548 786.5,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#fefaf7\" stroke=\"black\" d=\"M302,-544C302,-544 210,-544 210,-544 204,-544 198,-538 198,-532 198,-532 198,-473 198,-473 198,-467 204,-461 210,-461 210,-461 302,-461 302,-461 308,-461 314,-467 314,-473 314,-473 314,-532 314,-532 314,-538 308,-544 302,-544\"/>\n<text text-anchor=\"start\" x=\"206\" y=\"-528.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 117.5</text>\n<text text-anchor=\"start\" x=\"228\" y=\"-513.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.5</text>\n<text text-anchor=\"start\" x=\"215\" y=\"-498.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"209\" y=\"-483.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [24, 23]</text>\n<text text-anchor=\"start\" x=\"228.5\" y=\"-468.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#f0b58b\" stroke=\"black\" d=\"M205,-425C205,-425 127,-425 127,-425 121,-425 115,-419 115,-413 115,-413 115,-354 115,-354 115,-348 121,-342 127,-342 127,-342 205,-342 205,-342 211,-342 217,-348 217,-354 217,-354 217,-413 217,-413 217,-419 211,-425 205,-425\"/>\n<text text-anchor=\"start\" x=\"129\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 28.85</text>\n<text text-anchor=\"start\" x=\"130.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.413</text>\n<text text-anchor=\"start\" x=\"125\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 24</text>\n<text text-anchor=\"start\" x=\"123\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 7]</text>\n<text text-anchor=\"start\" x=\"138.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M224.77,-460.91C217.86,-451.92 210.47,-442.32 203.35,-433.05\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"206.04,-430.81 197.17,-425.02 200.49,-435.08 206.04,-430.81\"/>\n<text text-anchor=\"middle\" x=\"193.94\" y=\"-446.11\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#90c8f0\" stroke=\"black\" d=\"M447,-425C447,-425 247,-425 247,-425 241,-425 235,-419 235,-413 235,-413 235,-354 235,-354 235,-348 241,-342 247,-342 247,-342 447,-342 447,-342 453,-342 459,-348 459,-354 459,-354 459,-413 459,-413 459,-419 453,-425 447,-425\"/>\n<text text-anchor=\"start\" x=\"243\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">DiabetesPedigreeFunction ≤ 0.267</text>\n<text text-anchor=\"start\" x=\"311.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.423</text>\n<text text-anchor=\"start\" x=\"306\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 23</text>\n<text text-anchor=\"start\" x=\"304\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 16]</text>\n<text text-anchor=\"start\" x=\"319.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 0&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>0&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M287.57,-460.91C294.56,-451.92 302.03,-442.32 309.24,-433.05\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"312.11,-435.06 315.48,-425.02 306.58,-430.77 312.11,-435.06\"/>\n<text text-anchor=\"middle\" x=\"318.58\" y=\"-446.13\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M90,-298.5C90,-298.5 12,-298.5 12,-298.5 6,-298.5 0,-292.5 0,-286.5 0,-286.5 0,-242.5 0,-242.5 0,-236.5 6,-230.5 12,-230.5 12,-230.5 90,-230.5 90,-230.5 96,-230.5 102,-236.5 102,-242.5 102,-242.5 102,-286.5 102,-286.5 102,-292.5 96,-298.5 90,-298.5\"/>\n<text text-anchor=\"start\" x=\"23\" y=\"-283.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"10\" y=\"-268.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 11</text>\n<text text-anchor=\"start\" x=\"8\" y=\"-253.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [11, 0]</text>\n<text text-anchor=\"start\" x=\"23.5\" y=\"-238.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>1&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M126.1,-341.91C114.71,-330.32 102.31,-317.7 90.92,-306.11\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"93.11,-303.35 83.6,-298.67 88.11,-308.25 93.11,-303.35\"/>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#e3f1fb\" stroke=\"black\" d=\"M206,-306C206,-306 132,-306 132,-306 126,-306 120,-300 120,-294 120,-294 120,-235 120,-235 120,-229 126,-223 132,-223 132,-223 206,-223 206,-223 212,-223 218,-229 218,-235 218,-235 218,-294 218,-294 218,-300 212,-306 206,-306\"/>\n<text text-anchor=\"start\" x=\"135.5\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 33.5</text>\n<text text-anchor=\"start\" x=\"133.5\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.497</text>\n<text text-anchor=\"start\" x=\"128\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 13</text>\n<text text-anchor=\"start\" x=\"129.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 7]</text>\n<text text-anchor=\"start\" x=\"141.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 1&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>1&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M167.04,-341.91C167.25,-333.56 167.48,-324.67 167.7,-316.02\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"171.2,-316.11 167.96,-306.02 164.21,-315.93 171.2,-316.11\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M172.5,-179.5C172.5,-179.5 101.5,-179.5 101.5,-179.5 95.5,-179.5 89.5,-173.5 89.5,-167.5 89.5,-167.5 89.5,-123.5 89.5,-123.5 89.5,-117.5 95.5,-111.5 101.5,-111.5 101.5,-111.5 172.5,-111.5 172.5,-111.5 178.5,-111.5 184.5,-117.5 184.5,-123.5 184.5,-123.5 184.5,-167.5 184.5,-167.5 184.5,-173.5 178.5,-179.5 172.5,-179.5\"/>\n<text text-anchor=\"start\" x=\"109\" y=\"-164.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"99.5\" y=\"-149.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"97.5\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 4]</text>\n<text text-anchor=\"start\" x=\"109.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 3&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>3&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M157.9,-222.91C154.94,-212.09 151.74,-200.38 148.74,-189.44\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"152.09,-188.39 146.07,-179.67 145.33,-190.24 152.09,-188.39\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#f2c09c\" stroke=\"black\" d=\"M337,-187C337,-187 215,-187 215,-187 209,-187 203,-181 203,-175 203,-175 203,-116 203,-116 203,-110 209,-104 215,-104 215,-104 337,-104 337,-104 343,-104 349,-110 349,-116 349,-116 349,-175 349,-175 349,-181 343,-187 337,-187\"/>\n<text text-anchor=\"start\" x=\"211\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BloodPressure ≤ 73.5</text>\n<text text-anchor=\"start\" x=\"240.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"238.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"236.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 3]</text>\n<text text-anchor=\"start\" x=\"248.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 3&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>3&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M206.12,-222.91C214.51,-213.74 223.48,-203.93 232.11,-194.49\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"234.78,-196.76 238.94,-187.02 229.61,-192.04 234.78,-196.76\"/>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#bddef6\" stroke=\"black\" d=\"M202.5,-68C202.5,-68 131.5,-68 131.5,-68 125.5,-68 119.5,-62 119.5,-56 119.5,-56 119.5,-12 119.5,-12 119.5,-6 125.5,0 131.5,0 131.5,0 202.5,0 202.5,0 208.5,0 214.5,-6 214.5,-12 214.5,-12 214.5,-56 214.5,-56 214.5,-62 208.5,-68 202.5,-68\"/>\n<text text-anchor=\"start\" x=\"135\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.48</text>\n<text text-anchor=\"start\" x=\"129.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 5</text>\n<text text-anchor=\"start\" x=\"127.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 3]</text>\n<text text-anchor=\"start\" x=\"139.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 5&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>5&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M235.41,-103.73C226.24,-94.51 216.51,-84.74 207.35,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"209.68,-72.92 200.15,-68.3 204.72,-77.86 209.68,-72.92\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M315.5,-68C315.5,-68 244.5,-68 244.5,-68 238.5,-68 232.5,-62 232.5,-56 232.5,-56 232.5,-12 232.5,-12 232.5,-6 238.5,0 244.5,0 244.5,0 315.5,0 315.5,0 321.5,0 327.5,-6 327.5,-12 327.5,-12 327.5,-56 327.5,-56 327.5,-62 321.5,-68 315.5,-68\"/>\n<text text-anchor=\"start\" x=\"252\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"242.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"240.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [4, 0]</text>\n<text text-anchor=\"start\" x=\"252.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 5&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>5&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M277.49,-103.73C277.79,-95.52 278.11,-86.86 278.41,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"281.92,-78.42 278.78,-68.3 274.92,-78.17 281.92,-78.42\"/>\n</g>\n<!-- 9 -->\n<g id=\"node10\" class=\"node\">\n<title>9</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M365.5,-298.5C365.5,-298.5 294.5,-298.5 294.5,-298.5 288.5,-298.5 282.5,-292.5 282.5,-286.5 282.5,-286.5 282.5,-242.5 282.5,-242.5 282.5,-236.5 288.5,-230.5 294.5,-230.5 294.5,-230.5 365.5,-230.5 365.5,-230.5 371.5,-230.5 377.5,-236.5 377.5,-242.5 377.5,-242.5 377.5,-286.5 377.5,-286.5 377.5,-292.5 371.5,-298.5 365.5,-298.5\"/>\n<text text-anchor=\"start\" x=\"302\" y=\"-283.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"292.5\" y=\"-268.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 7</text>\n<text text-anchor=\"start\" x=\"290.5\" y=\"-253.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 7]</text>\n<text text-anchor=\"start\" x=\"302.5\" y=\"-238.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 8&#45;&gt;9 -->\n<g id=\"edge9\" class=\"edge\">\n<title>8&#45;&gt;9</title>\n<path fill=\"none\" stroke=\"black\" d=\"M341.1,-341.91C339.55,-331.2 337.86,-319.62 336.29,-308.78\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"339.72,-308.06 334.82,-298.67 332.79,-309.07 339.72,-308.06\"/>\n</g>\n<!-- 10 -->\n<g id=\"node11\" class=\"node\">\n<title>10</title>\n<path fill=\"#d3e9f9\" stroke=\"black\" d=\"M500,-306C500,-306 408,-306 408,-306 402,-306 396,-300 396,-294 396,-294 396,-235 396,-235 396,-229 402,-223 408,-223 408,-223 500,-223 500,-223 506,-223 512,-229 512,-235 512,-235 512,-294 512,-294 512,-300 506,-306 500,-306\"/>\n<text text-anchor=\"start\" x=\"404\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 158.0</text>\n<text text-anchor=\"start\" x=\"418.5\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.492</text>\n<text text-anchor=\"start\" x=\"413\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 16</text>\n<text text-anchor=\"start\" x=\"414.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 9]</text>\n<text text-anchor=\"start\" x=\"426.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 8&#45;&gt;10 -->\n<g id=\"edge10\" class=\"edge\">\n<title>8&#45;&gt;10</title>\n<path fill=\"none\" stroke=\"black\" d=\"M384.12,-341.91C392.51,-332.74 401.48,-322.93 410.11,-313.49\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"412.78,-315.76 416.94,-306.02 407.61,-311.04 412.78,-315.76\"/>\n</g>\n<!-- 11 -->\n<g id=\"node12\" class=\"node\">\n<title>11</title>\n<path fill=\"#f2c09c\" stroke=\"black\" d=\"M481.5,-187C481.5,-187 410.5,-187 410.5,-187 404.5,-187 398.5,-181 398.5,-175 398.5,-175 398.5,-116 398.5,-116 398.5,-110 404.5,-104 410.5,-104 410.5,-104 481.5,-104 481.5,-104 487.5,-104 493.5,-110 493.5,-116 493.5,-116 493.5,-175 493.5,-175 493.5,-181 487.5,-187 481.5,-187\"/>\n<text text-anchor=\"start\" x=\"409\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 41.65</text>\n<text text-anchor=\"start\" x=\"410.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"408.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"406.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 3]</text>\n<text text-anchor=\"start\" x=\"418.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 10&#45;&gt;11 -->\n<g id=\"edge11\" class=\"edge\">\n<title>10&#45;&gt;11</title>\n<path fill=\"none\" stroke=\"black\" d=\"M451.22,-222.91C450.65,-214.56 450.05,-205.67 449.45,-197.02\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"452.94,-196.76 448.77,-187.02 445.96,-197.24 452.94,-196.76\"/>\n</g>\n<!-- 14 -->\n<g id=\"node15\" class=\"node\">\n<title>14</title>\n<path fill=\"#5aade9\" stroke=\"black\" d=\"M724,-187C724,-187 524,-187 524,-187 518,-187 512,-181 512,-175 512,-175 512,-116 512,-116 512,-110 518,-104 524,-104 524,-104 724,-104 724,-104 730,-104 736,-110 736,-116 736,-116 736,-175 736,-175 736,-181 730,-187 724,-187\"/>\n<text text-anchor=\"start\" x=\"520\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">DiabetesPedigreeFunction ≤ 0.334</text>\n<text text-anchor=\"start\" x=\"588.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.245</text>\n<text text-anchor=\"start\" x=\"586.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 7</text>\n<text text-anchor=\"start\" x=\"584.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 6]</text>\n<text text-anchor=\"start\" x=\"596.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 10&#45;&gt;14 -->\n<g id=\"edge14\" class=\"edge\">\n<title>10&#45;&gt;14</title>\n<path fill=\"none\" stroke=\"black\" d=\"M512.03,-223.56C526.34,-213.71 541.79,-203.08 556.51,-192.95\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"558.82,-195.61 565.08,-187.05 554.86,-189.84 558.82,-195.61\"/>\n</g>\n<!-- 12 -->\n<g id=\"node13\" class=\"node\">\n<title>12</title>\n<path fill=\"#e9965a\" stroke=\"black\" d=\"M430.5,-68C430.5,-68 359.5,-68 359.5,-68 353.5,-68 347.5,-62 347.5,-56 347.5,-56 347.5,-12 347.5,-12 347.5,-6 353.5,0 359.5,0 359.5,0 430.5,0 430.5,0 436.5,0 442.5,-6 442.5,-12 442.5,-12 442.5,-56 442.5,-56 442.5,-62 436.5,-68 430.5,-68\"/>\n<text text-anchor=\"start\" x=\"359.5\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.245</text>\n<text text-anchor=\"start\" x=\"357.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 7</text>\n<text text-anchor=\"start\" x=\"355.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 1]</text>\n<text text-anchor=\"start\" x=\"367.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 11&#45;&gt;12 -->\n<g id=\"edge12\" class=\"edge\">\n<title>11&#45;&gt;12</title>\n<path fill=\"none\" stroke=\"black\" d=\"M427.01,-103.73C423.02,-95.15 418.79,-86.09 414.77,-77.46\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"417.9,-75.89 410.51,-68.3 411.56,-78.84 417.9,-75.89\"/>\n</g>\n<!-- 13 -->\n<g id=\"node14\" class=\"node\">\n<title>13</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M543.5,-68C543.5,-68 472.5,-68 472.5,-68 466.5,-68 460.5,-62 460.5,-56 460.5,-56 460.5,-12 460.5,-12 460.5,-6 466.5,0 472.5,0 472.5,0 543.5,0 543.5,0 549.5,0 555.5,-6 555.5,-12 555.5,-12 555.5,-56 555.5,-56 555.5,-62 549.5,-68 543.5,-68\"/>\n<text text-anchor=\"start\" x=\"480\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"470.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 2</text>\n<text text-anchor=\"start\" x=\"468.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 2]</text>\n<text text-anchor=\"start\" x=\"480.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 11&#45;&gt;13 -->\n<g id=\"edge13\" class=\"edge\">\n<title>11&#45;&gt;13</title>\n<path fill=\"none\" stroke=\"black\" d=\"M469.09,-103.73C473.99,-95.06 479.18,-85.9 484.12,-77.18\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"487.26,-78.73 489.15,-68.3 481.17,-75.28 487.26,-78.73\"/>\n</g>\n<!-- 15 -->\n<g id=\"node16\" class=\"node\">\n<title>15</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M657.5,-68C657.5,-68 586.5,-68 586.5,-68 580.5,-68 574.5,-62 574.5,-56 574.5,-56 574.5,-12 574.5,-12 574.5,-6 580.5,0 586.5,0 586.5,0 657.5,0 657.5,0 663.5,0 669.5,-6 669.5,-12 669.5,-12 669.5,-56 669.5,-56 669.5,-62 663.5,-68 657.5,-68\"/>\n<text text-anchor=\"start\" x=\"594\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"584.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"582.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 0]</text>\n<text text-anchor=\"start\" x=\"594.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 14&#45;&gt;15 -->\n<g id=\"edge15\" class=\"edge\">\n<title>14&#45;&gt;15</title>\n<path fill=\"none\" stroke=\"black\" d=\"M623.26,-103.73C623.11,-95.52 622.95,-86.86 622.8,-78.56\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"626.29,-78.23 622.61,-68.3 619.29,-78.36 626.29,-78.23\"/>\n</g>\n<!-- 16 -->\n<g id=\"node17\" class=\"node\">\n<title>16</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M770.5,-68C770.5,-68 699.5,-68 699.5,-68 693.5,-68 687.5,-62 687.5,-56 687.5,-56 687.5,-12 687.5,-12 687.5,-6 693.5,0 699.5,0 699.5,0 770.5,0 770.5,0 776.5,0 782.5,-6 782.5,-12 782.5,-12 782.5,-56 782.5,-56 782.5,-62 776.5,-68 770.5,-68\"/>\n<text text-anchor=\"start\" x=\"707\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"697.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"695.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 6]</text>\n<text text-anchor=\"start\" x=\"707.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 14&#45;&gt;16 -->\n<g id=\"edge16\" class=\"edge\">\n<title>14&#45;&gt;16</title>\n<path fill=\"none\" stroke=\"black\" d=\"M665.33,-103.73C674.67,-94.51 684.58,-84.74 693.91,-75.53\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"696.58,-77.81 701.24,-68.3 691.67,-72.83 696.58,-77.81\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3b1cad10>"]}, "metadata": {}}]}, {"cell_type": "code", "source": ["path = cart.cost_complexity_pruning_path(x_train, y_train)\n", "ccp_alphas, impurities = path.ccp_alphas, path.impurities\n", "\n", "clfs = []\n", "for ccp_alpha in ccp_alphas:\n", "    clf = DecisionTreeClassifier(random_state=27, ccp_alpha=ccp_alpha)\n", "    clf.fit(x_train, y_train)\n", "    clfs.append(clf)\n", "accuracy_scores = [accuracy_score(y_test, clf.predict(x_test)) for clf in clfs]\n", "best_alpha = ccp_alphas[accuracy_scores.index(max(accuracy_scores))]\n", "cart_postpruned = DecisionTreeClassifier(random_state=29, ccp_alpha=best_alpha)\n", "cart_postpruned.fit(x_train, y_train)\n", "y_pred_cart_postpruned = cart_postpruned.predict(x_test)\n", "accuracy_cart_postpruned = accuracy_score(y_test, y_pred_cart_postpruned)\n", "print(f\"Post-pruned CART Accuracy: {accuracy_cart_postpruned:.2f}\")\n", "display_tree(cart_postpruned, x.columns)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "dHDOgUWB4gF7", "outputId": "9772b12a-9538-4c25-af27-cdde61bf7f04"}, "execution_count": 43, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Post-pruned CART Accuracy: 0.75\n"]}, {"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"680pt\" height=\"790pt\"\n viewBox=\"0.00 0.00 679.50 790.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 786)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-786 675.5,-786 675.5,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#fefaf7\" stroke=\"black\" d=\"M402,-782C402,-782 310,-782 310,-782 304,-782 298,-776 298,-770 298,-770 298,-711 298,-711 298,-705 304,-699 310,-699 310,-699 402,-699 402,-699 408,-699 414,-705 414,-711 414,-711 414,-770 414,-770 414,-776 408,-782 402,-782\"/>\n<text text-anchor=\"start\" x=\"306\" y=\"-766.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 117.5</text>\n<text text-anchor=\"start\" x=\"328\" y=\"-751.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.5</text>\n<text text-anchor=\"start\" x=\"315\" y=\"-736.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"309\" y=\"-721.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [24, 23]</text>\n<text text-anchor=\"start\" x=\"328.5\" y=\"-706.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#f0b58b\" stroke=\"black\" d=\"M306,-663C306,-663 228,-663 228,-663 222,-663 216,-657 216,-651 216,-651 216,-592 216,-592 216,-586 222,-580 228,-580 228,-580 306,-580 306,-580 312,-580 318,-586 318,-592 318,-592 318,-651 318,-651 318,-657 312,-663 306,-663\"/>\n<text text-anchor=\"start\" x=\"230\" y=\"-647.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 28.85</text>\n<text text-anchor=\"start\" x=\"231.5\" y=\"-632.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.413</text>\n<text text-anchor=\"start\" x=\"226\" y=\"-617.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 24</text>\n<text text-anchor=\"start\" x=\"224\" y=\"-602.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [17, 7]</text>\n<text text-anchor=\"start\" x=\"239.5\" y=\"-587.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M325.12,-698.91C318.29,-689.92 310.98,-680.32 303.93,-671.05\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"306.66,-668.86 297.82,-663.02 301.09,-673.1 306.66,-668.86\"/>\n<text text-anchor=\"middle\" x=\"294.46\" y=\"-684.09\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 10 -->\n<g id=\"node11\" class=\"node\">\n<title>10</title>\n<path fill=\"#90c8f0\" stroke=\"black\" d=\"M485,-663C485,-663 407,-663 407,-663 401,-663 395,-657 395,-651 395,-651 395,-592 395,-592 395,-586 401,-580 407,-580 407,-580 485,-580 485,-580 491,-580 497,-586 497,-592 497,-592 497,-651 497,-651 497,-657 491,-663 485,-663\"/>\n<text text-anchor=\"start\" x=\"413\" y=\"-647.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 47.5</text>\n<text text-anchor=\"start\" x=\"410.5\" y=\"-632.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.423</text>\n<text text-anchor=\"start\" x=\"405\" y=\"-617.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 23</text>\n<text text-anchor=\"start\" x=\"403\" y=\"-602.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 16]</text>\n<text text-anchor=\"start\" x=\"418.5\" y=\"-587.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 0&#45;&gt;10 -->\n<g id=\"edge10\" class=\"edge\">\n<title>0&#45;&gt;10</title>\n<path fill=\"none\" stroke=\"black\" d=\"M387.23,-698.91C394.14,-689.92 401.53,-680.32 408.65,-671.05\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"411.51,-673.08 414.83,-663.02 405.96,-668.81 411.51,-673.08\"/>\n<text text-anchor=\"middle\" x=\"418.06\" y=\"-684.11\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M188,-536.5C188,-536.5 110,-536.5 110,-536.5 104,-536.5 98,-530.5 98,-524.5 98,-524.5 98,-480.5 98,-480.5 98,-474.5 104,-468.5 110,-468.5 110,-468.5 188,-468.5 188,-468.5 194,-468.5 200,-474.5 200,-480.5 200,-480.5 200,-524.5 200,-524.5 200,-530.5 194,-536.5 188,-536.5\"/>\n<text text-anchor=\"start\" x=\"121\" y=\"-521.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"108\" y=\"-506.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 11</text>\n<text text-anchor=\"start\" x=\"106\" y=\"-491.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [11, 0]</text>\n<text text-anchor=\"start\" x=\"121.5\" y=\"-476.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 1&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>1&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M226.06,-579.91C214.26,-568.21 201.4,-555.46 189.62,-543.78\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"192.02,-541.22 182.45,-536.67 187.09,-546.19 192.02,-541.22\"/>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#e3f1fb\" stroke=\"black\" d=\"M304,-544C304,-544 230,-544 230,-544 224,-544 218,-538 218,-532 218,-532 218,-473 218,-473 218,-467 224,-461 230,-461 230,-461 304,-461 304,-461 310,-461 316,-467 316,-473 316,-473 316,-532 316,-532 316,-538 310,-544 304,-544\"/>\n<text text-anchor=\"start\" x=\"233.5\" y=\"-528.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 33.5</text>\n<text text-anchor=\"start\" x=\"231.5\" y=\"-513.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.497</text>\n<text text-anchor=\"start\" x=\"226\" y=\"-498.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 13</text>\n<text text-anchor=\"start\" x=\"227.5\" y=\"-483.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 7]</text>\n<text text-anchor=\"start\" x=\"239.5\" y=\"-468.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 1&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>1&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M267,-579.91C267,-571.65 267,-562.86 267,-554.3\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"270.5,-554.02 267,-544.02 263.5,-554.02 270.5,-554.02\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M173.5,-417.5C173.5,-417.5 102.5,-417.5 102.5,-417.5 96.5,-417.5 90.5,-411.5 90.5,-405.5 90.5,-405.5 90.5,-361.5 90.5,-361.5 90.5,-355.5 96.5,-349.5 102.5,-349.5 102.5,-349.5 173.5,-349.5 173.5,-349.5 179.5,-349.5 185.5,-355.5 185.5,-361.5 185.5,-361.5 185.5,-405.5 185.5,-405.5 185.5,-411.5 179.5,-417.5 173.5,-417.5\"/>\n<text text-anchor=\"start\" x=\"110\" y=\"-402.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"100.5\" y=\"-387.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"98.5\" y=\"-372.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 4]</text>\n<text text-anchor=\"start\" x=\"110.5\" y=\"-357.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 3&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>3&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M222.24,-460.91C209.23,-449.1 195.02,-436.22 182.05,-424.45\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"184.33,-421.79 174.57,-417.67 179.62,-426.98 184.33,-421.79\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#f2c09c\" stroke=\"black\" d=\"M338,-425C338,-425 216,-425 216,-425 210,-425 204,-419 204,-413 204,-413 204,-354 204,-354 204,-348 210,-342 216,-342 216,-342 338,-342 338,-342 344,-342 350,-348 350,-354 350,-354 350,-413 350,-413 350,-419 344,-425 338,-425\"/>\n<text text-anchor=\"start\" x=\"212\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BloodPressure ≤ 73.5</text>\n<text text-anchor=\"start\" x=\"241.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"239.5\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"237.5\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 3]</text>\n<text text-anchor=\"start\" x=\"249.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 3&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>3&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M270.47,-460.91C271.18,-452.56 271.94,-443.67 272.68,-435.02\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"276.17,-435.28 273.54,-425.02 269.2,-434.69 276.17,-435.28\"/>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#bddef6\" stroke=\"black\" d=\"M212,-306C212,-306 12,-306 12,-306 6,-306 0,-300 0,-294 0,-294 0,-235 0,-235 0,-229 6,-223 12,-223 12,-223 212,-223 212,-223 218,-223 224,-229 224,-235 224,-235 224,-294 224,-294 224,-300 218,-306 212,-306\"/>\n<text text-anchor=\"start\" x=\"8\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">DiabetesPedigreeFunction ≤ 0.343</text>\n<text text-anchor=\"start\" x=\"80\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.48</text>\n<text text-anchor=\"start\" x=\"74.5\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 5</text>\n<text text-anchor=\"start\" x=\"72.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 3]</text>\n<text text-anchor=\"start\" x=\"84.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 5&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>5&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M219.75,-341.91C206.06,-332.2 191.34,-321.76 177.31,-311.81\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"179.33,-308.95 169.14,-306.02 175.28,-314.66 179.33,-308.95\"/>\n</g>\n<!-- 9 -->\n<g id=\"node10\" class=\"node\">\n<title>9</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M325.5,-298.5C325.5,-298.5 254.5,-298.5 254.5,-298.5 248.5,-298.5 242.5,-292.5 242.5,-286.5 242.5,-286.5 242.5,-242.5 242.5,-242.5 242.5,-236.5 248.5,-230.5 254.5,-230.5 254.5,-230.5 325.5,-230.5 325.5,-230.5 331.5,-230.5 337.5,-236.5 337.5,-242.5 337.5,-242.5 337.5,-286.5 337.5,-286.5 337.5,-292.5 331.5,-298.5 325.5,-298.5\"/>\n<text text-anchor=\"start\" x=\"262\" y=\"-283.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"252.5\" y=\"-268.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"250.5\" y=\"-253.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [4, 0]</text>\n<text text-anchor=\"start\" x=\"262.5\" y=\"-238.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 5&#45;&gt;9 -->\n<g id=\"edge9\" class=\"edge\">\n<title>5&#45;&gt;9</title>\n<path fill=\"none\" stroke=\"black\" d=\"M281.51,-341.91C282.7,-331.2 283.99,-319.62 285.19,-308.78\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"288.69,-308.99 286.31,-298.67 281.73,-308.22 288.69,-308.99\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M119.5,-179.5C119.5,-179.5 48.5,-179.5 48.5,-179.5 42.5,-179.5 36.5,-173.5 36.5,-167.5 36.5,-167.5 36.5,-123.5 36.5,-123.5 36.5,-117.5 42.5,-111.5 48.5,-111.5 48.5,-111.5 119.5,-111.5 119.5,-111.5 125.5,-111.5 131.5,-117.5 131.5,-123.5 131.5,-123.5 131.5,-167.5 131.5,-167.5 131.5,-173.5 125.5,-179.5 119.5,-179.5\"/>\n<text text-anchor=\"start\" x=\"56\" y=\"-164.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"46.5\" y=\"-149.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 2</text>\n<text text-anchor=\"start\" x=\"44.5\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [2, 0]</text>\n<text text-anchor=\"start\" x=\"56.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 6&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>6&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M102.29,-222.91C99.7,-212.09 96.89,-200.38 94.28,-189.44\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"97.67,-188.58 91.94,-179.67 90.86,-190.21 97.67,-188.58\"/>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M232.5,-179.5C232.5,-179.5 161.5,-179.5 161.5,-179.5 155.5,-179.5 149.5,-173.5 149.5,-167.5 149.5,-167.5 149.5,-123.5 149.5,-123.5 149.5,-117.5 155.5,-111.5 161.5,-111.5 161.5,-111.5 232.5,-111.5 232.5,-111.5 238.5,-111.5 244.5,-117.5 244.5,-123.5 244.5,-123.5 244.5,-167.5 244.5,-167.5 244.5,-173.5 238.5,-179.5 232.5,-179.5\"/>\n<text text-anchor=\"start\" x=\"169\" y=\"-164.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"159.5\" y=\"-149.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 3</text>\n<text text-anchor=\"start\" x=\"157.5\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 3]</text>\n<text text-anchor=\"start\" x=\"169.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 6&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>6&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M141.49,-222.91C149.75,-211.54 158.73,-199.18 167.01,-187.77\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"169.86,-189.81 172.9,-179.67 164.2,-185.7 169.86,-189.81\"/>\n</g>\n<!-- 11 -->\n<g id=\"node12\" class=\"node\">\n<title>11</title>\n<path fill=\"#d3e9f9\" stroke=\"black\" d=\"M546,-544C546,-544 346,-544 346,-544 340,-544 334,-538 334,-532 334,-532 334,-473 334,-473 334,-467 340,-461 346,-461 346,-461 546,-461 546,-461 552,-461 558,-467 558,-473 558,-473 558,-532 558,-532 558,-538 552,-544 546,-544\"/>\n<text text-anchor=\"start\" x=\"342\" y=\"-528.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">DiabetesPedigreeFunction ≤ 0.267</text>\n<text text-anchor=\"start\" x=\"410.5\" y=\"-513.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.492</text>\n<text text-anchor=\"start\" x=\"405\" y=\"-498.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 16</text>\n<text text-anchor=\"start\" x=\"406.5\" y=\"-483.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 9]</text>\n<text text-anchor=\"start\" x=\"418.5\" y=\"-468.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 10&#45;&gt;11 -->\n<g id=\"edge11\" class=\"edge\">\n<title>10&#45;&gt;11</title>\n<path fill=\"none\" stroke=\"black\" d=\"M446,-579.91C446,-571.65 446,-562.86 446,-554.3\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"449.5,-554.02 446,-544.02 442.5,-554.02 449.5,-554.02\"/>\n</g>\n<!-- 20 -->\n<g id=\"node21\" class=\"node\">\n<title>20</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M659.5,-536.5C659.5,-536.5 588.5,-536.5 588.5,-536.5 582.5,-536.5 576.5,-530.5 576.5,-524.5 576.5,-524.5 576.5,-480.5 576.5,-480.5 576.5,-474.5 582.5,-468.5 588.5,-468.5 588.5,-468.5 659.5,-468.5 659.5,-468.5 665.5,-468.5 671.5,-474.5 671.5,-480.5 671.5,-480.5 671.5,-524.5 671.5,-524.5 671.5,-530.5 665.5,-536.5 659.5,-536.5\"/>\n<text text-anchor=\"start\" x=\"596\" y=\"-521.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"586.5\" y=\"-506.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 7</text>\n<text text-anchor=\"start\" x=\"584.5\" y=\"-491.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 7]</text>\n<text text-anchor=\"start\" x=\"596.5\" y=\"-476.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 10&#45;&gt;20 -->\n<g id=\"edge20\" class=\"edge\">\n<title>10&#45;&gt;20</title>\n<path fill=\"none\" stroke=\"black\" d=\"M497.02,-589.09C518.76,-575.49 544.29,-559.23 567,-544 567.77,-543.48 568.55,-542.96 569.33,-542.43\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"571.51,-545.18 577.79,-536.64 567.56,-539.4 571.51,-545.18\"/>\n</g>\n<!-- 12 -->\n<g id=\"node13\" class=\"node\">\n<title>12</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M471.5,-417.5C471.5,-417.5 400.5,-417.5 400.5,-417.5 394.5,-417.5 388.5,-411.5 388.5,-405.5 388.5,-405.5 388.5,-361.5 388.5,-361.5 388.5,-355.5 394.5,-349.5 400.5,-349.5 400.5,-349.5 471.5,-349.5 471.5,-349.5 477.5,-349.5 483.5,-355.5 483.5,-361.5 483.5,-361.5 483.5,-405.5 483.5,-405.5 483.5,-411.5 477.5,-417.5 471.5,-417.5\"/>\n<text text-anchor=\"start\" x=\"408\" y=\"-402.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"398.5\" y=\"-387.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 4</text>\n<text text-anchor=\"start\" x=\"396.5\" y=\"-372.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 4]</text>\n<text text-anchor=\"start\" x=\"408.5\" y=\"-357.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 11&#45;&gt;12 -->\n<g id=\"edge12\" class=\"edge\">\n<title>11&#45;&gt;12</title>\n<path fill=\"none\" stroke=\"black\" d=\"M442.53,-460.91C441.62,-450.2 440.63,-438.62 439.7,-427.78\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"443.17,-427.33 438.83,-417.67 436.2,-427.93 443.17,-427.33\"/>\n</g>\n<!-- 13 -->\n<g id=\"node14\" class=\"node\">\n<title>13</title>\n<path fill=\"#f8dbc6\" stroke=\"black\" d=\"M588,-425C588,-425 514,-425 514,-425 508,-425 502,-419 502,-413 502,-413 502,-354 502,-354 502,-348 508,-342 514,-342 514,-342 588,-342 588,-342 594,-342 600,-348 600,-354 600,-354 600,-413 600,-413 600,-419 594,-425 588,-425\"/>\n<text text-anchor=\"start\" x=\"517.5\" y=\"-409.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">BMI ≤ 41.1</text>\n<text text-anchor=\"start\" x=\"515.5\" y=\"-394.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.486</text>\n<text text-anchor=\"start\" x=\"510\" y=\"-379.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 12</text>\n<text text-anchor=\"start\" x=\"511.5\" y=\"-364.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 5]</text>\n<text text-anchor=\"start\" x=\"523.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 11&#45;&gt;13 -->\n<g id=\"edge13\" class=\"edge\">\n<title>11&#45;&gt;13</title>\n<path fill=\"none\" stroke=\"black\" d=\"M482.43,-460.91C490.65,-451.74 499.46,-441.93 507.93,-432.49\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"510.56,-434.8 514.64,-425.02 505.35,-430.13 510.56,-434.8\"/>\n</g>\n<!-- 14 -->\n<g id=\"node15\" class=\"node\">\n<title>14</title>\n<path fill=\"#eca572\" stroke=\"black\" d=\"M511,-306C511,-306 419,-306 419,-306 413,-306 407,-300 407,-294 407,-294 407,-235 407,-235 407,-229 413,-223 419,-223 419,-223 511,-223 511,-223 517,-223 523,-229 523,-235 523,-235 523,-294 523,-294 523,-300 517,-306 511,-306\"/>\n<text text-anchor=\"start\" x=\"415\" y=\"-290.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Glucose ≤ 159.0</text>\n<text text-anchor=\"start\" x=\"429.5\" y=\"-275.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.346</text>\n<text text-anchor=\"start\" x=\"427.5\" y=\"-260.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"425.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [7, 2]</text>\n<text text-anchor=\"start\" x=\"437.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 13&#45;&gt;14 -->\n<g id=\"edge14\" class=\"edge\">\n<title>13&#45;&gt;14</title>\n<path fill=\"none\" stroke=\"black\" d=\"M521.16,-341.91C514.63,-333.01 507.64,-323.51 500.89,-314.33\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"503.53,-312.01 494.78,-306.02 497.89,-316.15 503.53,-312.01\"/>\n</g>\n<!-- 19 -->\n<g id=\"node20\" class=\"node\">\n<title>19</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M624.5,-298.5C624.5,-298.5 553.5,-298.5 553.5,-298.5 547.5,-298.5 541.5,-292.5 541.5,-286.5 541.5,-286.5 541.5,-242.5 541.5,-242.5 541.5,-236.5 547.5,-230.5 553.5,-230.5 553.5,-230.5 624.5,-230.5 624.5,-230.5 630.5,-230.5 636.5,-236.5 636.5,-242.5 636.5,-242.5 636.5,-286.5 636.5,-286.5 636.5,-292.5 630.5,-298.5 624.5,-298.5\"/>\n<text text-anchor=\"start\" x=\"561\" y=\"-283.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"551.5\" y=\"-268.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 3</text>\n<text text-anchor=\"start\" x=\"549.5\" y=\"-253.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 3]</text>\n<text text-anchor=\"start\" x=\"561.5\" y=\"-238.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 13&#45;&gt;19 -->\n<g id=\"edge19\" class=\"edge\">\n<title>13&#45;&gt;19</title>\n<path fill=\"none\" stroke=\"black\" d=\"M564.18,-341.91C567.7,-331.09 571.5,-319.38 575.05,-308.44\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"578.47,-309.26 578.23,-298.67 571.81,-307.1 578.47,-309.26\"/>\n</g>\n<!-- 15 -->\n<g id=\"node16\" class=\"node\">\n<title>15</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M400.5,-179.5C400.5,-179.5 329.5,-179.5 329.5,-179.5 323.5,-179.5 317.5,-173.5 317.5,-167.5 317.5,-167.5 317.5,-123.5 317.5,-123.5 317.5,-117.5 323.5,-111.5 329.5,-111.5 329.5,-111.5 400.5,-111.5 400.5,-111.5 406.5,-111.5 412.5,-117.5 412.5,-123.5 412.5,-123.5 412.5,-167.5 412.5,-167.5 412.5,-173.5 406.5,-179.5 400.5,-179.5\"/>\n<text text-anchor=\"start\" x=\"337\" y=\"-164.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"327.5\" y=\"-149.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"325.5\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [6, 0]</text>\n<text text-anchor=\"start\" x=\"337.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 14&#45;&gt;15 -->\n<g id=\"edge15\" class=\"edge\">\n<title>14&#45;&gt;15</title>\n<path fill=\"none\" stroke=\"black\" d=\"M430.31,-222.91C420.5,-211.43 409.82,-198.94 399.99,-187.44\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"402.51,-184.99 393.35,-179.67 397.18,-189.54 402.51,-184.99\"/>\n</g>\n<!-- 16 -->\n<g id=\"node17\" class=\"node\">\n<title>16</title>\n<path fill=\"#9ccef2\" stroke=\"black\" d=\"M563.5,-187C563.5,-187 442.5,-187 442.5,-187 436.5,-187 430.5,-181 430.5,-175 430.5,-175 430.5,-116 430.5,-116 430.5,-110 436.5,-104 442.5,-104 442.5,-104 563.5,-104 563.5,-104 569.5,-104 575.5,-110 575.5,-116 575.5,-116 575.5,-175 575.5,-175 575.5,-181 569.5,-187 563.5,-187\"/>\n<text text-anchor=\"start\" x=\"438.5\" y=\"-171.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">SkinThickness ≤ 12.5</text>\n<text text-anchor=\"start\" x=\"467.5\" y=\"-156.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.444</text>\n<text text-anchor=\"start\" x=\"465.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 3</text>\n<text text-anchor=\"start\" x=\"463.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 2]</text>\n<text text-anchor=\"start\" x=\"475.5\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 14&#45;&gt;16 -->\n<g id=\"edge16\" class=\"edge\">\n<title>14&#45;&gt;16</title>\n<path fill=\"none\" stroke=\"black\" d=\"M478.18,-222.91C480.93,-214.47 483.84,-205.48 486.68,-196.74\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"490.08,-197.61 489.84,-187.02 483.42,-195.45 490.08,-197.61\"/>\n</g>\n<!-- 17 -->\n<g id=\"node18\" class=\"node\">\n<title>17</title>\n<path fill=\"#399de5\" stroke=\"black\" d=\"M481.5,-68C481.5,-68 410.5,-68 410.5,-68 404.5,-68 398.5,-62 398.5,-56 398.5,-56 398.5,-12 398.5,-12 398.5,-6 404.5,0 410.5,0 410.5,0 481.5,0 481.5,0 487.5,0 493.5,-6 493.5,-12 493.5,-12 493.5,-56 493.5,-56 493.5,-62 487.5,-68 481.5,-68\"/>\n<text text-anchor=\"start\" x=\"418\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"408.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 2</text>\n<text text-anchor=\"start\" x=\"406.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [0, 2]</text>\n<text text-anchor=\"start\" x=\"418.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 1</text>\n</g>\n<!-- 16&#45;&gt;17 -->\n<g id=\"edge17\" class=\"edge\">\n<title>16&#45;&gt;17</title>\n<path fill=\"none\" stroke=\"black\" d=\"M481.78,-103.73C477.26,-95.06 472.49,-85.9 467.96,-77.18\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"471.06,-75.55 463.33,-68.3 464.85,-78.79 471.06,-75.55\"/>\n</g>\n<!-- 18 -->\n<g id=\"node19\" class=\"node\">\n<title>18</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M594.5,-68C594.5,-68 523.5,-68 523.5,-68 517.5,-68 511.5,-62 511.5,-56 511.5,-56 511.5,-12 511.5,-12 511.5,-6 517.5,0 523.5,0 523.5,0 594.5,0 594.5,0 600.5,0 606.5,-6 606.5,-12 606.5,-12 606.5,-56 606.5,-56 606.5,-62 600.5,-68 594.5,-68\"/>\n<text text-anchor=\"start\" x=\"531\" y=\"-52.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">gini = 0.0</text>\n<text text-anchor=\"start\" x=\"521.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"519.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = [1, 0]</text>\n<text text-anchor=\"start\" x=\"531.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">class = 0</text>\n</g>\n<!-- 16&#45;&gt;18 -->\n<g id=\"edge18\" class=\"edge\">\n<title>16&#45;&gt;18</title>\n<path fill=\"none\" stroke=\"black\" d=\"M523.85,-103.73C528.24,-95.15 532.87,-86.09 537.29,-77.46\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"540.53,-78.8 541.97,-68.3 534.3,-75.61 540.53,-78.8\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3a801570>"]}, "metadata": {}}]}, {"cell_type": "code", "source": ["X_reg = df[['Age', 'Insulin']]\n", "y_reg = df['Outcome']\n", "X_train_reg, X_test_reg, y_train_reg, y_test_reg = train_test_split(X_reg, y_reg, test_size=0.2, random_state=32)\n", "regressor = DecisionTreeRegressor(max_depth=3, random_state=27)\n", "regressor.fit(X_train_reg, y_train_reg)\n", "y_pred_reg = regressor.predict(X_test_reg)\n", "mse = mean_squared_error(y_test_reg, y_pred_reg)\n", "r2 = r2_score(y_test_reg, y_pred_reg)\n", "print(f\"Regression Mean Squared Error: {mse:.2f}\")\n", "print(f\"Regression R^2 Score: {r2:.2f}\")\n", "dot_data = tree.export_graphviz(regressor, out_file=None,\n", "                                feature_names=X_reg.columns,\n", "                                filled=True, rounded=True,\n", "                                special_characters=True)\n", "graph = graphviz.Source(dot_data)\n", "display(graph)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 554}, "id": "1Vig-lYp4r14", "outputId": "a668e889-2287-430d-db90-6a52992f0cfd"}, "execution_count": 44, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Regression Mean Squared Error: 0.39\n", "Regression R^2 Score: -0.77\n"]}, {"output_type": "display_data", "data": {"image/svg+xml": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n<!-- Generated by graphviz version 2.43.0 (0)\n -->\n<!-- Title: Tree Pages: 1 -->\n<svg width=\"625pt\" height=\"373pt\"\n viewBox=\"0.00 0.00 625.00 373.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 369)\">\n<title>Tree</title>\n<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-369 621,-369 621,4 -4,4\"/>\n<!-- 0 -->\n<g id=\"node1\" class=\"node\">\n<title>0</title>\n<path fill=\"#f2c19e\" stroke=\"black\" d=\"M371.5,-365C371.5,-365 252.5,-365 252.5,-365 246.5,-365 240.5,-359 240.5,-353 240.5,-353 240.5,-309 240.5,-309 240.5,-303 246.5,-297 252.5,-297 252.5,-297 371.5,-297 371.5,-297 377.5,-297 383.5,-303 383.5,-309 383.5,-309 383.5,-353 383.5,-353 383.5,-359 377.5,-365 371.5,-365\"/>\n<text text-anchor=\"start\" x=\"271.5\" y=\"-349.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 37.068</text>\n<text text-anchor=\"start\" x=\"248.5\" y=\"-334.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.25</text>\n<text text-anchor=\"start\" x=\"271\" y=\"-319.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 47</text>\n<text text-anchor=\"start\" x=\"271\" y=\"-304.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.489</text>\n</g>\n<!-- 1 -->\n<g id=\"node2\" class=\"node\">\n<title>1</title>\n<path fill=\"#f5cdb1\" stroke=\"black\" d=\"M291.5,-261C291.5,-261 164.5,-261 164.5,-261 158.5,-261 152.5,-255 152.5,-249 152.5,-249 152.5,-205 152.5,-205 152.5,-199 158.5,-193 164.5,-193 164.5,-193 291.5,-193 291.5,-193 297.5,-193 303.5,-199 303.5,-205 303.5,-205 303.5,-249 303.5,-249 303.5,-255 297.5,-261 291.5,-261\"/>\n<text text-anchor=\"start\" x=\"187.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 12.568</text>\n<text text-anchor=\"start\" x=\"160.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.239</text>\n<text text-anchor=\"start\" x=\"187\" y=\"-215.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 38</text>\n<text text-anchor=\"start\" x=\"187\" y=\"-200.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.395</text>\n</g>\n<!-- 0&#45;&gt;1 -->\n<g id=\"edge1\" class=\"edge\">\n<title>0&#45;&gt;1</title>\n<path fill=\"none\" stroke=\"black\" d=\"M284.73,-296.88C277.47,-288.07 269.53,-278.43 261.96,-269.24\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"264.48,-266.79 255.42,-261.3 259.08,-271.24 264.48,-266.79\"/>\n<text text-anchor=\"middle\" x=\"253.01\" y=\"-282.48\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">True</text>\n</g>\n<!-- 6 -->\n<g id=\"node7\" class=\"node\">\n<title>6</title>\n<path fill=\"#e88f4f\" stroke=\"black\" d=\"M460.5,-261C460.5,-261 333.5,-261 333.5,-261 327.5,-261 321.5,-255 321.5,-249 321.5,-249 321.5,-205 321.5,-205 321.5,-199 327.5,-193 333.5,-193 333.5,-193 460.5,-193 460.5,-193 466.5,-193 472.5,-199 472.5,-205 472.5,-205 472.5,-249 472.5,-249 472.5,-255 466.5,-261 460.5,-261\"/>\n<text text-anchor=\"start\" x=\"356.5\" y=\"-245.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 47.568</text>\n<text text-anchor=\"start\" x=\"329.5\" y=\"-230.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.099</text>\n<text text-anchor=\"start\" x=\"359.5\" y=\"-215.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 9</text>\n<text text-anchor=\"start\" x=\"356\" y=\"-200.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.889</text>\n</g>\n<!-- 0&#45;&gt;6 -->\n<g id=\"edge6\" class=\"edge\">\n<title>0&#45;&gt;6</title>\n<path fill=\"none\" stroke=\"black\" d=\"M339.6,-296.88C346.95,-288.07 354.97,-278.43 362.64,-269.24\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"365.54,-271.22 369.25,-261.3 360.16,-266.74 365.54,-271.22\"/>\n<text text-anchor=\"middle\" x=\"371.51\" y=\"-282.5\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">False</text>\n</g>\n<!-- 2 -->\n<g id=\"node3\" class=\"node\">\n<title>2</title>\n<path fill=\"#ffffff\" stroke=\"black\" d=\"M124,-149.5C124,-149.5 12,-149.5 12,-149.5 6,-149.5 0,-143.5 0,-137.5 0,-137.5 0,-108.5 0,-108.5 0,-102.5 6,-96.5 12,-96.5 12,-96.5 124,-96.5 124,-96.5 130,-96.5 136,-102.5 136,-108.5 136,-108.5 136,-137.5 136,-137.5 136,-143.5 130,-149.5 124,-149.5\"/>\n<text text-anchor=\"start\" x=\"8\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.0</text>\n<text text-anchor=\"start\" x=\"30.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 6</text>\n<text text-anchor=\"start\" x=\"34.5\" y=\"-104.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.0</text>\n</g>\n<!-- 1&#45;&gt;2 -->\n<g id=\"edge2\" class=\"edge\">\n<title>1&#45;&gt;2</title>\n<path fill=\"none\" stroke=\"black\" d=\"M176.05,-192.88C156.9,-180.68 135.3,-166.9 116.47,-154.9\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"118.35,-151.94 108.03,-149.52 114.58,-157.85 118.35,-151.94\"/>\n</g>\n<!-- 3 -->\n<g id=\"node4\" class=\"node\">\n<title>3</title>\n<path fill=\"#f3c4a2\" stroke=\"black\" d=\"M293.5,-157C293.5,-157 166.5,-157 166.5,-157 160.5,-157 154.5,-151 154.5,-145 154.5,-145 154.5,-101 154.5,-101 154.5,-95 160.5,-89 166.5,-89 166.5,-89 293.5,-89 293.5,-89 299.5,-89 305.5,-95 305.5,-101 305.5,-101 305.5,-145 305.5,-145 305.5,-151 299.5,-157 293.5,-157\"/>\n<text text-anchor=\"start\" x=\"189.5\" y=\"-141.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">Age ≤ 13.568</text>\n<text text-anchor=\"start\" x=\"162.5\" y=\"-126.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.249</text>\n<text text-anchor=\"start\" x=\"189\" y=\"-111.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 32</text>\n<text text-anchor=\"start\" x=\"189\" y=\"-96.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.469</text>\n</g>\n<!-- 1&#45;&gt;3 -->\n<g id=\"edge3\" class=\"edge\">\n<title>1&#45;&gt;3</title>\n<path fill=\"none\" stroke=\"black\" d=\"M228.65,-192.88C228.81,-184.78 228.98,-175.98 229.15,-167.47\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"232.65,-167.37 229.35,-157.3 225.65,-167.23 232.65,-167.37\"/>\n</g>\n<!-- 4 -->\n<g id=\"node5\" class=\"node\">\n<title>4</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M205,-53C205,-53 93,-53 93,-53 87,-53 81,-47 81,-41 81,-41 81,-12 81,-12 81,-6 87,0 93,0 93,0 205,0 205,0 211,0 217,-6 217,-12 217,-12 217,-41 217,-41 217,-47 211,-53 205,-53\"/>\n<text text-anchor=\"start\" x=\"89\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.0</text>\n<text text-anchor=\"start\" x=\"111.5\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"115.5\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 1.0</text>\n</g>\n<!-- 3&#45;&gt;4 -->\n<g id=\"edge4\" class=\"edge\">\n<title>3&#45;&gt;4</title>\n<path fill=\"none\" stroke=\"black\" d=\"M201.67,-88.95C193.91,-79.89 185.49,-70.07 177.7,-60.99\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"180.23,-58.55 171.06,-53.24 174.91,-63.11 180.23,-58.55\"/>\n</g>\n<!-- 5 -->\n<g id=\"node6\" class=\"node\">\n<title>5</title>\n<path fill=\"#f3c6a6\" stroke=\"black\" d=\"M374.5,-53C374.5,-53 247.5,-53 247.5,-53 241.5,-53 235.5,-47 235.5,-41 235.5,-41 235.5,-12 235.5,-12 235.5,-6 241.5,0 247.5,0 247.5,0 374.5,0 374.5,0 380.5,0 386.5,-6 386.5,-12 386.5,-12 386.5,-41 386.5,-41 386.5,-47 380.5,-53 374.5,-53\"/>\n<text text-anchor=\"start\" x=\"243.5\" y=\"-37.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.248</text>\n<text text-anchor=\"start\" x=\"270\" y=\"-22.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 31</text>\n<text text-anchor=\"start\" x=\"270\" y=\"-7.8\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.452</text>\n</g>\n<!-- 3&#45;&gt;5 -->\n<g id=\"edge5\" class=\"edge\">\n<title>3&#45;&gt;5</title>\n<path fill=\"none\" stroke=\"black\" d=\"M258.33,-88.95C266.09,-79.89 274.51,-70.07 282.3,-60.99\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"285.09,-63.11 288.94,-53.24 279.77,-58.55 285.09,-63.11\"/>\n</g>\n<!-- 7 -->\n<g id=\"node8\" class=\"node\">\n<title>7</title>\n<path fill=\"#e58139\" stroke=\"black\" d=\"M451,-149.5C451,-149.5 339,-149.5 339,-149.5 333,-149.5 327,-143.5 327,-137.5 327,-137.5 327,-108.5 327,-108.5 327,-102.5 333,-96.5 339,-96.5 339,-96.5 451,-96.5 451,-96.5 457,-96.5 463,-102.5 463,-108.5 463,-108.5 463,-137.5 463,-137.5 463,-143.5 457,-149.5 451,-149.5\"/>\n<text text-anchor=\"start\" x=\"335\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.0</text>\n<text text-anchor=\"start\" x=\"357.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 8</text>\n<text text-anchor=\"start\" x=\"361.5\" y=\"-104.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 1.0</text>\n</g>\n<!-- 6&#45;&gt;7 -->\n<g id=\"edge7\" class=\"edge\">\n<title>6&#45;&gt;7</title>\n<path fill=\"none\" stroke=\"black\" d=\"M396.35,-192.88C396.14,-182.22 395.91,-170.35 395.7,-159.52\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"399.2,-159.45 395.5,-149.52 392.2,-159.59 399.2,-159.45\"/>\n</g>\n<!-- 8 -->\n<g id=\"node9\" class=\"node\">\n<title>8</title>\n<path fill=\"#ffffff\" stroke=\"black\" d=\"M605,-149.5C605,-149.5 493,-149.5 493,-149.5 487,-149.5 481,-143.5 481,-137.5 481,-137.5 481,-108.5 481,-108.5 481,-102.5 487,-96.5 493,-96.5 493,-96.5 605,-96.5 605,-96.5 611,-96.5 617,-102.5 617,-108.5 617,-108.5 617,-137.5 617,-137.5 617,-143.5 611,-149.5 605,-149.5\"/>\n<text text-anchor=\"start\" x=\"489\" y=\"-134.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">squared_error = 0.0</text>\n<text text-anchor=\"start\" x=\"511.5\" y=\"-119.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">samples = 1</text>\n<text text-anchor=\"start\" x=\"515.5\" y=\"-104.3\" font-family=\"Helvetica,sans-Serif\" font-size=\"14.00\">value = 0.0</text>\n</g>\n<!-- 6&#45;&gt;8 -->\n<g id=\"edge8\" class=\"edge\">\n<title>6&#45;&gt;8</title>\n<path fill=\"none\" stroke=\"black\" d=\"M446.35,-192.88C464.38,-180.79 484.7,-167.15 502.47,-155.22\"/>\n<polygon fill=\"black\" stroke=\"black\" points=\"504.62,-158 510.97,-149.52 500.72,-152.19 504.62,-158\"/>\n</g>\n</g>\n</svg>\n", "text/plain": ["<graphviz.sources.Source at 0x7c2d3aa3bbe0>"]}, "metadata": {}}]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}