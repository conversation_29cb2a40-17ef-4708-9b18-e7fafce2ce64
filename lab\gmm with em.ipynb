{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["## Generate sample data\n", "#X, y = make_blobs(n_samples=150, centers=4, cluster_std=0.60, random_state=0)\n", "#\n", "## Initialize the Gaussian Mixture Model with 4 components\n", "#gmm = GaussianMixture(n_components=4, random_state=0)\n", "#\n", "## Fit the model to the data using the Expectation-Maximization (EM) algorithm\n", "#gmm.fit(X)\n", "#\n", "## Predict the cluster labels for each data point\n", "#labels = gmm.predict(X)\n", "#\n", "## Print the cluster labels\n", "#print(\"Cluster Labels:\", labels)\n", "#\n", "## You can also access the learned parameters like means and covariances\n", "#print(\"Means:\", gmm.means_)\n", "#print(\"Covariances:\", gmm.covariances_)\n", "#\n", "## Visualize the clusters (optional, requires matplotlib)\n", "#import matplotlib.pyplot as plt\n", "#\n", "#plt.scatter(X[:, 0], X[:, 1], c=labels, s=40, cmap='viridis')\n", "#plt.title('GMM Clustering')\n", "#plt.xlabel('Feature 1')\n", "#plt.ylabel('Feature 2')\n", "#plt.show()\n"], "metadata": {"id": "nBeDtEmNOmgf"}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "source": ["import numpy as np\n", "from sklearn.mixture import GaussianMixture\n", "from sklearn.datasets import make_blobs\n", "from numpy import array, argmax\n", "import matplotlib.pyplot as plt\n", "from sklearn.cluster import KMeans\n", "from numpy.random import multivariate_normal as mvn_random\n", "from scipy.stats import multivariate_normal\n", "from numpy.random import normal, uniform\n", "from scipy.stats import norm"], "metadata": {"id": "L2YmucdVO0Dk"}, "execution_count": 2, "outputs": []}, {"cell_type": "markdown", "source": [], "metadata": {"id": "hqrSAL-eJclN"}}, {"cell_type": "code", "source": ["maiasaura = lambda x : .5 * norm.pdf(x, 7, .3) + .5 * norm.pdf(x, 8, .3)\n", "stegosaurus = lambda x : norm.pdf(x, 4, .3)\n", "ankylosaurus = lambda x : norm.pdf(x, 5, .5)\n", "trex = lambda x : norm.pdf(x, 10, 1.5)\n", "raptor = lambda x : norm.pdf(x, .7, .2)\n", "sauropod = lambda x : norm.pdf(x, 20, 3)\n", "\n", "x = np.arange(0, 30, .01)\n", "plt.plot(x, .1 * raptor(x) + .25 * sauropod(x) + .05 * trex(x) + .3 * stegosaurus(x) + .15 * ankylosaurus(x) + .15 * maiasaura(x), color='blue')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 447}, "id": "qSeOz33WJdXF", "outputId": "15e2461b-127c-4cd4-d5cf-12fe2270c95c"}, "execution_count": 3, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x7ca9602bd9c0>]"]}, "metadata": {}, "execution_count": 3}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["class Component:\n", "    def __init__(self, mixture_prop, mean, variance):\n", "        self.mixture_prop = mixture_prop\n", "        self.mean = mean\n", "        self.variance = variance\n", "\n", "def generate_gmm_dataset(gmm_params, sample_size):\n", "    def get_random_component(gmm_params):\n", "        r = uniform()\n", "        for c in gmm_params:\n", "            r -= c.mixture_prop\n", "            if r <= 0:\n", "                return c\n", "\n", "    dataset = []\n", "    for _ in range(sample_size):\n", "        comp = get_random_component(gmm_params)\n", "        dataset += [[normal(comp.mean[0], comp.variance[0][0]), normal(comp.mean[1], comp.variance[1][1])]] # generate 2D data\n", "\n", "    return np.array(dataset) # Convert the list of lists to a numpy array\n", "\n", "gmm = [\n", "    Component(.25, [-3, 3], [[1, 0], [0, 1]]),\n", "    Component(.50, [0, 0], [[1, 0], [0, 1]]),\n", "    Component(.25, [3, 3], [[1, 0], [0, 1]])\n", "]\n", "# Assign a value to sample_size\n", "sample_size = 400\n", "data = generate_gmm_dataset(gmm, sample_size)"], "metadata": {"id": "E4znoEMpJhoU"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["def gmm_init(k, dataset):\n", "    kmeans = KMeans(k, init='k-means++').fit(dataset)\n", "    gmm_params = []\n", "\n", "    for j in range(k):\n", "        p_cj = sum([1 if kmeans.labels_[i] == j else 0 for i in range(len(dataset))]) / len(dataset)\n", "        mean_j = sum([dataset[i] for i in range(len(dataset)) if kmeans.labels_[i] == j]) / sum([1 if kmeans.labels_[i] == j else 0 for i in range(len(dataset))])\n", "        var_j = sum([(dataset[i] - mean_j).reshape(-1, 1) * (dataset[i] - mean_j).reshape(1, -1) for i in range(len(dataset)) if kmeans.labels_[i] == j]) / sum([1 if kmeans.labels_[i] == j else 0 for i in range(len(dataset))])\n", "        gmm_params.append(Component(p_cj, mean_j, var_j))\n", "\n", "    return gmm_params"], "metadata": {"id": "TnWEDrASJii0"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["def expectation_maximization(k, dataset, iterations):\n", "    gmm_params = gmm_init(k, dataset)\n", "\n", "    for _ in range(iterations):\n", "        # expectation step\n", "        probs = compute_probs(k, dataset, gmm_params)\n", "        # maximization step\n", "        gmm_params = compute_gmm(k, dataset, probs)\n", "\n", "    return probs, gmm_params"], "metadata": {"id": "fg74PxHMJlQh"}, "execution_count": 6, "outputs": []}, {"source": ["def expectation_maximization(k, dataset, iterations):\n", "    gmm_params = gmm_init(k, dataset)\n", "\n", "    for i in range(iterations):\n", "        # expectation step\n", "        probs = compute_probs(k, dataset, gmm_params)\n", "        # maximization step\n", "        gmm_params = compute_gmm(k, dataset, probs)\n", "\n", "        # Extract cluster labels, means, and covariances\n", "        cluster_labels = [argmax(array(p)) for p in probs]\n", "        cluster_means = [comp.mean for comp in gmm_params]\n", "        cluster_covariances = [comp.variance for comp in gmm_params]\n", "\n", "        # Print the results for the current iteration\n", "        print(f\"Iteration {i + 1}:\")\n", "        print(\"Cluster Labels:\", cluster_labels)\n", "        print(\"Cluster Means:\", cluster_means)\n", "        print(\"Cluster Covariances:\", cluster_covariances)\n", "        print(\"-\" * 20)  # Separator between iterations\n", "\n", "    return probs, gmm_params"], "cell_type": "code", "metadata": {"id": "lC3OR0nySAoB"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["def compute_gmm(k, dataset, probs):\n", "    gmm_params = []\n", "    for j in range(k):\n", "        p_cj = sum([probs[i][j] for i in range(len(dataset))]) / len(dataset)\n", "        mean_j = sum([probs[i][j] * dataset[i] for i in range(len(dataset))]) / sum([probs[i][j] for i in range(len(dataset))])\n", "        var_j = sum([probs[i][j] * (dataset[i] - mean_j).reshape(-1, 1) * (dataset[i] - mean_j).reshape(1, -1) for i in range(len(dataset))]) / sum([probs[i][j] for i in range(len(dataset))])\n", "        gmm_params.append(Component(p_cj, mean_j, var_j))\n", "\n", "    return gmm_params\n", "\n", "\n", "def compute_probs(k, dataset, gmm_params):\n", "    probs = []\n", "    for i in range(len(dataset)):\n", "        p_cj_xi = []\n", "        for j in range(k):\n", "            p_cj_xi += [gmm_params[j].mixture_prop * multivariate_normal.pdf(dataset[i], gmm_params[j].mean, gmm_params[j].variance)]\n", "        p_cj_xi = p_cj_xi / sum(p_cj_xi)\n", "        probs.append(p_cj_xi)\n", "\n", "    return probs"], "metadata": {"id": "QjR0t9TBJnT9"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["probs, gmm_p = expectation_maximization(3, data, 3)\n", "labels = [argmax(array(p)) for p in probs]\n", "size = 50 * array(probs).max(1) ** 2\n", "\n", "plt.scatter(data[:, 0], data[:, 1], c=labels, cmap='viridis', s=size)\n", "plt.title('GMM with {} clusters and {} samples'.format(3, sample_size))\n", "\n", "# Get cluster centers from gmm_p\n", "cluster_centers = [comp.mean for comp in gmm_p]\n", "\n", "# Plot cluster centers\n", "plt.scatter([c[0] for c in cluster_centers], [c[1] for c in cluster_centers], marker='x', s=200, c='red', label='Cluster Centers')\n", "\n", "plt.legend()\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 888}, "id": "GZRpuk7iPSH0", "outputId": "554b06cd-0e4d-4e14-9402-66a2028e47d8"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Iteration 1:\n", "Cluster Labels: [1, 0, 2, 1, 2, 1, 2, 2, 1, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 1, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 1, 2, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 1, 2, 2, 0, 1, 0, 1, 0, 1, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 1, 0, 2, 0, 1, 0, 0, 2, 1, 0, 1, 2, 1, 1, 0, 0, 0, 1, 0, 0, 0, 2, 1, 0, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 2, 0, 0, 1, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 2, 2, 0, 0, 1, 2, 2, 0, 2, 0, 2, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 2, 1, 1, 1, 1, 0, 1, 0, 2, 0, 0, 2, 0, 0, 0, 1, 1, 0, 0, 0, 2, 0, 1, 1, 2, 1, 0, 2, 0, 0, 2, 0, 0, 0, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 1, 0, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 2, 1, 0, 0, 0, 2, 2, 0, 1, 1, 1, 0, 2, 1, 2, 0, 0, 0, 2, 2, 0, 1, 2, 2, 1, 2, 1, 2, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 1, 2, 2, 1, 0, 0, 2, 0, 2, 0, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 2, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 1, 0, 0, 2, 0, 1, 1, 0, 0, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 1, 2, 0, 1, 0, 2, 2, 2, 0, 1, 0, 1, 2, 2, 1, 0, 0, 2, 0, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 1, 2, 1, 0, 0, 1, 1, 2, 0, 2, 2, 0, 0, 1, 1, 0, 2, 2, 0]\n", "Cluster Means: [array([-0.00349583, -0.02363774]), array([3.03585238, 2.85107294]), array([-2.99547342,  3.01311318])]\n", "Cluster Covariances: [array([[ 0.9507675 , -0.02952947],\n", "       [-0.02952947,  0.95885184]]), array([[1.11303311, 0.01140972],\n", "       [0.01140972, 1.22180809]]), array([[0.85681271, 0.02181019],\n", "       [0.02181019, 1.02664577]])]\n", "--------------------\n", "Iteration 2:\n", "Cluster Labels: [1, 0, 2, 1, 2, 1, 2, 2, 1, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 1, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 1, 2, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 1, 2, 2, 0, 1, 0, 1, 0, 1, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 1, 0, 2, 0, 1, 0, 0, 2, 1, 0, 1, 2, 1, 1, 0, 0, 0, 1, 0, 0, 0, 2, 1, 0, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 2, 0, 0, 1, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 2, 2, 0, 0, 1, 2, 2, 0, 2, 0, 2, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 2, 1, 1, 1, 1, 0, 1, 0, 2, 0, 0, 2, 0, 0, 0, 1, 1, 0, 0, 0, 2, 0, 1, 1, 2, 1, 0, 2, 0, 0, 2, 0, 0, 0, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 1, 0, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 2, 1, 0, 0, 0, 2, 2, 0, 1, 1, 1, 0, 2, 1, 2, 0, 0, 0, 2, 2, 0, 1, 2, 1, 1, 2, 1, 2, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 1, 2, 2, 1, 0, 0, 2, 0, 2, 0, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 2, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 1, 0, 0, 2, 0, 1, 1, 0, 0, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 1, 2, 0, 1, 0, 2, 2, 2, 0, 1, 0, 1, 2, 2, 1, 0, 0, 2, 0, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 1, 2, 1, 0, 0, 1, 1, 2, 0, 2, 2, 0, 0, 1, 1, 0, 2, 2, 0]\n", "Cluster Means: [array([-0.00026875, -0.01726198]), array([3.03601967, 2.85849379]), array([-3.00232703,  3.0087665 ])]\n", "Cluster Covariances: [array([[ 0.95665835, -0.02513482],\n", "       [-0.02513482,  0.97088427]]), array([[ 1.12485749, -0.00285412],\n", "       [-0.00285412,  1.22885405]]), array([[0.83921074, 0.01104879],\n", "       [0.01104879, 1.02927305]])]\n", "--------------------\n", "Iteration 3:\n", "Cluster Labels: [1, 0, 2, 1, 2, 1, 2, 2, 1, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 1, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 1, 2, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 1, 2, 2, 0, 1, 0, 1, 0, 1, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 1, 0, 2, 0, 1, 0, 0, 2, 1, 0, 1, 2, 1, 1, 0, 0, 0, 1, 0, 0, 0, 2, 1, 0, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 2, 0, 0, 1, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 2, 2, 0, 0, 1, 2, 2, 0, 2, 0, 2, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 2, 1, 1, 1, 1, 0, 1, 0, 2, 0, 0, 2, 0, 0, 0, 1, 1, 0, 0, 0, 2, 0, 1, 1, 2, 1, 0, 2, 0, 0, 2, 0, 0, 0, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 1, 0, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 2, 1, 0, 0, 0, 2, 2, 0, 1, 1, 1, 0, 2, 1, 2, 0, 0, 0, 2, 2, 0, 1, 2, 1, 1, 2, 1, 2, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 1, 2, 2, 1, 0, 0, 2, 0, 2, 0, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 2, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 1, 0, 0, 2, 0, 1, 1, 0, 0, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 1, 2, 0, 1, 0, 2, 2, 2, 0, 1, 0, 1, 2, 2, 1, 0, 0, 2, 0, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 1, 2, 1, 0, 0, 1, 1, 2, 0, 2, 2, 0, 0, 1, 1, 0, 2, 2, 0]\n", "Cluster Means: [array([ 0.00174254, -0.01467239]), array([3.03742855, 2.86431802]), array([-3.0056469 ,  3.00741459])]\n", "Cluster Covariances: [array([[ 0.95973896, -0.02316317],\n", "       [-0.02316317,  0.97482049]]), array([[ 1.12818973, -0.01265174],\n", "       [-0.01265174,  1.22591458]]), array([[0.83096298, 0.00748398],\n", "       [0.00748398, 1.02911882]])]\n", "--------------------\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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*************************************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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from matplotlib.patches import Ellipse\n", "\n", "# Assuming the output of the expectation_maximization function\n", "# 'probs' contains the probabilities for each data point for the clusters\n", "# 'gmm_p' contains the parameters of each cluster (e.g., means and covariances)\n", "\n", "# Example function to plot the ellipses around clusters\n", "def plot_ellipse(mean, cov, ax, **kwargs):\n", "    \"\"\"Plot an ellipse representing the covariance of the cluster.\"\"\"\n", "    # Get the eigenvalues and eigenvectors for the covariance matrix\n", "    eigenvalues, eigenvectors = np.linalg.eigh(cov)\n", "\n", "    # Calculate angle and widths (size of the ellipse)\n", "    angle = np.degrees(np.arctan2(*eigenvectors[0][::-1]))\n", "    width, height = 2 * np.sqrt(eigenvalues)\n", "\n", "    # Create and add the ellipse to the plot\n", "    ellipse = Ellipse(mean, width, height, angle, **kwargs)\n", "    ax.add_patch(ellipse)\n", "\n", "# Create labels and size for scatter plot based on probabilities\n", "labels = [np.argmax(p) for p in probs]  # Assign the most likely cluster to each point\n", "size = 50 * np.array(probs).max(1) ** 2  # Use probabilities to scale the size of points\n", "\n", "# Scatter plot the data points, colored by cluster labels\n", "plt.scatter(data[:, 0], data[:, 1], c=labels, cmap='viridis', s=size)\n", "plt.title('GMM with {} clusters and {} samples'.format(3, len(data)))\n", "\n", "# Get the cluster centers from gmm_p\n", "cluster_centers = [comp.mean for comp in gmm_p]\n", "covariances = [comp.variance for comp in gmm_p]\n", "\n", "# Plot ellipses around each cluster\n", "ax = plt.gca()\n", "for mean, cov in zip(cluster_centers, covariances):\n", "    plot_ellipse(mean[:2], cov[:2, :2], ax, edgecolor='red', linewidth=2, facecolor='none')\n", "\n", "# Add legend and display plot\n", "plt.legend(['Cluster Centers'])\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 507}, "id": "hcEKHqNbysW6", "outputId": "20f106e4-6eb7-4958-f9fa-6a293d57916d"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-10-aebb391fce54>:20: MatplotlibDeprecationWarning: Passing the angle parameter of __init__() positionally is deprecated since Matplotlib 3.6; the parameter will become keyword-only two minor releases later.\n", "  ellipse = Ellipse(mean, width, height, angle, **kwargs)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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**************************************************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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# prompt: find Cluster Labels,Means,covariances\n", "\n", "# Extract cluster labels, means, and covariances from the GMM parameters\n", "cluster_labels = [argmax(array(p)) for p in probs]\n", "cluster_means = [comp.mean for comp in gmm_p]\n", "cluster_covariances = [comp.variance for comp in gmm_p]\n", "\n", "# Print the results\n", "print(\"Cluster Labels:\", cluster_labels)\n", "print(\"Cluster Means:\", cluster_means)\n", "print(\"Cluster Covariances:\", cluster_covariances)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "to6migr7OFoO", "outputId": "ecd6dd34-7e93-4e90-a5bb-247d6d397b19"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cluster Labels: [1, 0, 2, 1, 2, 1, 2, 2, 1, 0, 2, 0, 2, 0, 2, 2, 2, 0, 0, 1, 1, 0, 0, 1, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 1, 2, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 2, 2, 2, 0, 0, 0, 0, 2, 2, 0, 0, 0, 2, 0, 0, 1, 2, 2, 0, 1, 0, 1, 0, 1, 2, 0, 0, 2, 0, 0, 0, 2, 0, 0, 0, 1, 0, 2, 0, 1, 0, 0, 2, 1, 0, 1, 2, 1, 1, 0, 0, 0, 1, 0, 0, 0, 2, 1, 0, 2, 2, 2, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 2, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 2, 2, 0, 0, 1, 0, 2, 2, 2, 0, 2, 2, 0, 0, 0, 2, 2, 0, 0, 1, 2, 2, 0, 2, 0, 2, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 2, 0, 0, 2, 1, 1, 1, 1, 0, 1, 0, 2, 0, 0, 2, 0, 0, 0, 1, 1, 0, 0, 0, 2, 0, 1, 1, 2, 1, 0, 2, 0, 0, 2, 0, 0, 0, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, 0, 1, 0, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 2, 1, 0, 0, 0, 2, 2, 0, 1, 1, 1, 0, 2, 1, 2, 0, 0, 0, 2, 2, 0, 1, 2, 1, 1, 2, 1, 2, 2, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 2, 2, 1, 2, 2, 1, 0, 0, 2, 0, 2, 0, 2, 1, 0, 1, 1, 0, 0, 0, 0, 0, 1, 1, 0, 0, 0, 2, 1, 0, 0, 2, 2, 1, 1, 0, 0, 0, 1, 0, 0, 2, 0, 1, 1, 0, 0, 0, 0, 2, 2, 0, 1, 0, 0, 0, 2, 0, 0, 0, 0, 2, 0, 1, 0, 1, 2, 0, 1, 0, 2, 2, 2, 0, 1, 0, 1, 2, 2, 1, 0, 0, 2, 0, 1, 1, 0, 0, 2, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2, 0, 0, 0, 0, 0, 0, 1, 2, 1, 0, 0, 1, 1, 2, 0, 2, 2, 0, 0, 1, 1, 0, 2, 2, 0]\n", "Cluster Means: [array([ 0.00174254, -0.01467239]), array([3.03742855, 2.86431802]), array([-3.0056469 ,  3.00741459])]\n", "Cluster Covariances: [array([[ 0.95973896, -0.02316317],\n", "       [-0.02316317,  0.97482049]]), array([[ 1.12818973, -0.01265174],\n", "       [-0.01265174,  1.22591458]]), array([[0.83096298, 0.00748398],\n", "       [0.00748398, 1.02911882]])]\n"]}]}, {"source": ["probs, gmm_p = expectation_maximization(3, data, 3)  # 3 clusters, your data, 3 iterations"], "cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AYOaZumJSDuv", "outputId": "4ac57299-a69b-46b0-8fe5-1e0bd92c7159"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Iteration 1:\n", "Cluster Labels: [0, 1, 2, 0, 2, 0, 2, 2, 0, 1, 2, 1, 2, 1, 2, 2, 2, 1, 1, 0, 0, 1, 1, 0, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 0, 2, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 2, 2, 2, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1, 1, 0, 2, 2, 1, 0, 1, 0, 1, 0, 2, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 0, 1, 2, 1, 0, 1, 1, 2, 0, 1, 0, 2, 0, 0, 1, 1, 1, 0, 1, 1, 1, 2, 0, 1, 2, 2, 2, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 2, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 2, 2, 1, 1, 0, 1, 2, 2, 2, 1, 2, 2, 1, 1, 1, 2, 2, 1, 1, 0, 2, 2, 1, 2, 1, 2, 0, 1, 0, 1, 1, 1, 1, 1, 2, 1, 2, 1, 1, 2, 0, 0, 0, 0, 1, 0, 1, 2, 1, 1, 2, 1, 1, 1, 0, 0, 1, 1, 1, 2, 1, 0, 0, 2, 0, 1, 2, 1, 1, 2, 1, 1, 1, 1, 2, 2, 1, 1, 2, 1, 1, 2, 1, 1, 0, 1, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 2, 0, 1, 1, 1, 2, 2, 1, 0, 0, 0, 1, 2, 0, 2, 1, 1, 1, 2, 2, 1, 0, 2, 2, 0, 2, 0, 2, 2, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 2, 2, 0, 2, 2, 0, 1, 1, 2, 1, 2, 1, 2, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 2, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 0, 1, 1, 2, 1, 0, 0, 1, 1, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 0, 1, 0, 2, 1, 0, 1, 2, 2, 2, 1, 0, 1, 0, 2, 2, 0, 1, 1, 2, 1, 0, 0, 1, 1, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 2, 1, 1, 1, 1, 1, 1, 0, 2, 0, 1, 1, 0, 0, 2, 1, 2, 2, 1, 1, 0, 0, 1, 2, 2, 1]\n", "Cluster Means: [array([3.03585238, 2.85107294]), array([-0.00349583, -0.02363774]), array([-2.99547342,  3.01311318])]\n", "Cluster Covariances: [array([[1.11303311, 0.01140972],\n", "       [0.01140972, 1.22180809]]), array([[ 0.9507675 , -0.02952947],\n", "       [-0.02952947,  0.95885184]]), array([[0.85681271, 0.02181019],\n", "       [0.02181019, 1.02664577]])]\n", "--------------------\n", "Iteration 2:\n", "Cluster Labels: [0, 1, 2, 0, 2, 0, 2, 2, 0, 1, 2, 1, 2, 1, 2, 2, 2, 1, 1, 0, 0, 1, 1, 0, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 0, 2, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 2, 2, 2, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1, 1, 0, 2, 2, 1, 0, 1, 0, 1, 0, 2, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 0, 1, 2, 1, 0, 1, 1, 2, 0, 1, 0, 2, 0, 0, 1, 1, 1, 0, 1, 1, 1, 2, 0, 1, 2, 2, 2, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 2, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 2, 2, 1, 1, 0, 1, 2, 2, 2, 1, 2, 2, 1, 1, 1, 2, 2, 1, 1, 0, 2, 2, 1, 2, 1, 2, 0, 1, 0, 1, 1, 1, 1, 1, 2, 1, 2, 1, 1, 2, 0, 0, 0, 0, 1, 0, 1, 2, 1, 1, 2, 1, 1, 1, 0, 0, 1, 1, 1, 2, 1, 0, 0, 2, 0, 1, 2, 1, 1, 2, 1, 1, 1, 1, 2, 2, 1, 1, 2, 1, 1, 2, 1, 1, 0, 1, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 2, 0, 1, 1, 1, 2, 2, 1, 0, 0, 0, 1, 2, 0, 2, 1, 1, 1, 2, 2, 1, 0, 2, 0, 0, 2, 0, 2, 2, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 2, 2, 0, 2, 2, 0, 1, 1, 2, 1, 2, 1, 2, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 2, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 0, 1, 1, 2, 1, 0, 0, 1, 1, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 0, 1, 0, 2, 1, 0, 1, 2, 2, 2, 1, 0, 1, 0, 2, 2, 0, 1, 1, 2, 1, 0, 0, 1, 1, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 2, 1, 1, 1, 1, 1, 1, 0, 2, 0, 1, 1, 0, 0, 2, 1, 2, 2, 1, 1, 0, 0, 1, 2, 2, 1]\n", "Cluster Means: [array([3.03601967, 2.85849379]), array([-0.00026875, -0.01726198]), array([-3.00232703,  3.0087665 ])]\n", "Cluster Covariances: [array([[ 1.12485749, -0.00285412],\n", "       [-0.00285412,  1.22885405]]), array([[ 0.95665835, -0.02513482],\n", "       [-0.02513482,  0.97088427]]), array([[0.83921074, 0.01104879],\n", "       [0.01104879, 1.02927305]])]\n", "--------------------\n", "Iteration 3:\n", "Cluster Labels: [0, 1, 2, 0, 2, 0, 2, 2, 0, 1, 2, 1, 2, 1, 2, 2, 2, 1, 1, 0, 0, 1, 1, 0, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 0, 2, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 1, 2, 2, 2, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1, 1, 0, 2, 2, 1, 0, 1, 0, 1, 0, 2, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 0, 1, 2, 1, 0, 1, 1, 2, 0, 1, 0, 2, 0, 0, 1, 1, 1, 0, 1, 1, 1, 2, 0, 1, 2, 2, 2, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 2, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 2, 2, 1, 1, 0, 1, 2, 2, 2, 1, 2, 2, 1, 1, 1, 2, 2, 1, 1, 0, 2, 2, 1, 2, 1, 2, 0, 1, 0, 1, 1, 1, 1, 1, 2, 1, 2, 1, 1, 2, 0, 0, 0, 0, 1, 0, 1, 2, 1, 1, 2, 1, 1, 1, 0, 0, 1, 1, 1, 2, 1, 0, 0, 2, 0, 1, 2, 1, 1, 2, 1, 1, 1, 1, 2, 2, 1, 1, 2, 1, 1, 2, 1, 1, 0, 1, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 2, 0, 1, 1, 1, 2, 2, 1, 0, 0, 0, 1, 2, 0, 2, 1, 1, 1, 2, 2, 1, 0, 2, 0, 0, 2, 0, 2, 2, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 2, 2, 0, 2, 2, 0, 1, 1, 2, 1, 2, 1, 2, 0, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 2, 0, 1, 1, 2, 2, 0, 0, 1, 1, 1, 0, 1, 1, 2, 1, 0, 0, 1, 1, 1, 1, 2, 2, 1, 0, 1, 1, 1, 2, 1, 1, 1, 1, 2, 1, 0, 1, 0, 2, 1, 0, 1, 2, 2, 2, 1, 0, 1, 0, 2, 2, 0, 1, 1, 2, 1, 0, 0, 1, 1, 2, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 2, 1, 1, 1, 1, 1, 1, 0, 2, 0, 1, 1, 0, 0, 2, 1, 2, 2, 1, 1, 0, 0, 1, 2, 2, 1]\n", "Cluster Means: [array([3.03742855, 2.86431802]), array([ 0.00174254, -0.01467239]), array([-3.0056469 ,  3.00741459])]\n", "Cluster Covariances: [array([[ 1.12818973, -0.01265174],\n", "       [-0.01265174,  1.22591458]]), array([[ 0.95973896, -0.02316317],\n", "       [-0.02316317,  0.97482049]]), array([[0.83096298, 0.00748398],\n", "       [0.00748398, 1.02911882]])]\n", "--------------------\n"]}]}]}