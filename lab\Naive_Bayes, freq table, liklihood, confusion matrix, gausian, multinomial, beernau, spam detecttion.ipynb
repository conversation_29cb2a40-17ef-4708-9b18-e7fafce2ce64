{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7ip_09WqQNTQ", "outputId": "f93c3436-6f99-429d-d04e-b49e0ae03619"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["          Team1     Team2     Margin        Ground  Match Date T-20 Int Match  \\\n", "0         India  Pakistan     5 runs  Johannesburg  2007/09/24      T20I # 46   \n", "1     Australia     India    15 runs        Durban  2007/09/22      T20I # 45   \n", "2   New Zealand  Pakistan  6 wickets     Cape Town  2007/09/22      T20I # 44   \n", "3  South Africa     India    37 runs        Durban  2007/09/20      T20I # 43   \n", "4    Bangladesh  Pakistan  4 wickets     Cape Town  2007/09/20      T20I # 42   \n", "\n", "     Winner  \n", "0     India  \n", "1     India  \n", "2  Pakistan  \n", "3     India  \n", "4  Pakistan  \n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.naive_bayes import GaussianNB\n", "from sklearn.metrics import classification_report, accuracy_score, confusion_matrix\n", "import seaborn as sns\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.naive_bayes import BernoulliNB\n", "from sklearn.naive_bayes import BernoulliNB\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "\n", "df = pd.read_csv('/content/all_t20_world_cup_matches_results.csv')\n", "print(df.head())\n"]}, {"cell_type": "code", "source": ["df.isnull().sum()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 304}, "id": "otWq0RtAcY5d", "outputId": "06f64c3c-85d0-47b4-c0df-90188a3736fb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["Team1              0\n", "Team2              0\n", "Margin            12\n", "Ground             0\n", "Match Date         0\n", "T-20 Int Match     0\n", "Winner             0\n", "dtype: int64"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Team1</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Team2</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>gin</th>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ground</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Match Date</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>T-20 Int Match</th>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Winner</th>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div><br><label><b>dtype:</b> int64</label>"]}, "metadata": {}, "execution_count": 3}]}, {"source": ["# Alternative solution for filling all null values with the mean for numerical columns only:\n", "for column in df.select_dtypes(include=['number']).columns:\n", "    df[column].fillna(df[column].mean(), inplace=True)\n"], "cell_type": "code", "metadata": {"id": "LuPQ0wQUc77j"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(df.isnull().sum())"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oYiErw4JdIAz", "outputId": "553facf8-ddaa-4b9f-8c40-7d2988adf056"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Team1             0\n", "Team2             0\n", "Margin            0\n", "Ground            0\n", "Match Date        0\n", "T-20 Int Match    0\n", "Winner            0\n", "dtype: int64\n"]}]}, {"cell_type": "code", "source": ["# prompt: create a Frequency table, Liklihood, confusion matrix for df\n", "\n", "# Frequency Table\n", "frequency_table = df['Winner'].value_counts()\n", "print(\"Frequency Table:\\n\", frequency_table)\n", "\n", "# Likelihood (Probability of each winner)\n", "likelihood = df['Winner'].value_counts(normalize=True)\n", "print(\"\\nLikelihood (Probability):\\n\", likelihood)\n", "\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BEx90yRPdNPA", "outputId": "6ad6da4a-e0eb-4f76-bb48-c2f0fe9965fb"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Frequency Table:\n", " Winner\n", "India           35\n", "South Africa    32\n", "Sri Lanka       32\n", "Australia       30\n", "Pakistan        30\n", "England         28\n", "New Zealand     25\n", "West Indies     24\n", "Afghanistan     12\n", "Bangladesh      12\n", "Netherlands     10\n", "Zimbabwe         8\n", "Scotland         7\n", "Ireland          7\n", "no result        7\n", "tied             5\n", "Namibia          4\n", "Nepal            2\n", "Oman             2\n", "Hong Kong        1\n", "U.A.E.           1\n", "Canada           1\n", "Uganda           1\n", "U.S.A.           1\n", "Name: count, dtype: int64\n", "\n", "Likelihood (Probability):\n", " Winner\n", "India           0.110410\n", "South Africa    0.100946\n", "Sri Lanka       0.100946\n", "Australia       0.094637\n", "Pakistan        0.094637\n", "England         0.088328\n", "New Zealand     0.078864\n", "West Indies     0.075710\n", "Afghanistan     0.037855\n", "Bangladesh      0.037855\n", "Netherlands     0.031546\n", "Zimbabwe        0.025237\n", "Scotland        0.022082\n", "Ireland         0.022082\n", "no result       0.022082\n", "tied            0.015773\n", "Namibia         0.012618\n", "Nepal           0.006309\n", "Oman            0.006309\n", "Hong Kong       0.003155\n", "U.A.E.          0.003155\n", "Canada          0.003155\n", "Uganda          0.003155\n", "U.S.A.          0.003155\n", "Name: proportion, dtype: float64\n"]}]}, {"cell_type": "code", "source": ["#Assuming 'Winner' and another column 'Predicted_Winner' for the confusion matrix\n", "# Replace 'Predicted_Winner' with the actual name of your prediction column if different\n", "# Create a 'Predicted_Winner' column (example - replace with your prediction logic)\n", "df['Predicted_Winner'] = np.random.choice(df['Winner'].unique(), size=len(df))\n", "# Confusion Matrix\n", "conf_matrix = confusion_matrix(df['Winner'], df['Predicted_Winner'])\n", "print(\"\\nConfusion Matrix:\\n\", conf_matrix)\n", "\n", "# Visualize the confusion matrix using a heatmap\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=df['Winner'].unique(), yticklabels=df['Winner'].unique())\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "oZRJcikWdjK5", "outputId": "1194df2d-98a8-408f-8171-5ace85474730"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Confusion Matrix:\n", " [[0 0 1 0 0 1 3 0 0 2 0 0 0 0 0 0 1 2 0 2 0 0 0 0]\n", " [2 2 1 1 0 2 2 0 2 1 1 1 0 0 0 1 1 2 2 1 1 2 3 2]\n", " [0 1 1 1 0 0 2 1 0 1 0 0 0 1 0 1 1 0 0 0 0 0 2 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0]\n", " [0 0 2 0 2 0 3 0 2 1 1 2 2 1 0 3 0 0 0 0 0 3 3 3]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1]\n", " [0 1 1 2 0 3 2 1 2 1 6 2 0 2 1 2 0 2 3 0 1 0 2 1]\n", " [0 0 1 1 0 1 0 0 1 1 0 0 1 1 0 0 0 0 0 0 0 0 0 0]\n", " [1 0 0 0 0 2 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1]\n", " [0 1 1 0 0 0 0 0 0 1 0 0 2 0 0 1 2 0 1 0 0 0 1 0]\n", " [2 1 2 1 1 1 0 1 1 1 1 0 1 2 0 1 2 1 0 4 0 0 0 2]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0]\n", " [1 2 4 2 1 2 1 3 0 0 3 1 0 0 0 1 2 1 1 1 2 0 1 1]\n", " [0 1 0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0 1 1 0 0 1 0]\n", " [1 1 4 0 5 2 1 2 0 0 2 2 0 0 3 0 0 2 2 1 2 1 1 0]\n", " [1 3 0 0 1 2 1 1 2 1 0 1 2 1 1 4 0 1 0 2 2 3 3 0]\n", " [0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0]\n", " [0 0 2 1 0 2 1 2 1 1 1 2 0 0 0 4 0 3 0 0 1 2 1 0]\n", " [0 0 0 0 1 0 1 0 1 1 1 1 1 0 0 0 0 0 0 0 1 0 0 0]\n", " [0 0 0 0 0 1 1 0 0 0 0 1 0 0 1 0 0 0 2 1 0 0 0 0]\n", " [0 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 1 0 1 0 0 0 1]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["**Gaussian NB**"], "metadata": {"id": "mMLJYvXrg3Id"}}, {"cell_type": "code", "source": ["# Feature Selection (replace with your actual relevant features)\n", "# Ensure column names match exactly (case-sensitive) and are present in the DataFrame\n", "features = ['Score_1', 'Score_2', 'Overs_1', 'Overs_2']\n", "\n", "X = df[features]\n", "y = df['Winner']\n", "\n", "# Handle non-numeric data in features if needed (e.g., one-hot encoding for categorical variables)\n", "# Example:\n", "# X = pd.get_dummies(X, columns=['Team1', 'Team2'], drop_first=True)\n", "\n", "# Split data into training and testing sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize and train the Gaussian Naive <PERSON> model\n", "gnb = GaussianNB()\n", "gnb.fit(X_train, y_train)\n", "\n", "# Make predictions on the test set\n", "y_pred = gnb.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy}\")\n", "print(classification_report(y_test, y_pred))\n", "\n", "\n", "# Confusion Matrix\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "print(\"\\nConfusion Matrix:\\n\", conf_matrix)\n", "\n", "# Visualize the confusion matrix using a heatmap\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=np.unique(y_pred), yticklabels=np.unique(y_test)) # Use unique labels\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 356}, "id": "cPQJBTvkdxhH", "outputId": "a5d5911d-c4f2-4201-cef2-aec14c617315"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "KeyError", "evalue": "\"None of [Index(['Score_1', 'Score_2', 'Overs_1', 'Overs_2'], dtype='object')] are in the [columns]\"", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-23-0a70da553813>\u001b[0m in \u001b[0;36m<cell line: 5>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mfeatures\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m'Score_1'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'Score_2'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'Overs_1'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'Overs_2'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 5\u001b[0;31m \u001b[0mX\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mfeatures\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      6\u001b[0m \u001b[0my\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdf\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'Winner'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      7\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36m__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   4106\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mis_iterator\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4107\u001b[0m                 \u001b[0mkey\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4108\u001b[0;31m             \u001b[0mindexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_get_indexer_strict\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"columns\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   4109\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4110\u001b[0m         \u001b[0;31m# take() does not accept boolean indexers\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36m_get_indexer_strict\u001b[0;34m(self, key, axis_name)\u001b[0m\n\u001b[1;32m   6198\u001b[0m             \u001b[0mkeyarr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnew_indexer\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_reindex_non_unique\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkeyarr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6199\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 6200\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_raise_if_missing\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkeyarr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindexer\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0maxis_name\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   6201\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6202\u001b[0m         \u001b[0mkeyarr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtake\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.10/dist-packages/pandas/core/indexes/base.py\u001b[0m in \u001b[0;36m_raise_if_missing\u001b[0;34m(self, key, indexer, axis_name)\u001b[0m\n\u001b[1;32m   6247\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mnmissing\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6248\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mnmissing\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindexer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 6249\u001b[0;31m                 \u001b[0;32mraise\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"None of [{key}] are in the [{axis_name}]\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   6250\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   6251\u001b[0m             \u001b[0mnot_found\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mlist\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mensure_index\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mkey\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mmissing_mask\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnonzero\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0munique\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: \"None of [Index(['Score_1', 'Score_2', 'Overs_1', 'Overs_2'], dtype='object')] are in the [columns]\""]}]}, {"source": ["# Feature Selection (replace with your actual relevant features)\n", "# Ensure column names match exactly (case-sensitive) and are present in the DataFrame\n", "# The original features were not found in the DataFrame.\n", "# Replacing with existing columns for demonstration:\n", "features = ['Team1', 'Team2', 'Winner']  # Example - Replace with actual relevant features from your DataFrame\n", "\n", "X = df[features]\n", "y = df['Winner']\n", "\n", "# Handle non-numeric data in features if needed (e.g., one-hot encoding for categorical variables)\n", "# Example:\n", "# X = pd.get_dummies(X, columns=['Team1', 'Team2'], drop_first=True)\n", "from sklearn.preprocessing import LabelEncoder\n", "# Initialize LabelEncoder\n", "label_encoder = LabelEncoder()\n", "\n", "# Iterate through each column in X and encode if it's of type 'object' (string)\n", "for col in X.select_dtypes(include=['object']).columns:\n", "    X[col] = label_encoder.fit_transform(X[col])\n", "\n", "# Split data into training and testing sets\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize and train the Gaussian Naive <PERSON> model\n", "gnb = GaussianNB()\n", "gnb.fit(X_train, y_train)\n", "\n", "# Make predictions on the test set\n", "y_pred = gnb.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy}\")\n", "print(classification_report(y_test, y_pred))"], "cell_type": "code", "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xy4A20Jcfv0u", "outputId": "cca3c8be-1622-4b1c-9be8-54a73665d815"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy: 0.984375\n", "              precision    recall  f1-score   support\n", "\n", " Afghanistan       1.00      1.00      1.00         2\n", "   Australia       1.00      1.00      1.00         6\n", "  Bangladesh       1.00      1.00      1.00         3\n", "     England       1.00      1.00      1.00         5\n", "       India       1.00      1.00      1.00         8\n", "     Ireland       1.00      1.00      1.00         2\n", "     Namibia       1.00      1.00      1.00         1\n", " Netherlands       1.00      1.00      1.00         2\n", " New Zealand       1.00      1.00      1.00         4\n", "    Pakistan       1.00      1.00      1.00        11\n", "    Scotland       1.00      1.00      1.00         2\n", "South Africa       1.00      1.00      1.00         4\n", "   Sri Lanka       0.86      1.00      0.92         6\n", "      U.A.E.       0.00      0.00      0.00         1\n", " West Indies       1.00      1.00      1.00         3\n", "    Zimbabwe       1.00      1.00      1.00         3\n", "        tied       1.00      1.00      1.00         1\n", "\n", "    accuracy                           0.98        64\n", "   macro avg       0.93      0.94      0.94        64\n", "weighted avg       0.97      0.98      0.98        64\n", "\n"]}, {"output_type": "stream", "name": "stderr", "text": ["<ipython-input-26-adb671b57006>:19: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-26-adb671b57006>:19: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-26-adb671b57006>:19: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}]}, {"cell_type": "code", "source": ["# Confusion Matrix\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "print(\"\\nConfusion Matrix:\\n\", conf_matrix)\n", "\n", "# Visualize the confusion matrix using a heatmap\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=np.unique(y_pred), yticklabels=np.unique(y_test)) # Use unique labels\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 971}, "id": "L2JDDe9dgDDW", "outputId": "62142476-fa5b-451c-bac1-5c83feb7d664"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Confusion Matrix:\n", " [[ 2  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  6  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  3  0  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  5  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  8  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  2  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  1  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  2  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  4  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0 11  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  4  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  0  6  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  0  0  0  3  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  3  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  1]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["**Multinomial NB**"], "metadata": {"id": "XhKqaFDCgv8X"}}, {"cell_type": "code", "source": ["# Feature Selection (replace with your actual relevant features)\n", "features = ['Team1', 'Team2', 'Winner']  # Example features\n", "\n", "X = df[features]\n", "y = df['Winner']\n", "\n", "# Initialize LabelEncoder\n", "label_encoder = LabelEncoder()\n", "\n", "# Iterate through each column in X and encode if it's of type 'object'\n", "for col in X.select_dtypes(include=['object']).columns:\n", "    X[col] = label_encoder.fit_transform(X[col])\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize and train the Multinomial Naive Bayes model\n", "mnb = MultinomialNB()\n", "mnb.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred = mnb.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy}\")\n", "print(classification_report(y_test, y_pred))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Ekcim773gMQN", "outputId": "a39cd493-4f00-49d1-ec51-558e0d79a31c"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy: 0.171875\n", "              precision    recall  f1-score   support\n", "\n", " Afghanistan       1.00      1.00      1.00         2\n", "   Australia       0.71      0.83      0.77         6\n", "  Bangladesh       0.00      0.00      0.00         3\n", "     England       0.33      0.60      0.43         5\n", "       India       0.14      0.12      0.13         8\n", "     Ireland       0.00      0.00      0.00         2\n", "     Namibia       0.00      0.00      0.00         1\n", "       Nepal       0.00      0.00      0.00         0\n", " Netherlands       0.00      0.00      0.00         2\n", " New Zealand       0.00      0.00      0.00         4\n", "    Pakistan       0.00      0.00      0.00        11\n", "    Scotland       0.00      0.00      0.00         2\n", "South Africa       0.00      0.00      0.00         4\n", "   Sri Lanka       0.00      0.00      0.00         6\n", "      U.A.E.       0.00      0.00      0.00         1\n", "      U.S.A.       0.00      0.00      0.00         0\n", " West Indies       0.00      0.00      0.00         3\n", "    Zimbabwe       0.00      0.00      0.00         3\n", "   no result       0.00      0.00      0.00         0\n", "        tied       0.00      0.00      0.00         1\n", "\n", "    accuracy                           0.17        64\n", "   macro avg       0.11      0.13      0.12        64\n", "weighted avg       0.14      0.17      0.15        64\n", "\n"]}, {"output_type": "stream", "name": "stderr", "text": ["<ipython-input-32-6f5284bb0e8e>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-32-6f5284bb0e8e>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-32-6f5284bb0e8e>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}]}, {"cell_type": "code", "source": ["# Confusion Matrix\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "print(\"\\nConfusion Matrix:\\n\", conf_matrix)\n", "\n", "# Visualize the confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=np.unique(y_pred), yticklabels=np.unique(y_test))\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "MUCW1qbWhAoG", "outputId": "55e20c67-2038-427d-a26f-874939b2f6a1"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Confusion Matrix:\n", " [[2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 5 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0]\n", " [0 2 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 3 0 0 0 0 0 0 0 0 1 0 0 1 0 0 0 0]\n", " [0 0 0 4 1 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0]\n", " [0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0]\n", " [0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 3 1 0 0 0 0 0 0]\n", " [0 0 0 0 3 0 0 1 0 0 0 0 1 6 0 0 0 0 0 0]\n", " [0 0 0 0 1 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0]\n", " [0 0 0 0 1 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 2 0 0 1 0 3 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 2 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 1 0 1 1 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]\n", " [0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["**Bernauli NB**"], "metadata": {"id": "lD29_wnJhEfu"}}, {"cell_type": "code", "source": ["# Feature Selection (replace with your actual relevant features)\n", "features = ['Team1', 'Team2', 'Winner']  # Example features\n", "\n", "X = df[features]\n", "y = df['Winner']\n", "\n", "# Initialize LabelEncoder\n", "label_encoder = LabelEncoder()\n", "\n", "# Iterate through each column in X and encode if it's of type 'object'\n", "for col in X.select_dtypes(include=['object']).columns:\n", "    X[col] = label_encoder.fit_transform(X[col])\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# Initialize and train the <PERSON><PERSON><PERSON> Naive Bayes model\n", "bnb = BernoulliNB()\n", "bnb.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred = bnb.predict(X_test)\n", "\n", "# Evaluate the model\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy}\")\n", "print(classification_report(y_test, y_pred))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Y9uL80CchCaW", "outputId": "0e453d7f-6798-4b46-a5a6-b8452e934671"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Accuracy: 0.09375\n", "              precision    recall  f1-score   support\n", "\n", " Afghanistan       1.00      1.00      1.00         2\n", "   Australia       0.00      0.00      0.00         6\n", "  Bangladesh       0.00      0.00      0.00         3\n", "     England       0.00      0.00      0.00         5\n", "       India       0.00      0.00      0.00         8\n", "     Ireland       0.00      0.00      0.00         2\n", "     Namibia       0.00      0.00      0.00         1\n", " Netherlands       0.00      0.00      0.00         2\n", " New Zealand       0.00      0.00      0.00         4\n", "    Pakistan       0.00      0.00      0.00        11\n", "    Scotland       0.00      0.00      0.00         2\n", "South Africa       0.07      1.00      0.12         4\n", "   Sri Lanka       0.00      0.00      0.00         6\n", "      U.A.E.       0.00      0.00      0.00         1\n", " West Indies       0.00      0.00      0.00         3\n", "    Zimbabwe       0.00      0.00      0.00         3\n", "        tied       0.00      0.00      0.00         1\n", "\n", "    accuracy                           0.09        64\n", "   macro avg       0.06      0.12      0.07        64\n", "weighted avg       0.04      0.09      0.04        64\n", "\n"]}, {"output_type": "stream", "name": "stderr", "text": ["<ipython-input-37-f86f3fc69800>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-37-f86f3fc69800>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-37-f86f3fc69800>:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}]}, {"cell_type": "code", "source": ["# Confusion Matrix\n", "conf_matrix = confusion_matrix(y_test, y_pred)\n", "print(\"\\nConfusion Matrix:\\n\", conf_matrix)\n", "\n", "# Visualize the confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=np.unique(y_pred), yticklabels=np.unique(y_test))\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 965}, "id": "dWpXFVlEhU9f", "outputId": "50c37319-aead-4253-8397-dc18a632ba00"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Confusion Matrix:\n", " [[ 2  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  6  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  3  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  5  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  8  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  4  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0 11  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  4  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  6  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]\n", " [ 0  1  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  3  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Apply on boolean parameters, spam detection and documentation"], "metadata": {"id": "OcUsdYaHjMlk"}}, {"cell_type": "code", "source": ["# prompt: Apply on boolean parameters, spam detection and documentation\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import confusion_matrix\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.naive_bayes import GaussianNB, MultinomialNB, BernoulliNB\n", "from sklearn.metrics import classification_report, accuracy_score\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "# ... (Your existing code)\n", "\n", "def apply_spam_detection(df, features, model_type='bernoulli'):\n", "  \"\"\"Applies spam detection using Naive Bayes.\n", "\n", "  Args:\n", "      df: The DataFrame containing the data.\n", "      features: A list of feature column names.\n", "      model_type: The type of <PERSON><PERSON> model to use ('gaussian', 'multinomial', 'bernoulli'). Defaults to 'bernoulli'.\n", "\n", "  Returns:\n", "      A tuple containing the accuracy, classification report, and confusion matrix.\n", "  \"\"\"\n", "  X = df[features]\n", "  y = df['Winner']  # Assuming 'Winner' is the target variable\n", "\n", "  # Encode categorical features\n", "  label_encoder = LabelEncoder()\n", "  for col in X.select_dtypes(include=['object']).columns:\n", "      X[col] = label_encoder.fit_transform(X[col])\n", "\n", "  X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "  if model_type == 'gaussian':\n", "    model = GaussianNB()\n", "  elif model_type == 'multinomial':\n", "    model = MultinomialNB()\n", "  elif model_type == 'bernoulli':\n", "    model = BernoulliNB()\n", "  else:\n", "    raise ValueError(\"Invalid model_type. Choose from 'gaussian', 'multinomial', or 'bernoulli'.\")\n", "\n", "  model.fit(X_train, y_train)\n", "  y_pred = model.predict(X_test)\n", "\n", "  accuracy = accuracy_score(y_test, y_pred)\n", "  report = classification_report(y_test, y_pred)\n", "  conf_matrix = confusion_matrix(y_test, y_pred)\n", "\n", "  return accuracy, report, conf_matrix\n", "\n", "\n", "# Example usage:\n", "features = ['Team1', 'Team2']  # Example features. Replace with your actual features.\n", "\n", "accuracy, report, conf_matrix = apply_spam_detection(df, features, model_type='bernoulli')\n", "print(f\"Accuracy: {accuracy}\")\n", "print(f\"Classification Report:\\n{report}\")\n", "print(f\"Confusion Matrix:\\n{conf_matrix}\")\n", "\n", "\n", "# Visualize the confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix, annot=True, fmt='d', cmap='Blues')\n", "plt.xlabel('Predicted Winner')\n", "plt.ylabel('Actual Winner')\n", "plt.title('Confusion Matrix')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "dmrwasCJh5cr", "outputId": "b99527da-628d-48b1-d97f-96aa785fed7e"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-40-be75dc741078>:32: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "<ipython-input-40-be75dc741078>:32: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  X[col] = label_encoder.fit_transform(X[col])\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/usr/local/lib/python3.10/dist-packages/sklearn/metrics/_classification.py:1531: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Accuracy: 0.09375\n", "Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", " Afghanistan       1.00      1.00      1.00         2\n", "   Australia       0.00      0.00      0.00         6\n", "  Bangladesh       0.00      0.00      0.00         3\n", "     England       0.00      0.00      0.00         5\n", "       India       0.00      0.00      0.00         8\n", "     Ireland       0.00      0.00      0.00         2\n", "     Namibia       0.00      0.00      0.00         1\n", " Netherlands       0.00      0.00      0.00         2\n", " New Zealand       0.00      0.00      0.00         4\n", "    Pakistan       0.00      0.00      0.00        11\n", "    Scotland       0.00      0.00      0.00         2\n", "South Africa       0.07      1.00      0.12         4\n", "   Sri Lanka       0.00      0.00      0.00         6\n", "      U.A.E.       0.00      0.00      0.00         1\n", " West Indies       0.00      0.00      0.00         3\n", "    Zimbabwe       0.00      0.00      0.00         3\n", "        tied       0.00      0.00      0.00         1\n", "\n", "    accuracy                           0.09        64\n", "   macro avg       0.06      0.12      0.07        64\n", "weighted avg       0.04      0.09      0.04        64\n", "\n", "Confusion Matrix:\n", "[[ 2  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  6  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  3  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  5  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  8  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  4  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0 11  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  4  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  6  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]\n", " [ 0  1  0  0  0  0  0  0  0  0  0  2  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  3  0  0  0  0  0]\n", " [ 0  0  0  0  0  0  0  0  0  0  0  1  0  0  0  0  0]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}