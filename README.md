# Audio Classification: Scream Detection

An optimized deep learning project for classifying audio as screaming vs non-screaming sounds using PyTorch.

## 🚀 Key Improvements Made

### 1. **Code Structure & Organization**
- ✅ Removed redundant and unnecessary code blocks
- ✅ Added proper configuration management with `Config` class
- ✅ Implemented modular functions for better maintainability
- ✅ Added comprehensive error handling

### 2. **Data Processing Enhancements**
- ✅ **Standardized audio preprocessing**: Fixed sample rate (22050 Hz) and duration (3 seconds)
- ✅ **Improved audio loading**: Automatic mono conversion and resampling
- ✅ **Better mel-spectrogram parameters**: Increased resolution (128 mel bands, 2048 FFT)
- ✅ **Proper normalization**: Z-score normalization for spectrograms

### 3. **Model Architecture Improvements**
- ✅ **Custom CNN model**: Designed specifically for audio spectrograms
- ✅ **Batch normalization**: Added for better training stability
- ✅ **Dropout layers**: Prevents overfitting (30% dropout rate)
- ✅ **Global average pooling**: Reduces parameters and improves generalization

### 4. **Training Optimizations**
- ✅ **Proper data splitting**: Train/Validation/Test (70%/15%/15%)
- ✅ **Learning rate scheduling**: ReduceLROnPlateau for adaptive learning
- ✅ **Enhanced early stopping**: Prevents overfitting
- ✅ **Data augmentation**: Time and frequency masking for robustness

### 5. **Evaluation & Monitoring**
- ✅ **Comprehensive metrics**: Classification report, confusion matrix
- ✅ **Training visualization**: Loss and accuracy plots
- ✅ **Model evaluation**: Proper test set evaluation
- ✅ **Prediction function**: Easy-to-use inference for new audio files

## 📊 Model Performance Features

- **Reproducible results**: Fixed random seeds
- **Memory efficient**: Optimized batch processing
- **GPU support**: Automatic CUDA detection
- **Robust preprocessing**: Handles various audio formats and lengths

## 🛠️ Installation

```bash
pip install -r requirements.txt
```

## 📁 Project Structure

```
├── ml-review2.py          # Main optimized script
├── requirements.txt       # Dependencies
├── README.md             # This file
└── best_audio_model.pth  # Saved model (after training)
```

## 🎯 Usage

### Training the Model
```python
python ml-review2.py
```

### Using Trained Model for Prediction
```python
from ml-review2 import predict_audio_file
import torch

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = torch.load('best_audio_model.pth')

result = predict_audio_file(model, 'your_audio.wav', device)
print(f"Prediction: {result['prediction']}")
print(f"Confidence: {result['confidence']:.2f}")
```

## 🔧 Configuration

Update the `Config` class in `ml-review2.py` to modify:
- Audio processing parameters
- Training hyperparameters
- Data paths

## 📈 Expected Improvements

The optimized version should provide:
- **Better accuracy**: Improved model architecture and preprocessing
- **Faster training**: Optimized data loading and processing
- **More stable training**: Proper normalization and regularization
- **Better generalization**: Enhanced data augmentation and validation

## 🎵 Audio Requirements

- **Format**: WAV files
- **Duration**: Any (automatically standardized to 3 seconds)
- **Sample Rate**: Any (automatically resampled to 22050 Hz)
- **Channels**: Mono or stereo (automatically converted to mono)

## 📝 Notes

- The model automatically handles different audio lengths and sample rates
- Training includes proper validation and early stopping
- All hyperparameters are configurable through the Config class
- The code includes comprehensive error handling and logging
