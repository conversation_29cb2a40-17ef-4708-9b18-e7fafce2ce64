{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["import numpy as np\n", "from sklearn.cluster import KMeans\n", "import pandas as pd\n", "from sklearn.metrics.pairwise import manhattan_distances, euclidean_distances\n", "from scipy.spatial.distance import cdist\n", "from scipy.spatial.distance import pdist\n", "from scipy.spatial.distance import pdist, squareform, mahalanobis\n", "from sklearn.preprocessing import StandardScaler\n", "import matplotlib.pyplot as plt\n", "from scipy.cluster.hierarchy import dendrogram, linkage\n", "from sklearn.cluster import AgglomerativeClustering\n", "from sklearn.metrics import silhouette_score"], "metadata": {"id": "eY428lxF3e6B"}, "execution_count": 1, "outputs": []}, {"cell_type": "code", "source": ["df=pd.read_csv('/content/Mall_Customers.csv')\n", "df.head()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 206}, "id": "SnDngsbw3jqs", "outputId": "0ef451c1-7e02-4f22-fb49-da8563268ff5"}, "execution_count": 2, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["   CustomerID  Gender  Age  Annual Income (k$)  Spending Score (1-100)\n", "0           1    Male   19                  15                      39\n", "1           2    Male   21                  15                      81\n", "2           3  Female   20                  16                       6\n", "3           4  Female   23                  16                      77\n", "4           5  Female   31                  17                      40"], "text/html": ["\n", "  <div id=\"df-69135748-6fcf-4fda-8c36-1c3631c60f40\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CustomerID</th>\n", "      <th>Gender</th>\n", "      <th>Age</th>\n", "      <th>Annual Income (k$)</th>\n", "      <th>Spending Score (1-100)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Male</td>\n", "      <td>19</td>\n", "      <td>15</td>\n", "      <td>39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Male</td>\n", "      <td>21</td>\n", "      <td>15</td>\n", "      <td>81</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>Female</td>\n", "      <td>20</td>\n", "      <td>16</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Female</td>\n", "      <td>23</td>\n", "      <td>16</td>\n", "      <td>77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>Female</td>\n", "      <td>31</td>\n", "      <td>17</td>\n", "      <td>40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-69135748-6fcf-4fda-8c36-1c3631c60f40')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-69135748-6fcf-4fda-8c36-1c3631c60f40 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-69135748-6fcf-4fda-8c36-1c3631c60f40');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-3aa19827-da8f-4832-80cb-08ac56b45b97\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-3aa19827-da8f-4832-80cb-08ac56b45b97')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-3aa19827-da8f-4832-80cb-08ac56b45b97 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 200,\n  \"fields\": [\n    {\n      \"column\": \"CustomerID\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 57,\n        \"min\": 1,\n        \"max\": 200,\n        \"num_unique_values\": 200,\n        \"samples\": [\n          96,\n          16,\n          31\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Gender\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 2,\n        \"samples\": [\n          \"Female\",\n          \"Male\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Age\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 13,\n        \"min\": 18,\n        \"max\": 70,\n        \"num_unique_values\": 51,\n        \"samples\": [\n          55,\n          26\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Annual Income (k$)\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 26,\n        \"min\": 15,\n        \"max\": 137,\n        \"num_unique_values\": 64,\n        \"samples\": [\n          87,\n          101\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Spending Score (1-100)\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 25,\n        \"min\": 1,\n        \"max\": 99,\n        \"num_unique_values\": 84,\n        \"samples\": [\n          83,\n          39\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 2}]}, {"cell_type": "code", "source": ["## prompt: apply the following distance metric for df\n", "## a) Euclidean distance\n", "## b) Manhattan distance\n", "## c) Maximum norm\n", "## d) <PERSON><PERSON><PERSON><PERSON> distance\n", "## e) Inner product space\n", "#\n", "## a) Euclidean distance\n", "#euclidean_dist = euclidean_distances(df[['Annual Income (k$)', 'Spending Score (1-100)']])\n", "#print(\"Euclidean Distance Matrix:\\n\", euclidean_dist)\n", "#\n", "## b) Manhattan distance\n", "#manhattan_dist = manhattan_distances(df[['Annual Income (k$)', 'Spending Score (1-100)']])\n", "#print(\"\\nManhattan Distance Matrix:\\n\", manhattan_dist)\n", "#\n", "## c) Maximum norm (<PERSON><PERSON><PERSON><PERSON><PERSON> distance)\n", "#from scipy.spatial.distance import cdist\n", "#chebyshev_dist = cdist(df[['Annual Income (k$)', 'Spending Score (1-100)']],\n", "#                        df[['Annual Income (k$)', 'Spending Score (1-100)']],\n", "#                        metric='chebyshev')\n", "#print(\"\\nChebyshev Distance Matrix:\\n\", chebyshev_dist)\n", "#\n", "## d) <PERSON><PERSON><PERSON><PERSON> distance\n", "## Note: <PERSON><PERSON><PERSON><PERSON> distance requires calculating the inverse covariance matrix.\n", "## For simplicity, let's assume the covariance matrix is the identity matrix.\n", "#mahalanobis_dist = cdist(df[['Annual Income (k$)', 'Spending Score (1-100)']],\n", "#                         df[['Annual Income (k$)', 'Spending Score (1-100)']],\n", "#                         metric='mahalanobis', VI=np.identity(2))\n", "#print(\"\\nMahalanobis Distance Matrix:\\n\", mahalanobis_dist)\n", "#\n", "## e) Inner product space (Cosine similarity)\n", "## Note: Cosine similarity is not a distance metric, but it can be used for hierarchical clustering.\n", "## We need to convert it to a distance by subtracting from 1.\n", "#from sklearn.metrics.pairwise import cosine_similarity\n", "#cosine_sim = cosine_similarity(df[['Annual Income (k$)', 'Spending Score (1-100)']])\n", "#cosine_dist = 1 - cosine_sim\n", "#print(\"\\nCosine Distance Matrix:\\n\", cosine_dist)"], "metadata": {"id": "q4q8xyBn7_zR"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["## prompt: for dendrogram use  the following distance metric\n", "## a) Euclidean distance\n", "## b) Manhattan distance\n", "## c) Maximum norm\n", "## d) <PERSON><PERSON><PERSON><PERSON> distance\n", "## e) Inner product space and print the dendrogram graph\n", "#\n", "#import numpy as np\n", "#import matplotlib.pyplot as plt\n", "#import scipy.cluster.hierarchy as sch\n", "#\n", "## Select the features for clustering\n", "#X = df[['Annual Income (k$)', 'Spending Score (1-100)']].values\n", "#\n", "## a) Euclidean distance\n", "#plt.figure(figsize=(10, 7))\n", "#dendrogram = sch.dendrogram(sch.linkage(X, method='ward', metric='euclidean'))\n", "#plt.title('Dendrogram (Euclidean Distance)')\n", "#plt.xlabel('Customers')\n", "#plt.ylabel('Euclidean distances')\n", "#plt.show()\n", "#\n", "## b) Manhattan distance\n", "#plt.figure(figsize=(10, 7))\n", "#dendrogram = sch.dendrogram(sch.linkage(X, method='average', metric='cityblock')) # Changed method to 'average' which is compatible with 'cityblock'\n", "#plt.title('Dendrogram (Manhattan Distance)')\n", "#plt.xlabel('Customers')\n", "#plt.ylabel('Manhattan distances')\n", "#plt.show()\n", "#\n", "## c) Maximum norm (<PERSON><PERSON><PERSON><PERSON><PERSON> distance)\n", "#plt.figure(figsize=(10, 7))\n", "#dendrogram = sch.dendrogram(sch.linkage(X, method='average', metric='chebyshev')) # Changed method to 'average' which is compatible with 'chebyshev'\n", "#plt.title('Dendrogram (Maximum Norm)')\n", "#plt.xlabel('Customers')\n", "#plt.ylabel('Maximum Norm distances')\n", "#plt.show()\n", "#\n", "## d) <PERSON><PERSON><PERSON><PERSON> distance\n", "## Note: <PERSON><PERSON><PERSON><PERSON> distance requires calculating the inverse covariance matrix.\n", "## For simplicity, let's assume the covariance matrix is the identity matrix.\n", "#plt.figure(figsize=(10, 7))\n", "#distances = pdist(X, metric='mahalanobis', VI=np.identity(X.shape[1]))\n", "#dendrogram = sch.dendrogram(sch.linkage(distances, method='ward'))\n", "#plt.title('Dendrogram (Mahalanobis Distance)')\n", "#plt.xlabel('Customers')\n", "#plt.ylabel('Mahalanobis distances')\n", "#plt.show()\n", "#\n", "## e) Inner product space (Cosine similarity)\n", "## Note: Cosine similarity is not a distance metric, but it can be used for hierarchical clustering.\n", "## We need to convert it to a distance by subtracting from 1.\n", "#from sklearn.metrics.pairwise import cosine_similarity\n", "#cosine_sim = cosine_similarity(X)\n", "#distance_matrix = 1 - cosine_sim\n", "#plt.figure(figsize=(10, 7))\n", "#dendrogram = sch.dendrogram(sch.linkage(distance_matrix, method='ward'))\n", "#plt.title('Dendrogram (Inner Product Space - Cosine Similarity)')\n", "#plt.xlabel('Customers')\n", "#plt.ylabel('Distance (1 - Cosine Similarity)')\n", "#plt.show()\n"], "metadata": {"id": "cf0NiscV2mz3"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Select relevant features: Age, Annual Income, and Spending Score\n", "X = df[['Age', 'Annual Income (k$)', 'Spending Score (1-100)']].values\n", "\n", "# Standardize the data\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "\n", "# 1. Distance Metrics for the new dataset\n", "def distance_metrics(X):\n", "    print(\"Euclidean Distance:\\n\", squareform(pdist(X, 'euclidean')))\n", "    print(\"\\nManhattan Distance:\\n\", squareform(pdist(X, 'cityblock')))\n", "    print(\"\\nMaximum Norm:\\n\", squareform(pdist(X, 'chebyshev')))\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON>\n", "    cov_matrix = np.cov(X.T)\n", "    inv_cov_matrix = np.linalg.inv(cov_matrix)\n", "    mahal_dist = [mahala<PERSON>is(x, X.mean(axis=0), inv_cov_matrix) for x in X]\n", "    print(\"\\nMahalanobis Distance:\\n\", np.array(mahal_dist))\n", "\n", "    # Inner Product Space\n", "    print(\"\\nInner Product Space:\\n\", np.dot(X, X.T))\n", "\n", "# Apply distance metrics function to the scaled dataset\n", "distance_metrics(X_scaled)"], "metadata": {"id": "OjeC6jJtB5dG", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "0c92cb8b-b1f7-4525-ad8f-8bf075e73ce8"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Euclidean Distance:\n", " [[0.         1.63681098 1.28368768 ... 4.54611614 4.81867651 5.02250496]\n", " [1.63681098 0.         2.91275149 ... 4.3182849  5.31878915 4.70189264]\n", " [1.28368768 2.91275149 0.         ... 5.03379713 4.72114828 5.54808733]\n", " ...\n", " [4.54611614 4.3182849  5.03379713 ... 0.         2.21418015 0.5647686 ]\n", " [4.81867651 5.31878915 4.72114828 ... 2.21418015 0.         2.5274803 ]\n", " [5.02250496 4.70189264 5.54808733 ... 0.5647686  2.5274803  0.        ]]\n", "\n", "Manhattan Distance:\n", " [[0.         1.77403893 1.39104762 ... 6.52852823 6.4048901  7.15425262]\n", " [1.77403893 0.         3.02155317 ... 5.29799115 7.89186227 5.38021369]\n", " [1.39104762 3.02155317 0.         ... 7.69970361 5.94555993 8.325428  ]\n", " ...\n", " [6.52852823 5.29799115 7.69970361 ... 0.         2.59387112 0.91279115]\n", " [6.4048901  7.89186227 5.94555993 ... 2.59387112 0.         2.66693483]\n", " [7.15425262 5.38021369 8.325428   ... 0.91279115 2.66693483 0.        ]]\n", "\n", "Maximum Norm:\n", " [[0.         1.63050555 1.2811115  ... 4.23680664 4.65667036 4.65667036]\n", " [1.63050555 0.         2.91161705 ... 4.23680664 4.65667036 4.65667036]\n", " [1.2811115  2.91161705 0.         ... 4.19863721 4.61850093 4.61850093]\n", " ...\n", " [4.23680664 4.23680664 4.19863721 ... 0.         2.1740074  0.41986372]\n", " [4.65667036 4.65667036 4.61850093 ... 2.1740074  0.         2.52340145]\n", " [4.65667036 4.65667036 4.61850093 ... 0.41986372 2.52340145 0.        ]]\n", "\n", "Mahalanobis Distance:\n", " [2.44421772 2.32106338 3.14657554 2.17411396 1.85816475 2.16294602\n", " 2.51216333 2.44000616 2.71452377 1.84340832 2.66250382 2.50107716\n", " 2.26511211 2.02569938 2.2008804  2.11728007 1.69055346 2.04366422\n", " 1.7842339  2.37418445 1.58968761 1.82070858 2.20370069 1.65162004\n", " 1.97254238 1.78716749 1.43544328 1.32189982 1.41854658 2.00649265\n", " 2.33622674 1.80918796 2.11206753 2.19427399 1.7641223  1.85751581\n", " 1.6421891  1.40012469 1.40279728 1.72153228 2.05033793 1.9271956\n", " 1.10103207 1.0321285  1.27034451 1.37301808 1.20953781 1.2350994\n", " 1.20617431 1.10274793 1.05992761 0.86619787 0.87944965 1.82305996\n", " 1.03710414 0.89835601 1.10848959 2.3148069  1.05121555 1.16309948\n", " 2.505315   1.55936672 2.21414488 1.40560153 1.89732868 1.58052158\n", " 0.56832939 2.22773439 1.51050106 0.75087535 2.46709821 0.7382913\n", " 1.63320149 1.7388526  1.50708042 0.96974929 0.57073178 0.26883792\n", " 1.20742901 0.76994184 1.40613831 0.31283837 2.05311668 0.57177233\n", " 1.31518488 0.7004377  1.36778741 1.23535688 0.45873372 0.80980035\n", " 2.28063415 1.74129458 0.67883565 0.39850012 0.70493924 1.10355457\n", " 0.58790622 0.90094436 0.66455495 1.44487812 1.37197859 0.74697981\n", " 2.27742244 0.92897911 0.8781106  1.49696115 2.05725777 1.10845513\n", " 2.13327927 2.03227899 2.00952063 1.46231682 0.38571014 1.57296258\n", " 1.61867879 1.51440072 1.76539117 0.96601349 0.90668524 1.00427564\n", " 0.88205205 0.50752964 0.4801509  1.70439882 1.733232   1.11926932\n", " 0.72266938 1.90595943 1.86663361 1.07088688 1.64955246 1.09258611\n", " 1.47717548 0.96371616 2.72463744 1.55198558 1.75297061 1.0194337\n", " 2.61804116 0.98289341 1.99626243 1.74216066 1.18935871 1.5363607\n", " 2.24004627 1.92084742 0.97981663 1.1258218  1.48128115 1.68109785\n", " 1.45820368 1.68515532 1.35063315 1.22841113 1.49661533 1.6782992\n", " 2.17386548 1.29256307 2.26671043 1.15645529 1.43623776 1.47600441\n", " 2.84892763 1.8241678  1.42888318 1.33657235 1.53399014 1.98565531\n", " 1.45124586 1.16760294 1.81082294 1.46523244 2.00582855 1.93056541\n", " 1.8586304  1.73711435 1.98902581 1.41795127 2.15280367 1.98192792\n", " 1.60582359 1.95186174 1.9839115  2.04564585 1.5308957  2.31846315\n", " 2.02232468 1.77432082 2.09200982 2.11074786 2.06586818 1.78357411\n", " 2.77516284 2.58316809 2.63977264 2.5158811  2.65085723 2.65393245\n", " 3.30890557 3.17054173]\n", "\n", "Inner Product Space:\n", " [[ 5.24256676  4.32914736  5.63098274 ... -4.04510071 -3.82998175\n", "  -4.72268726]\n", " [ 4.32914736  6.09487814  2.63900484 ... -2.60915125 -5.93876342\n", "  -2.7376507 ]\n", " [ 5.63098274  2.63900484  7.66725278 ... -5.16872846 -2.15243765\n", "  -6.2882027 ]\n", " ...\n", " [-4.04510071 -2.60915125 -5.16872846 ...  7.3344038   6.37446155\n", "   8.77652753]\n", " [-3.82998175 -5.93876342 -2.15243765 ...  6.37446155 10.31711303\n", "   7.23328558]\n", " [-4.72268726 -2.7376507  -6.2882027  ...  8.77652753  7.23328558\n", "  10.53761483]]\n"]}]}, {"cell_type": "code", "source": ["# Simulate DIANA with complete-linkage agglomerative clustering\n", "agg_complete = linkage(X_scaled, method='complete')\n", "plt.figure(figsize=(10, 7))\n", "dendrogram(agg_complete, orientation='left')\n", "plt.title(\"DIANA (Simulated with Complete-Linkage Agglomerative)\")\n", "plt.show()\n", "\n", "# AGNES clustering with different linkage methods (single, complete, ward)\n", "linkage_methods = ['single', 'complete', 'ward']\n", "for method in linkage_methods:\n", "    linked = linkage(X_scaled, method=method)\n", "    plt.figure(figsize=(10, 7))\n", "    dendrogram(linked)\n", "    plt.title(f\"AGNES (Linkage Method: {method.capitalize()})\")\n", "    plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "aWUWsBCoSogp", "outputId": "6f55dcd4-b4d7-47f0-f586-930bd71b92d1"}, "execution_count": 4, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x700 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAzIAAAJdCAYAAADp4EIWAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAACpIElEQVR4nOzdd1jV5f8/8Oc5h70FxQWKe2G5cqO4xZUZqeFEc+ZKTSW0MEXSjDTzo2i4cUviTMWVM3Oj4gRFHKjIEkEQ3r8/+sGXKe+zB8/HdXGVh8N9bg7nHM6L+zUkgiAIICIiIiIi0iNSbW+AiIiIiIhIXgxkiIiIiIhI7zCQISIiIiIivcNAhoiIiIiI9A4DGSIiIiIi0jsMZIiIiIiISO8wkCEiIiIiIr3DQIaIiIiIiPQOAxkiIiIiItI7DGSINOTChQswMTHBo0eP5P7aEydOQCKR4MSJE6rfmAguLi4YPny4Vm67OH5+fpBIJGpbXyKRwM/PT/R1J0yYoLa96Dp3d3e4u7trexsKkefnXNTXluafu6J08fVEkx4+fAiJRIJ169ap7TZWrlyJKlWq4N27d2q7DSJdwECGtGbdunWQSCS5H2ZmZqhUqRK6deuG3377DSkpKYW+JufN66tXr4pcs3///pBIJJg5c2aRn88JCCQSCS5dulTo88OHD4eVlVWxe27evDkkEglWrFgh8rv8P76+vvjyyy9RtWrV3Muys7OxYcMGtGjRAvb29rC2tkbt2rUxdOhQnD9/Xu7b0DVPnz6Fn58frl69qu2tKO3s2bPw8/NDYmKiWtZPTk7G3Llz8fHHH8PKygrm5uZwdXXFzJkz8fTpU7Xcpi5YsGABdu/erfJ1c57rO3fuVPnahuLAgQOQSCSoVKkSsrOztb0dg7N582YsWbJEK7c9fPhwZGRkICgoSCu3T6QpDGRI63788Uds3LgRK1aswMSJEwEAU6ZMQcOGDXH9+nXR6yQnJ2Pv3r1wcXHBli1bIAjCB68v719h7927h3///RcuLi4ICQmR62uvXr2K8PBwjB07Nt/lkyZNwrBhw1CxYkX4+flh4cKF8PDwwPnz5/HXX3/lXq9du3ZIS0tDu3bt5LpdbXv69Cnmzp2rl4FMWloaZs+enfvvs2fPYu7cuWoJZKKiotCoUSPMmzcP9evXx8KFC/Hbb7+hQ4cOCA4O1tvTDjHUFciIVfDnXJqEhITAxcUFz549w7Fjx7S9HYNTXCBTtWpVpKWlYciQIWq7bTMzMwwbNgyBgYEl/i4k0mdG2t4AkYeHB5o1a5b7bx8fHxw7dgy9evVCnz59EBkZCXNz8xLX2bVrF7KysrBmzRp07NgRf//9N9q3b1/kdRs1aoR9+/bh8uXLaNKkiah9btq0CY6Ojvjll1/g6emJhw8fwsXFRdTXrl27FlWqVEHLli1zL4uLi8P//vc/jBo1CqtWrcp3/SVLluDly5e5/5ZKpTAzMxN1W6Qamrq/379/j379+iEuLg4nTpxA27Zt833e398fCxcu1MheSqPS+rxKTU1FWFgYAgICsHbtWoSEhKBz587a3pZOe/v2LSwsLJReJycDQd369++PRYsW4fjx4+jYsaPab49IG3giQzqpY8eOmDNnDh49eoRNmzaJ+pqQkBB06dIFHTp0QL169T54ajJx4kSUKVNGrlOZzZs3w9PTE7169YKtrS02b94s+mt3796Njh075qvpiI6OhiAIaNOmTaHrSyQSODo65v67qBoZd3d3uLq64vr162jfvj0sLCxQs2bN3FSakydPokWLFjA3N0edOnUQHh6e7zaGDx9eZCAmpvbk9evXmD59Oho2bAgrKyvY2NjAw8MD165dy7fnTz75BADg7e2dm9KXNy/8n3/+Qffu3WFrawsLCwu0b98eZ86cKXR7p0+fxieffAIzMzPUqFFDdLrEb7/9BplMlu8U5ZdffoFEIsHUqVNzL8vKyoK1tXW+lMS8tRN+fn749ttvAQDVqlXL/V4ePnyY7/Z2794NV1dXmJqaokGDBvlO1Yqza9cuXLt2Db6+voWCGACwsbGBv79/vst27NiBpk2bwtzcHGXLlsXgwYPx5MmTfNfJSZOMiYlBr169YGVlhcqVK2P58uUAgIiICHTs2BGWlpaoWrVqocdzTurn33//jTFjxsDBwQE2NjYYOnQoEhISSvy+3r17hx9++AE1a9aEqakpnJ2dMWPGjHw5+xKJBKmpqVi/fn3ufZq3duLJkycYMWIEypcvn3ufrlmzpsTblkfBGpmcx//9+/cxfPhw2NnZwdbWFt7e3nj79m2J682fPx9SqRTLli0DADx69Ajjx49HnTp1YG5uDgcHB3zxxReFHjsAcp/L5ubmcHJywvz587F27doiH2sHDx6Em5sbLC0tYW1tjZ49e+LmzZuiv+8///wTaWlp+OKLLzBw4ECEhoYiPT290PXS0tIwadIklC1bFtbW1ujTpw+ePHlSZG3RiRMn0KxZs3zPU7G1bFFRUfjiiy9gb28PCwsLtGzZEvv37y+0vkQiwfbt2zF37lxUrlwZ1tbW8PT0RFJSEt69e4cpU6bA0dERVlZW8Pb2LrJGZNOmTbnPH3t7ewwcOBCPHz/Od52c19dLly6hXbt2sLCwwHfffQcACAsLQ8+ePVGpUiWYmpqiRo0amDdvHrKysvJ9/f79+/Ho0aPcx3bO623BGpnFixdDIpEUWT/p4+MDExOTfM85sa+bTZs2hb29PcLCwkq8/4n0FQMZ0lk5x+6HDx8u8bpPnz7F8ePH8eWXXwIAvvzyS+zcuRMZGRlFXt/GxgbffPMN9u7di8uXL5e4/j///IP79+/jyy+/hImJCfr16yc6vezJkyeIiYkpdPKTUyuzY8cOUW+QipKQkIBevXqhRYsWWLRoEUxNTTFw4EBs27YNAwcORI8ePfDTTz8hNTUVnp6eRdYdKSIqKgq7d+9Gr169EBgYiG+//RYRERFo3759bj1HvXr18OOPPwIARo8ejY0bN2Ljxo256XHHjh1Du3btkJycjB9++AELFixAYmIiOnbsiAsXLuTeVkREBLp27YoXL17Az88P3t7e+OGHH/Dnn3+WuE83NzdkZ2fj9OnTuZedOnUKUqkUp06dyr3sypUrePPmTbGpe/369ct9bP3666+530u5cuVyr3P69GmMHz8eAwcOxKJFi5Ceno7PP/8c8fHxH9zjnj17AEB0msm6devQv39/yGQyBAQEYNSoUQgNDUXbtm0Lpb1lZWXBw8MDzs7OWLRoEVxcXDBhwgSsW7cO3bt3R7NmzbBw4UJYW1tj6NChiI6OLnR7EyZMQGRkJPz8/DB06FCEhISgb9++H0xXyc7ORp8+fbB48WL07t0by5YtQ9++ffHrr79iwIABudfbuHEjTE1N4ebmlnufjhkzBsB/J5YtW7ZEeHg4JkyYgKVLl6JmzZoYOXKkRuoO+vfvj5SUFAQEBKB///5Yt24d5s6d+8GvmT17Nr7//nsEBQXlpsn++++/OHv2LAYOHIjffvsNY8eOxdGjR+Hu7p7vef/kyRN06NABN2/ehI+PD7755huEhIRg6dKlhW5n48aN6NmzJ6ysrLBw4ULMmTMHt27dQtu2bYsMkIoSEhKCDh06oEKFChg4cCBSUlKwd+/eQtcbPnw4li1bhh49emDhwoUwNzdHz549C13vypUr6N69O+Lj4zF37lyMHDkSP/74o6i0wbi4OLRu3RqHDh3C+PHj4e/vj/T0dPTp06fI53lAQAAOHTqEWbNmYcSIEQgNDcXYsWMxYsQI3L17F35+fujXrx/WrVtX6DTT398fQ4cORa1atRAYGIgpU6bg6NGjaNeuXaHnT3x8PDw8PNCoUSMsWbIEHTp0APDfc9DKygpTp07F0qVL0bRpU3z//feYNWtW7tf6+vqiUaNGKFu2bO5ju7jHbU5t5/bt2wt9bvv27ejatSvKlCkDQPzrZo4mTZoUGeQQGQyBSEvWrl0rABD+/fffYq9ja2srNG7cOPffP/zwgwBAePnyZb7rLV68WDA3NxeSk5MFQRCEu3fvCgCEP//8M9/1jh8/LgAQduzYISQmJgplypQR+vTpk/v5YcOGCZaWloX2MWHCBMHZ2VnIzs4WBEEQDh8+LAAQrly5UuL3GR4eLgAQ9u7dW+hzQ4cOFQAIZcqUET777DNh8eLFQmRkZKHr5ez7+PHjuZe1b99eACBs3rw597Lbt28LAASpVCqcP38+9/JDhw4JAIS1a9fm+16rVq1a6LZy7uO8qlatKgwbNiz33+np6UJWVla+60RHRwumpqbCjz/+mHvZv//+W+h2BUEQsrOzhVq1agndunXLvU8FQRDevn0rVKtWTejSpUvuZX379hXMzMyER48e5V5269YtQSaTFdpnQVlZWYKNjY0wY8aM3Nt1cHAQvvjiC0EmkwkpKSmCIAhCYGCgIJVKhYSEhNyvBSD88MMPuf/++eefBQBCdHR0odsBIJiYmAj379/PvezatWsCAGHZsmUf3GPjxo0FW1vbD14nR0ZGhuDo6Ci4uroKaWlpuZfv27dPACB8//33uZcNGzZMACAsWLAg97KEhATB3NxckEgkwtatW3Mvz3nc5P1+c56fTZs2FTIyMnIvX7RokQBACAsLy72sffv2Qvv27XP/vXHjRkEqlQqnTp3Kt/+VK1cKAIQzZ87kXmZpaZnvsZVj5MiRQsWKFYVXr17lu3zgwIGCra2t8Pbt2w/cU/mf6x9S8PvOefyPGDEi3/U+++wzwcHBodDXfv3114IgCMK0adMEqVQqrFu3Lt91itrnuXPnBADChg0bci+bOHGiIJFI8r2mxMfHC/b29vkedykpKYKdnZ0watSofGs+f/5csLW1LXR5UeLi4gQjIyNh9erVuZe1bt1a+PTTT/Nd79KlSwIAYcqUKfkuHz58eKH7rXfv3oKFhYXw5MmT3Mvu3bsnGBkZlfh6MmXKFAFAvsdLSkqKUK1aNcHFxSX3tSbnZ+rq6prvMfnll18KEolE8PDwyHc7rVq1yvca9/DhQ0Emkwn+/v75rhcRESEYGRnluzzn9XXlypVCQUX9TMeMGSNYWFgI6enpuZf17NmzyNfY6OjoQq+LrVq1Epo2bZrvehcuXMj3OJHndTPH6NGjBXNz80KXExkKnsiQTrOyshJ1ihASEoKePXvC2toaAFCrVi00bdr0g6cmtra2mDJlCvbs2YMrV64Ue733799j27ZtGDBgQG6KRMeOHeHo6CjqVCbnL/I5f1HLa+3atfj9999RrVo1/Pnnn5g+fTrq1auHTp06FUoVKoqVlRUGDhyY++86derAzs4O9erVQ4sWLXIvz/n/qKioEtcUw9TUFFLpfy8fWVlZiI+Ph5WVFerUqSPqhOvq1au4d+8evLy8EB8fj1evXuHVq1dITU1Fp06d8PfffyM7OxtZWVk4dOgQ+vbtiypVquR+fb169dCtW7cSb0cqlaJ169b4+++/AQCRkZGIj4/HrFmzIAgCzp07B+C/UxpXV1fY2dkpcG/8p3PnzqhRo0buvz/66CPY2NiUeJ8nJyfnPm5LcvHiRbx48QLjx4/Pl2Pfs2dP1K1bt1AqDgB89dVXuf9vZ2eHOnXqwNLSEv3798+9POdxU9ReR48eDWNj49x/jxs3DkZGRjhw4ECx+9yxYwfq1auHunXr5v5sX716lZunf/z48Q9+n4IgYNeuXejduzcEQci3Rrdu3ZCUlCTqcaaMgo053NzcEB8fj+Tk5EJ7zTkx2rRpE4YNG5bv83nr+zIzMxEfH4+aNWvCzs4u3/fw119/oVWrVmjUqFHuZfb29hg0aFC+9Y4cOYLExER8+eWX+e4XmUyGFi1alHjfAsDWrVshlUrx+eef51725Zdf4uDBg/lSmHJSI8ePH5/v63NOm3JkZWUhPDwcffv2RaVKlXIvr1mzJjw8PErcz4EDB9C8efN8qZVWVlYYPXo0Hj58iFu3buW7/tChQ/M9Jlu0aAFBEDBixIh812vRogUeP36M9+/fAwBCQ0ORnZ2N/v3757vvKlSogFq1ahW670xNTeHt7V1ov3l/pikpKXj16hXc3Nzw9u1b3L59u8TvtygDBgzApUuX8ODBg9zLtm3bBlNTU3z66acAxL9u5lWmTBmkpaUpfOpPpOsYyJBOe/PmTYlv8iIjI3HlyhW0adMG9+/fz/1wd3fHvn37Cr3xyGvy5Mmws7P7YK3M4cOH8fLlSzRv3jx37ejoaHTo0AFbtmwR3bZUKCIVRyqV4uuvv8alS5fw6tUrhIWFwcPDA8eOHcsXoBTHycmpUP65ra0tnJ2dC10GQFRtgxjZ2dn49ddfUatWLZiamqJs2bIoV64crl+/jqSkpBK//t69ewCAYcOGoVy5cvk+/vjjD7x79w5JSUl4+fIl0tLSUKtWrUJr1KlTR9Re3dzccOnSJaSlpeHUqVOoWLEimjRpgo8//jg3vez06dNwc3OT4x4oLG+glaNMmTIl3uc2NjaiU/5ycuiL+t7r1q1bKMfezMwsX/ob8N9jobjHTVF7LXjfW1lZoWLFih9MYbp37x5u3rxZ6Gdbu3ZtAMCLFy+K/yYBvHz5EomJiVi1alWhNXLeWOas8fz583wfaWlpH1xbrII/z5w/RBS8jzZs2IDly5dj2bJluemHeaWlpeH777+Hs7NzvudKYmJivufKo0ePULNmzUJfX/CynOdOx44dC903hw8fLvG+Bf6rEWnevDni4+NzX9MaN26MjIwM7NixI9+epFIpqlWr9sE9vXjxAmlpaaL2X5RHjx4V+ZiuV69e7ufzKvizyXl9K+p1Lzs7O/d+vnfvHgRBQK1atQrdd5GRkYXuu8qVK8PExKTQvm7evInPPvsMtra2sLGxQbly5TB48GAAEPX6V5QvvvgCUqkU27ZtA/Df74sdO3bAw8MDNjY2ufsHSn7dzCvn9446Z24RaRO7lpHOio2NRVJSUom/CHOaAXzzzTf45ptvCn1+165dRf5VDfi/Uxk/P79iT2VyTl3y/gU7r5MnT+bmThfFwcEBQMlBhIODA/r06YM+ffrA3d0dJ0+exKNHj/LNnSlIJpPJdXneYKq4X2x5C1aLs2DBAsyZMwcjRozAvHnzYG9vD6lUiilTpogK7HKu8/PPP+f7C3ReVlZWKhnm1rZtW2RmZuLcuXM4depUbsDi5uaGU6dO4fbt23j58qXSgYyY+7wodevWxZUrV/D48eNCb8SUpczjQxnZ2dlo2LAhAgMDi/x8Sd9nzuNj8ODBhU44cnz00UcAgIoVK+a7fO3atSoZtij2PmrTpg2uXr2K33//Hf3794e9vX2+z0+cOBFr167FlClT0KpVK9ja2kIikWDgwIEKzW7J+ZqNGzeiQoUKhT5vZPThX+s5beSBwkEq8N/r3ejRo+XelyYp+rjOzs6GRCLBwYMHi7xuwRliRXXLTExMRPv27WFjY4Mff/wRNWrUgJmZGS5fvoyZM2cqPI+nUqVKcHNzw/bt2/Hdd9/h/PnziImJyVfjI/Z1M6+EhARYWFiI6vxJpI8YyJDO2rhxIwB8MIVIEARs3rwZHTp0KJT+AADz5s1DSEhIsYEM8N/MmiVLlmDu3LmFUotyWpQOGDAAnp6ehb520qRJuUWzxalbty4AFFlIXZxmzZrh5MmTePbs2QcDGWWUKVOmyJkoRXXOKWjnzp25M07ySkxMRNmyZXP/XVywlJOCZWNj88GWr+XKlYO5uXnuXyLzunPnTon7BP4bYmpiYoJTp07h1KlTud3H2rVrh9WrV+Po0aO5//4Qdf1Fs3fv3tiyZQs2bdoEHx+fD14357Fw586dQu1U79y5o5bHyr179/I9vt+8eYNnz56hR48exX5NjRo1cO3aNXTq1KnE+62oz5crVw7W1tbIysoqsSXwkSNH8v27QYMGH7y+qtWsWROLFi2Cu7s7unfvjqNHj+Y7Rd65cyeGDRuGX375Jfey9PT0Qs+9qlWr4v79+4XWL3hZznPH0dFRoXbJISEhMDY2xsaNGwu9mT99+jR+++03xMTEoEqVKqhatSqys7MRHR2dL+gpuCdHR0eYmZmJ2n9RqlatWuTzOSdNS1WP6xo1akAQBFSrVi33dFBeJ06cQHx8PEJDQ/O9ZhT1+i7va8aAAQMwfvx43LlzB9u2bYOFhQV69+6db/9Aya+beUVHR+eebBEZIqaWkU46duwY5s2bh2rVqhXKEc/rzJkzePjwIby9veHp6VnoY8CAATh+/PgHJ6PnnMqEhYUVGtz4559/IjU1FV9//XWR6/fq1Qu7du364MlB5cqV4ezsjIsXL+a7/Pnz54VyvwEgIyMDR48ehVQqFZWWoagaNWogKSkp39DRZ8+eieoGJpPJCv1leseOHYXqeiwtLQGg0Ju2pk2bokaNGli8eDHevHlTaP2cGToymQzdunXD7t27ERMTk/v5yMhIHDp0qMR9Av+lV33yySfYsmULYmJi8p3IpKWl4bfffkONGjUK/WW/oOK+F2V5enqiYcOG8Pf3z63ZySslJQW+vr4A/gtwHR0dsXLlynyPuYMHDyIyMrLIblLKWrVqFTIzM3P/vWLFCrx///6DtQ/9+/fHkydPsHr16kKfS0tLQ2pqau6/LS0tC92nMpkMn3/+OXbt2oUbN24UWiPvjKXOnTvn+yjp56gOH330EQ4cOIDIyEj07t07X3pbUc+VZcuWFTr57NatG86dO5fvNej169eF6vC6desGGxsbLFiwIN/PJUfe+6YoISEhcHNzy/3jTN6PnCB/y5YtubcFAP/73/8K7T8vmUyGzp07Y/fu3flea+/fv4+DBw9+cD8A0KNHD1y4cCHf4z81NRWrVq2Ci4sL6tevX+IaYvTr1w8ymQxz584t9DMRBKHEDoPA/5365P36jIyMQvcR8N9jW55Us88//xwymQxbtmzBjh070KtXr9zXHUD862Zely9fRuvWrUXvgUjf8ESGtO7gwYO4ffs23r9/j7i4OBw7dgxHjhxB1apVsWfPng8ODgsJCYFMJiv2DVyfPn3g6+uLrVu35psbUtDkyZPx66+/4tq1a/l+cYSEhMDBwaHYXwR9+vTB6tWrsX//fvTr16/Y9T/99FP8+eefEAQh9690sbGxaN68OTp27IhOnTqhQoUKePHiBbZs2YJr165hypQp+U43VG3gwIGYOXMmPvvsM0yaNAlv377FihUrULt27RILqXv16oUff/wR3t7eaN26NSIiIhASEoLq1avnu16NGjVgZ2eHlStXwtraGpaWlmjRogWqVauGP/74Ax4eHmjQoAG8vb1RuXJlPHnyBMePH4eNjU1uK9i5c+fir7/+gpubG8aPH4/3799j2bJlaNCgQb4g7EPc3Nzw008/wdbWFg0bNgTw31+R69Spgzt37ohKRWratCmA/9qqDhw4EMbGxujdu3e+x4sijI2NERoais6dO6Ndu3bo378/2rRpA2NjY9y8eRObN29GmTJl4O/vD2NjYyxcuBDe3t5o3749vvzyS8TFxWHp0qVwcXEpMrVSWRkZGejUqRP69++PO3fu4H//+x/atm2LPn36FPs1Q4YMwfbt2zF27FgcP34cbdq0QVZWFm7fvo3t27fj0KFDuUNwmzZtivDwcAQGBqJSpUqoVq0aWrRogZ9++gnHjx9HixYtMGrUKNSvXx+vX7/G5cuXER4ejtevX4va/65du4oswB42bJhKU/latmyJsLAw9OjRA56enti9ezeMjY3Rq1cvbNy4Eba2tqhfvz7OnTuH8PDw3JTTHDNmzMCmTZvQpUsXTJw4EZaWlvjjjz9QpUoVvH79Ovd1w8bGBitWrMCQIUPQpEkTDBw4EOXKlUNMTAz279+PNm3a4Pfffy9yjzlt5CdMmFDk5ytXrowmTZogJCQEM2fORNOmTfH5559jyZIliI+PR8uWLXHy5EncvXsXQP4TBz8/Pxw+fBht2rTBuHHjkJWVhd9//x2urq6F/kBU0KxZs7BlyxZ4eHhg0qRJsLe3x/r16xEdHY1du3blNhZRVo0aNTB//nz4+Pjg4cOH6Nu3L6ytrREdHY0///wTo0ePxvTp0z+4RuvWrVGmTBkMGzYMkyZNgkQiwcaNG4tMy2zatCm2bduGqVOn4pNPPoGVlVW+E5aCHB0d0aFDBwQGBiIlJSVfq3Lgv5pKsa+bAHDp0iW8fv06t1kAkUHSYIc0onxy2rvmfJiYmAgVKlQQunTpIixdujS3lXJeedsvZ2RkCA4ODoKbm9sHb6datWq5LZw/1JI1Z+2c9ss5LUqHDBlS7Npv374VLCwshM8+++yDe7h8+XKh9qLJycnC0qVLhW7duglOTk6CsbGxYG1tLbRq1UpYvXp1vvaaxbVfbtCgQaHbqlq1qtCzZ89ClyNPq9gchw8fFlxdXQUTExOhTp06wqZNm0S3X542bZpQsWJFwdzcXGjTpo1w7ty5Qm14BUEQwsLChPr16+e2Yc3bcvTKlStCv379BAcHB8HU1FSoWrWq0L9/f+Ho0aP51jh58qTQtGlTwcTERKhevbqwcuXKIvdZnP379wsACrVn/eqrrwQAQnBwcJH3V972soIgCPPmzRMqV64sSKXSfC1xi7pvBaHw/fYhCQkJwvfffy80bNhQsLCwEMzMzARXV1fBx8dHePbsWb7rbtu2TWjcuLFgamoq2NvbC4MGDRJiY2PzXae4VuJiHzc5z8+TJ08Ko0ePFsqUKSNYWVkJgwYNEuLj4wutWfDnnpGRISxcuFBo0KCBYGpqKpQpU0Zo2rSpMHfuXCEpKSn3erdv3xbatWsnmJubCwDy3V9xcXHC119/LTg7OwvGxsZChQoVhE6dOgmrVq0q8f7Mec4U95HzXCz4cy6uxXvO/ZG3/XZRP/ewsDDByMhIGDBggJCVlSUkJCQI3t7eQtmyZQUrKyuhW7duwu3bt4t8bFy5ckVwc3MTTE1NBScnJyEgIED47bffBADC8+fPC31/3bp1E2xtbQUzMzOhRo0awvDhw4WLFy8We59MnDhRACA8ePCg2Ov4+fkJAIRr164JgiAIqampwtdffy3Y29sLVlZWQt++fYU7d+4IAISffvop39cePXpUaNy4sWBiYiLUqFFD+OOPP4Rp06YJZmZm+a5X1Pf+4MEDwdPTU7CzsxPMzMyE5s2bC/v27Sv0PRf1+l1cK//ifpa7du0S2rZtK1haWgqWlpZC3bp1ha+//lq4c+dO7nWKe54IgiCcOXNGaNmypWBubi5UqlRJmDFjRm6L+7yv0W/evBG8vLwEOzs7AUBuK+ai2i/nWL16tQBAsLa2ztdiPS+xr5szZ84UqlSpku93CZGhkQiCiqo7ieiDOnXqhEqVKuXW/hDpsnXr1sHb2xv//vtv7ukJad6UKVMQFBSEN2/eFFvMrmlXr15F48aNsWnTpg+m/gJA3759cfPmzSLr3Eh93r17BxcXF8yaNQuTJ0/W9naI1IY1MkQasmDBAmzbtk1UMT0RlT4FW0fHx8dj48aNaNu2rdaCmKLaWS9ZsgRSqbRQg4yC17137x4OHDgAd3d3dW6RirB27VoYGxsXmodEZGhYI0OkIS1atEBGRoa2t0FEOqpVq1Zwd3dHvXr1EBcXh+DgYCQnJ2POnDla29OiRYtw6dIldOjQAUZGRjh48CAOHjyI0aNHF6oxql69OoYPH47q1avj0aNHWLFiBUxMTDBjxgwt7b70Gjt2LIMYKhUYyBAREemAHj16YOfOnVi1ahUkEgmaNGmC4ODgEluDq1Pr1q1x5MgRzJs3D2/evEGVKlXg5+eX20kvr+7du2PLli14/vw5TE1N0apVKyxYsKDIeTVERKrAGhkiIiIiItI7rJEhIiIiIiK9w0CGiIiIiIj0jqgamezsbDx9+hTW1tb5BmAREREREVHpIggCUlJSUKlSpRKH1u7evRv79+9HcnIyRo4ciYiICERHRyMzMxMrV65UKrYQVSMTGxur0gnIRERERESk3x4/fgwnJydR101ISMDUqVORkZGBkJAQ/P777/j444/h5uam8O2LOpGxtrbO3ayNjY3CN0ZEREREVFoIgoD3Gdna3kaRBEHA/v9dx8uYFLm/Nj3jLeaEDMyNEcSYP38+vvrqK+zYsQMAULVqVcTGxsp923mJCmRyjnxsbGwYyBARERERlUAQBIT+fBnPo5K0vZUPMjexVPhrxaSFCYKAsWPH4tGjR4iOjoaFhQVOnz6NxYsXo1y5cnB3d0fFihUVun3OkSEiIiIyYLp8KmDIMt9l6XwQownLli3DxYsX8cknn+DChQsYPHgwhg8fDisrK7Ru3Rr29vYKry2qRiY5ORm2trZISkriiQwRERGRntCXUwFD572oLYxNZdreRj6CIGDP0quIi06W+2vTMlLx7do+cscGnp6e2LlzJ2rVqoXIyEgcOnQIcXFxGDFihNx7AHgiQ0RERGSwpxY8FaDiSCQS9JncSKHHfXJyMr5dK/76qampaN++PUxNTQEA9erVg5GREcqUKYP79+/Lffs5GMgQERFRqVZaTi108VTAkGW+y8LaGacBIPe/hiItI1Wu6//www8wMjJCVFQUAgICMHjwYIwbNw6pqan45ZdfFN4HAxkiIiIq1d5nZBt8EAMAxqYyBjIaZGQiRcUatnj2wPAfWx+yd+9eXLhwAaamphg0aBB8fHwAAP3791d6bdbIEBERUamW+S4LqyafBGB4pxZ5TwUM7XvTB4aaspicnIyKzuVExQZ9+vQBAKSnp+PmzZt4/PhxiUM0xeKJDBEREdH/Z8inFoaW3kTaI09qWatWreDh4YGrV69i7dq1KgtiAEB1KxERERGRTslJbyLSlpSUFAwaNAjnz5+HTCaDr68vateujTdv3ii9Nk9kiIiIiAyURCLBZ9ObGGR6ExVPEASELbmKFw/lb62sau3atcOGDRtw/PhxjB49GtOmTcOTJ09UsjYDGSIiIiIDJpFIDDZdztApWmPzPiNbJ4IYAOjatStiY2MRFxeHqVOnqnRtBjJERERERDpGVW3Bh8xvBSMT1Qay8syRkUqlSE1NhYeHB8zNzTFz5kycPn0aw4YNw5YtW2BiYqLwPhjIEBERaYChdi8yBJnvsrS9BVITfX7eqWqY6cbZ51Swm/zkKfYPDQ2Fv78/srKy0KtXL5iamkImk+Hu3bu4efMmGjdurPA+GMgQERGpWWkZuEikSwzpeSdv62xBELBn6VXERWs/vSwqKgqpqamoWLEiUlJScPXqVUilUmRkZKBBgwZKrc1AhoiISM1Ky8BFohy6cBKiqhMNfSSRSNBnciO1/QzkSS27du0a0tPTcefOHchkMhw+fBhSqRQfffQRzpw5gw4dOii8DwYyREREGsShhLon79BIUp4unoTo4/Mu7+NS1x6f8qSWrV+/HlKpFEuXLkVoaCgmTZqEZ8+eQSqVokWLFkrtg4EMERGRBhnywEUigCeQqmJkIkX5ajY6kR6mjD179mD//v1ISEhAdnY2YmJi8OjRI7x58waPHz9GnTp1FF6bgQwRERERqYU2T0J0+USjNMnOzoZUKkVWVhaMjIzwzz//oHfv3ggPD0eVKlWUWpuBDBERERGphTZPII1MpKhYwxbPHvB0SJv69euHfv36Ydq0aRg0aBCaNGmC7du3o3Xr1jA3N1dqbQYyRERERGRwJBIJPpveROtNB3SJqpowyFPsLwgCpk2bhn379sHd3R2rVq3CoUOH0KtXLwiCAIlEovA+GMgQERER/X+cKaM8XboPJRIJa9L+P1U2YZCn2H/ZsmXYsmULqlatip07d+Lt27fo1q0bnJ2dcebMGbRt21bhfTCQISIiIvr/WEtB6lCa21HXq1cPAQEB8PPzQ9euXXH8+HFERkbir7/+gqOjIwMZIiIiIkWxloLUSRfbUQ+Z3wpGJoqfVMmTWnbixAmEh4fDxMQEhw8fRtWqVXHq1Cl07NiRNTJEREREymAthWqVprk8Yk5adHEw58bZ55T6enlSy9zd3dGwYUOEh4ejUqVKOHbsGKpUqQJzc3N8/vnnSu2DgQwREeWjCykQhkaXagaoaKylIHkpctKizXbUgiBgz9KrGp9Lc/z4cYSHh+P58+coU6YMAKB79+548OABdu3ahQEDBii8NgMZIiLKpYspEEREukjfBn9KJBL0mdxI413LPvnkEzx58gRlypSBm5sbVq9eDSMjI6SkpCAuLk6pfTCQISKiXPr2i5mIdJshn0bm/d4+dNJiiIM55Uktu3PnDlq3bo0xY8bA09MT8+bNw7///osWLVrg7du3Su2DgQwRERVJmykQhqY01QwQ5VVaHvcfGvxZ2ptJpKSkYN26dbhy5Qri4+Nx8eJFpKWl4erVq5g2bZpSazOQISKiImlzIjcR6a/S/sa9IFU1kxAEAWFLruLFQ83WuCirXbt2WL9+PU6ePIkBAwYgLCwMrVq1grGxMerUqaPU2gxkiIiIiEhlSksXuLwnrZpIoXufka13QQwAdOnSBbGxsYiLi8PUqVPRs2dPXLp0CeXKlcNHH32k1NoMZIiIiIhIpUpbFzhNp9ApOwdGWfIU+0ulUqSmpsLDwwPZ2dm4dOkS3N3d8eeffyIoKAhff/21wvtgIENEREREJCdtptApOwdGWfIU+4eGhsLf3x9ZWVkYMGAAoqKiIAgCpFIp+vbtq9Q+GMgQEREREclJ0yl02poDoyxra2sMHToUu3btwv79+3HmzBls374dGRkZ8PLywsmTJxVem4EMEREREZEC1JlCV3A4sSrnwChLntSyEydOIDU1FWZmZrhx4ways7MRHBwMNzc3pKSkKLUPBjJEREREBqDgG1/SX7reoUye1DJ/f38AwLp167B69WpERUUhNjYWM2bMgJeXl1L7YCBDREREpOcEQUDoz5c50JZ0TnZ2NubMmYPk5GRkZGQAAFxdXTFjxgxcu3YNBw4cQI8ePRRam4EMERGRBhnypHPSnsx3WQxiSCeFhYXh/v37iIyMxKtXr7Bjxw5s27YNABAbG6twEAMwkCEiItKo0jLpnLTHe1HbUtX62BDpa2F/Ue7cuYOOHTti27Zt8PT0hI+PT+7ndu7cqdTaDGSIiIjUjJPOiUgeulTYXxR5iv2fP3+OI0eO4NixY3j58qVK98FAhoiISM1Ky6Rz0p68U+Z56kfqJk+x/4IFCzBx4kTIZDLExMSodB8MZIiIiDSgtE06J83iqR/pKgsLC/j6+qJPnz4oX748AGDz5s04fvw43r17hxUrVuDff//FnDlz0KBBAwwcOBDu7u6i1mYgQ0RERKTneOpHYqiqRbc8qWWCICAoKAhLlizBypUrAQB//vknduzYgX379iE0NBRVqlSBlZUV0tPT4eTkJHofDGSIiIiIDABP/fSPJmf/qHI2jTypZcuWLUN4eDiSkpIQFRUF4L/HKgBUrVoVERERGDRoENq3b4+4uDhMnToVISEhotZmIENEREREpGGlZfbPpEmT0KtXL/j7++PFixcAgJiYGIwaNQoPHjyAl5cX0tLSMH78eMhkMty7d0/02gxkiIiIiIjy0MRJSWma/WNrawtjY2MkJiYiICAAgiDAyMgo92TGx8cHcXFxsLW1haWlpeh1GcgQEREREf1/2jgp0cTsH23OpnFwcMDKlSvx6tUr+Pj44MqVK1ixYgUiIiKwb98+VKxYESNGjECjRo3g5eUlel0GMkRERERE/9/7jGyDPClR5WwaeYr9b926BT8/Pzg4OCA2NhaBgYE4cuQIbty4gZiYmNzi/tjYWDRq1AjZ2eL3x0CGiIiIiKgI6jwp0efZP/IU+x88eBBDhw7Fvn37EBERgU8//RQdO3bEvHnzYG5ujuXLlwMAJkyYgP3796N3796i12YgQ0RERERUBGNTmdoCmdIy+2fIkCGYNm0arl69CnNzc/j4+KBbt26IiYnBjh07YGlpiRMnTuD+/fto0KABKleuLHptBjJERERERBqmK7N/FGlsIE9qmaOjIzZu3IisrCw4OzsDAFq1aoUaNWrkXkcikXCODBERERGRvtDU7J/ighVFZ8vIk1r28OFDLFiwAKmpqahduzbWr1+Pffv2IT09HZ07d4arqyvc3Nw4R4aIiIiIdJcmB0AqKvNdlra3oFLanlfj4uKCWbNmwd/fH/fv38ewYcMQHx+Pn3/+Obf9slQqRWpqKjw8PGBubi56bQYyRERERKR22n5DXRoUFSiKnVczZH4rGJmIOx2SJ7UMKDxHplKlSpBIJAgKCkLNmjVx7949+Pv7IysrC7169RK9LgMZIiIiIlI7Q21rrCvEBIoFu7Dl7Zy2cfY50bclT2oZUHiODABER0fD09MTrq6ueP78Ob799lukp6ejbNmyotdlIENEREREGqWJAZCKyvvmXp8oEigamUhRvpqN2odkRkVFwd/fH+fPnwcABAYGIigoCB9//DFcXV2xc+dOnD17Fi9evECFChXQo0cPSKXSkvev1l0TERERERWgzrbGhkLeeqK8tT15A0VdmFdTUmpZUFAQAGDNmjXYsGGDqCAGYCBDRERERKRTlK0nyhsoanteza1bt+Dn5wcHBwe4uroiNTUVZ86cQaVKlTBv3jw0bNgQALBnzx7s2LEDo0ePFr02AxkiIiIioiJoq4OZ2AL9D319Xr0mfqzSbnHyFPsfPHgQEydOhJubGypVqoQbN25g9+7d+OKLLxAeHo5PPvkEANCnTx/06dMHPXv2hJeXl6i1GcgQERERERVBF2plxNYTaTKFTJ5i/yFDhsDHxwcTJ07E69evYWxsjM6dO+P69eu4c+cOevXqhfj4eISGhuLdu3fo0aOH6LXFJaAREREREZVAEARkvssq9kMf5KRi6YqcNLGSPsytjXVq3zkcHR3h6+uLxo0bw97eHjt27ECPHj1gbm6OkSNHomHDhqhSpQpSU1MRHx+Pr7/+WvTaPJEhIiIiIqUZypwYiUSCz6Y30ergzrynK/IEgKpOISuOPKllDx8+xE8//YSMjAzUrl0bS5Yswf3791G+fHl06NABAFC9enUEBwfD09NTrn0wkCGiUksfJkxrmr78xZSIdI8hzYmRSCQ601VNF9LbCpIntczFxQWrVq0CAHh6emLKlCkAAD8/P9HdyYrDQIaISiVD+cshEZEuKqquQ1/ns2iDtjuNqVrBOTKbN2/Gnj17cOnSJWzduhUxMTFYunQpzp8/jxUrVmDcuHGi1mUgQ0SlkiH95ZCISNdwToxyFE1vEwQBYUuu4sVD9Q64lFfBOTKhoaGQSqVITEzE0qVLMWbMGACAqakpgoKCGMgQEYmlyxOmNY1/MSUi0g3ypLflpEq/z8jWuSAGABwcHLBy5Uq8evUKPj4+uHLlCrZv346IiAjs27cPDg4O6NGjBx49eoQhQ4aIXpeBDBGVevzLIRER6aviUqWHzG8FIxP1/G6Tp9g/70DM2NhYeHh44O7du5g+fTo6dOgAJycnAJwjQ0RERESkN1TRdKa44ZkbZ59Tat0PkafY/+DBgxg6dCj27duHiIgIVKtWDVWrVsWZM2fw6tUrLF++HCdOnFBojgwDGSIiIiIiDVNH05nhC9vg4MoIxEXrTnpZ8+bN4e3tDSsrKzg7O6NHjx5ITk5GaGgohg4dCktLS7i7u8Pd3V3utRnIEBEREZFGsdV78ScpypBIJOgzuZHaRwvIk1p24cIFrF27Fq1bt0blypWxaNEiAMCJEyfg6uqq1D4YyBARERGRRrGpSH7KNJ3J26RFU/erPKll7du3x7BhwyAIAsqVK4dhw4bhzZs3sLCwQJs2bZTah3JTaIiIiIiIRMiZjUKqZWQiRflqNtreRrGaNWuGvXv3okWLFoiPj8f69etx48YN1K1bFyNHjkRWVhaePn2KQYMGYejQoTh+/LjotXkiQ0RERKQGqijk1iclpYspOhvFUGnjJEUbHj58iJ9++gkZGRmoXbs2UlJS0KBBA2zevBmTJ0/G06dPsW7dOsyaNQsNGjTA4MGD0aFDB1FrM5AhIiIiUjF1FHIbAnlmoxi6nBOqZw8M+zHi4uKCVatWAQA8PT1hbW2N0NBQ3L59G+/evYOzszNiY2Ph7OwMqVS+ZDEGMkREREQq9j4jm0EMfZAqT6g0ffonT7H/qVOnEBISgvfv3+P48eOYP38+Ll26hCtXruDYsWMAACcnJ8TGxsLGRr4UOQYyRERERGqkTCG3PsmbKkWaIwgCwpZcxYuHmmu5LE+xv5ubG+rXrw8vLy9IpVJs374dL1++RJ06dTBu3DgEBwdj5MiRmDVrFoyMjPDVV1+JXpuBDBEREZEaGZvKSkUgQ/IpLemHOScy9+7dQ/Xq1ZGamgqpVIoHDx7g1q1bsLa2BgBs2LBB7rUZyBARERERaZg60g+HzG8FIxP1B83ypJa5ubmhatWqePDgATw9PeHn54fevXvD2NgYwcHBmDJlisL7YCBDRERERKWKLnSUy9vlTVVzZDbOPqeSvZVEntQyAAgODkZWVha8vLwQFRWFpKQkHD58GO7u7krtg4EMEREREZUaupjSpUz6oa53P4uJicG5c+fw5MkTLF++PPfy1NRUxMbGKrU2AxkiIiIiKjUMraOcmO5n2mgIkCMiIgKWlpZYu3Ytli5dCjMzM1hYWMDIyAgTJkxQam0GMkRERERUKmmzo1zelLCShokq631GtlaCGABo2bIlFi9ejNmzZ2PIkCHw9vbGhQsXUK5cORgbGyu1NgMZIiIiIiqVdKWjnCbbVquiIYA8xf6BgYHIzMxE7dq1ERgYCG9vb3z77bdIT0/Hjz/+qNQ+GMgQERERkUqp+4RBGbqyN23VtqiiIYA8xf6VKlVCRkYGTExMkJGRgevXryM+Ph5169ZVeh8MZIiIiEgtdKEzlLboyptlbeFgzJKJqW1RFUEQsGfpVcRFaz69rH///li0aBH27duHrl27YtSoUWjatCnCw8MRGxsLJycnhddmIENEREQqp4udoUi9dL17li6SSCQaS237fEZTUUGTmD9AyJNaNn36dNSrVw/169fHxYsX8ezZM7Rq1QpZWVkICgrCvHnzxC1UBAYyREREpHKG1hmKSqbJEwZl5C2yN2SKnIiK7W4mT2pZmTJlEBERARcXFzx//hyzZ8/G1q1bkZWVhRkzZsi1v4IYyBAREZFaabMzlLaUljfLBWnyhIGKp0snok2aNMGjR49gbGyMMmXKYPbs2ejXrx/Kly+P4OBgTJkyReG1GcgQERGRWulKZyiiggy1linzXZbSQcyHupvJk1pmYmKCGzdu4OHDh3Bzc0NiYiJOnjwJIyMjzJo1S6k9MpAhIiIiolKpNJyayXMimvck8UPdzeRJLXv48CGmT5+OMWPGwNPTE5GRkZBKpZg6dSokEonodYoiVeqriYiIiIj0SE5TAirMyESK8tVsVLpmpUqV8Ndff2HixIl4/PgxlixZgiZNmiA7Oxtv3rxRam2eyBARERFRqaEvTQmUkfdkRdunTgVTy65duwapVIrY2Fi0bNlSqbUZyBARERFRqWLoTQl0qRV2wdSynTt3ws/PD56enpBKlUsOYyBDRERERKRnSmqv3Gvix2o7dZKn2N/JyQkJCQkYOXIk/vnnHwDAmTNncP36dWRlZWHt2rX4+++/sX//fiQnJ2PkyJHo2rWrqLUZyBARERERQbHZK9ogdt6LushT7N+vXz9MnDgRFhYWKFu2LNavX4+EhATY29ujRo0auH//Pvr27Yu+ffsiISEB06dPZyBDRERERCSWLs1eMSQWFhbw9fWFv78/Xr58iWHDhuHo0aO4dOkSzp49m9uC+dmzZ6hbty5Wr14tem0GMkRERERqZKizSgyNKmavaMOH5r2oizypZQBQvXp1BAcHw8nJCQCwYcMGAICHhwfWr1+Pb775Br1790b37t1Rv3590esykCEiIiJSI213jSL5yTN7RRvEzntRF3lSywAgPj4evr6+SExMREBAABITE/HkyRPcv38f69atw6BBg5CSkoIHDx5g+/btcHV1FbUu58gQERERqRhnlZA6qWPeizolJSUhMzMTdnZ28PHxwZAhQxATE4MKFSqgfPnyePr0KUxMTBAdHY27d++KXpcnMkREVCSmw5AySvvjpzTMKjE0ujR7xdDY2trC2NgYiYmJ8Pf3x9KlS1G1alXEx8cjNjYWJ06cAACMGTMGxsbGotdlIENEREXiL3Ii5Rj6rBJDo0uzVwyNg4MDVq5ciVevXsHHxwdPnz7Fr7/+Ci8vr9y6mffv3yMjIwOzZ88WvS4DGSIiysVf5ERUmqlz9oohkbfYPyoqCv7+/jh//jyioqJw4MAB/P3333j06BH27t2LJ0+eYOnSpahcuTLi4+Ph7Owsal0GMkRElIvpMKQqedN0iD5EF2a3aHsui76Rt9g/b2rZjh07EB0dDQCoWbMmGjVqhOPHj6N8+fKws7NDQEAAtm3bJmpdBjJERJQP02GISFM4u8XwxcTEYNKkSbC3t0e1atWQlZWFESNGICYmBq1atYKzszMCAwPx/v17jBo1Cj/++KPotRnIEBEREZFWvM/I1rkgRhtzWfSNPKllERER8PT0xODBg+Hs7IybN2/C19cXEydORJ8+fQAAmZmZ+PrrrzFlyhTRaWUAAxkiIiIi0gHanN2i7bks+kae1LKWLVvi008/xcyZM5GZmYm0tDQ0bdoU9vb2+Ouvv9CqVSsEBgbi7t27WLFiBTp16oQvvvhC1NoMZIiIiIhIFFXXs+Rt021sKtNaIMNGJ+oTGBgIAOjWrRv+/fdf3Lt3Dw0aNMA333yD+Ph4ODk55V5HXgxkiIiIiKhEhlzPoouNTgylAUGlSpWQkZEBExMTZGRk4LPPPkN4eDg2bNiAxYsXK7U2Axkq1QRBQFpm6R7aVlplZmQhAwJMINH2VoiI9IIu1rOokryNTtTdbe19RrbeBzEAMHDgQOzcuRN3797FrFmz4O3tDT8/P3h6esLV1VWptRnIUKklCAI8V57DpUcJ2t4KaYsd8G2iubZ3QUSkd1RVz6Kvbbo1fTqlaw0I5Cn2nzNnDqytreHo6Ijp06fj9u3bOHr0KLZu3YqlS5eiW7duCu+DgQyVWmmZWQxiiIiIFKCOepa89TK6LvNdlkZPp3StAYE8xf7jx4+Hn58fHj9+jCZNmqB+/fo4evQokpOTUb58eaX2wUCGCMDF2Z1hoUN/6SD1y3yXhTXf6t9fAYmIDJU+nswA6uu2JggC9iy9irho/U4vS0hIQNmyZXH58mWUK1cONWrUQLVq1SCTyRjIEKmChYkMFiZ8OpQmmYKE9TFERFpmCN3C1Nlt7fMZTfE+I1vt9Tjykie1zM3NDVWrVsWDBw/g6emJJUuW4N69e2jTpg3s7e2V2gffuRERERGRVuhitzAx8tb2qDslThe7l8mTWgYAwcHByMrKgpeXFxYtWoSPPvoIPXv2REhICEaMGKHwPhjIEBEREZHWyNstTNfoa0qcpmRnZyM+Ph5xcXEIDQ1FxYoVUblyZaxfvx4NGzZUam0GMkRERKRW+lTETcXjz/H/aCslTle6l8mTWhYWFoZTp06hYcOGcHJywvv373HmzBm8f/9e4UGYORjIEBERkVrxL9ZkaDSZEpc3jU1XupfJk1p2584djB8/HmPGjIGnpyfi4+MRGRmJQ4cO4fDhw0wtIyIiIt1iCEXcRB+iqZQ4fX8uOTk5ISEhASNHjsQ///yDxo0bY9SoUXj9+jWeP3+OYcOG4dKlS/jll1/g7OyMxYsXi16bgQwRERGpnL4WcVPx9HV4pb5T5XNJG40D+vXrh4kTJ8LCwgJly5bF4MGDcfz4cdja2sLR0RFPnz5F8+bNsXDhQvz+++9yrc1AhoiIiNRC34u4iXSBKlsvv8/I1nj3MwsLC/j6+sLf3x8vX75E//79ERsbiz/++AOurq5wdnbG7t27sXXrVly5cgVdu3ZF165dRa3NQIaIiIiISAcJgoDQny/jeZTq08qUaRwgT7E/AFSvXh3BwcFwcnICAHTt2hWbNm2Cj48PAKBv375o1KgRFi9ejG3btjGQISIiIiJSlC4Mocx8l6WWIAZQrnGAvHNk4uPj4evri8TERPj7++P3339HhQoVEBAQgMDAQLx9+xZz587FkSNHMHr0aNHrMpAhIiIiIspDnSchivJe1FbpVE1BELBn6VXERWsuvezWrVvw8/ODg4MDXF1dYWxsjJ49e+LUqVOYPHkynJycIAgCnJycsHnzZnTu3Fn02gxkiIiIiEguhj5TRp0nIdokkUjQZ3IjpU+a5EktO3jwIIYOHYp9+/YhIiICn376Kdzc3HDo0CH4+fkhMDAQx48fR3h4OJKSknD//n2MHTtW1NoMZIiIiIhILqWpe5kqTkIUlbdTnC7d5/Kklg0ZMgRz586FtbU1GjVqhGnTpuGbb77Bxx9/jE2bNsHOzg4NGzbEpEmT5N6HVO6vICIiIqJSJ2eeCWmOkYkU5avZaHsbSnn16hVevnyJ5ORkZGRkwMTEBE2bNkVUVBTCwsKUWpsnMkRERERUotI0G0hXT0L0UUhICNLT0/HmzRuYmJhgzJgxuHHjBt68eYPu3bsrtTYDGSIiIiISpbTMBso5fXr2wPDqZDRt8uTJmDt3LiwsLBAVFQUjIyMcP34cXl5eKF++vFJrM5AhIiIiIspD0dMnXWjZrAnyFPs7Ojpi0aJFaNeuHaRSKY4fP47y5cvDwcEBBw4cQI8ePRTeBwMZIiIiIqICSjp9Khi0CIKAsCVX8eKh5loba4s8xf4PHz5Ev379IAgC+vfvj5kzZ2L48OFITExUKogBGMgQEREREclFF+fM6KqjR4/C3t4eiYmJePToERYtWoSzZ8+iVq1aeP36Nezt7RVem4EMEREREZEc3mdkfzCIGTK/FYxMDLeWSJ7UsqioKLi6uuLmzZvYu3cvHj16hFu3bqFu3bq4f/8+mjdvrvA+GMgQERERkV7Rdi1K3oGgOXNm8nY62zj7nLa2phHypJb5+/tjz549OHr0KAYMGIDFixcjLCwMx44dw6effqrUPhjIEBEREZHe0LW0LmNTGYxNZex0VozIyEgcOHAAderUwV9//QVnZ2cMGDAAT548wYULF1CvXj2F12YgQ0RERER6o6S0Lm1R5ZwdQ2ocEBcXBxMTE9jb28Pc3BxRUVGIiYmBqakp58gQERERGSJtp0/pqqLSurSxh5w0srz7UZX3GdkGEcQAgLu7O5KTk7FixQp88803GDhwIKRSKaZOnYq4uDilZskwkCEiIiLSMbqWPqWrctK6tCknoFEXXWwcIE+x/+7du7F//37Y2NhgyZIl+PvvvwEAN27cwOeff67UPhjIEBEREekYXU2fov9osh5GFxsHyFPsb2dnB3Nzc2RmZkIqleLo0aPo3LkzMjIykJWl3GkWAxkiIiIiHaat9CldlTetS1tUWQ9TFEEQsGfpVcRF6396mUwmQ0ZGBi5dugSJRIJx48YhJiYG9erVw9OnT5Vam4EMERERGTx9qzfJW3ehC+lTukod9Sm6QCKRoM/kRjr7mJUntaxt27bYt28fPD09UaFCBYwZMwYAMGfOHNStW1epfTCQISIiIoPGehPDpe2TmdJKntSyZcuWITw8HImJiZg4cSLmzJmDV69ewdHREY0aNVJqH1KlvpqIiIhIx7HexLDk1KeQfpg0aRKWLl0KIyMjnD59GvPmzYODgwOePXuGcePG4e3btwqvzRMZIiIiKjX0pd5EF+pAdJW661MMkbbn0hw5cgRr165FYGAggP86lu3evRvbt29HaGgoBg8erNC6DGSIiIio1NDHehNDrQMhzdH2XJq5c+cCQG4g069fP0ycOBEAULlyZYXXZSBDREREpMN4MkOqpIq5NPIU+wNAVFQU/P39cf78eQDAq1evcObMGfTt21epgn8GMkREREQ6RpNzSqh0UcVcGnmK/QHA1tYWxsbGSExMREBAAMzNzSGTyXD79m3Mnj1b4X0wkCEiIiLSMawDIVXS5lya3bt3Y//+/UhOTkbjxo2RmJiIu3fvIi0tDd9++y2kUsV7jzGQISIiItJBEolE7+p5SLPEzkdS9VwaeVLL+vbti759+yIhIQFNmjTBwoULAQCtWrVCYmKiUvtgIENEREREpCbqGsaqzU5k8qaWxcfHo2PHjsjMzMR3332Hc+fO4c6dO/jf//6HsmXLomHDhgrtg4EMEREREZEacBgrcPPmTXz++eeoVasW5syZA09PTwDAokWL0KJFC4WDGICBDBERERFRIao4Scl8l6WRIEYVncjkIU9qma+vLwRBQOXKleHn54cTJ05AIpEgNTUVEyZMUGofDGSIiIiIiPJQx0mKqoex5h2aqopOZPKQJ7Vs1apVmDt3LiwsLGBra4vff/9dZftQvE0AEREREZEBep+RrfPpYEYmUpSvZqPtbZTo7NmzyMjIwMOHD5GdnQ1fX1/Url0bb968UXptnsgQERERkdLUVdSuDZnvsnL/X5mTlLynJqV1sGmjRo1w4MABAIC9vT38/f3x5MkTlazNQIaISr28v7CIyPDwOa5+hlzUbmwqUziQ4WBTIC0tDVKpFNeuXcPnn3+OmTNn4ty5c2jdujV+/vlndOvWTeG1GcgQUalXWv9KRkSkKvqQiqUNOYNNM99lGcxpFSBfsX/dunVza2M2bNiATZs2IS4uDs+fP0fnzp2V2gcDGSIqlfhXMiIi9VB1Ubs25E0JU/ZET5vzXtRFnmL/ZcuWYdeuXdiyZQs6d+6MwMBAHD9+HPb29oiLi0OlSpUU3gcDGSIqlXL+SmZIfyEjoqLlfVNK6qdMKpYu4mNHOVWqVEGHDh2QnJyMmzdv4saNG7CwsECXLl2UCmIABjJEVIpJJBKD+mVLRKQMZYr1Da0OSV2n9pqe96Iu8qSW2dnZwdzcHJmZmZBKpTAyMkJcXBwaNGig9D4YyBARERGVcoZcrK8IVZ7aa3Pei7rIk1rm7u4Od3d3TJs2DcuXL0ejRo0glUoxYMAA9O7dG/b29grvg4EMERERUSnHYv3CVHVqX9prMm/dugUvLy9YW1ujZs2aCA0NxYsXL3Djxg0kJCQwkCEiIiIi1VCkWJ91SMVTxemOPjcMCA8Ph0wmQ/369REUFIT4+Hh4eHigVq1aOHfuHGrUqKHw2gxkiIiIiCiXoRXrq4smB4C+z8jWyyAGACZNmgQXFxesWLECM2bMwPv37/Hvv//C2dkZsbGxSq3NQIaIiIiISA7arCnShYYB8hT7R0ZG4sCBA7C0tMSCBQswYsQInDlzBn379kXdunWV2gcDGSIiIiJSGUPrYFaUzHdZWqsp0oWGAfIU+8fFxcHExAT29vYwNzeHVCqFTCbD7du3MXv2bKX2wUCGiIiIiFSmtNXKaGIAqCAI2LP0KuKi9S+9zN3dHS9evMCoUaMwatQobN68GYIgoFy5cpBKpUqtzUCGiIiIiJRS2jtzqZtEIkGfyY10ZoizPKllAHDjxg34+vpix44dsLKyQmJiIhwdHZXeBwMZIiIiIlKKKueu6IO8XdpK2wkUIF9q2eLFi3HhwgWkp6ejWbNmaN68OZYvX46goCC0b98ebdu2VXgfDGSIiIiISGmqmruiD3gCJV5CQgIqVqyIw4cP4+7du1i+fDm2b9+Ot2/fYtOmTQxkiIiIiMQoDYXoiuD9Ih99OoHS9gwaf39/AMC6deuwfPlyDB8+HLdv34atra1SM2QABjJERERUipTGNCBSD10/gcqZc6MrM2iGDx+Offv2YcOGDcjOzoZUKsXUqVNx/fp1fPTRRwqtyUCGiIiIDBrTgKi0KW7Ojapm0Mhb7B8VFQV/f3+cP38ewH91M3fv3sXZs2fRtWtXBjJERERERdGnNCBtyVu8TtqXc5qiqOLm3KhqBo08xf4AYGtrC2NjYyQmJiIgIAC3b9+GhYUFbGxsYGdnp/A+GMgQERGRwdP1NCCiHMWdpihq+MI2OLgyQmszaE6cOIE5c+agQYMGaNq0KXx8fJCRkYFvvvkGZcuWRd26dRVem4EMEREREZGOeJ+RrbIgBgBMzIzw+YymJZ7wyHMKJE9qmUQigZWVFdLT02Fubv7fnkxMcruXhYWFYdiwYeIWK4CBDBERERGRDvJe1Fahk8S8qYJiOtLJ29lMntQyNzc3uLq6YurUqThz5gwCAgKQmJiIt2/fIiEhAb/88ovotQrS20BGEASkZbJVICnubcZ7bW+BiIiItEjZWhR1yBt4GJvKlE6J1Hbt0507d7B06VKkpKSgVq1a8PHxUdnaehnICIIAz5XncOlRgra3QkRERER6SNW1KLpElzr1RUZGQhAEGBkZwcHBQaVr62Ugk5aZxSCGiIiIiBSm6loUXSJvpz5BELBn6VW1NATo168fjIyMsGLFCnh7e6t0bb0MZPK6OLszLFTQD5tKn7cZ79Fs/lFtb4OIiEiniKmpMAR5v09Fa1HUQd76FlWQSCToM7mRWor9AcDV1RWVKlXCtGnT4OXlpeAuC9P7QMbCRAYLE73/NoiIiIh0grZrKrRBFbUo6qCrPwt5iv1PnDiB0NBQGBkZoUKFCirdh1SlqxERERGR3smpqSDtM7Sfhbu7O6ZMmYL379/j5cuXAICZM2di7NixaNy4MQ4dOqTw2jzKICIiIirl5K2pMATaSOESq9fEj3X6ZyFvaln16tURHBwMJycnAMDChQsBAL169ULnzp0V3gcDGSIiIiKCRCLRyfQqTdDVFC5dJU9qGQDEx8fD19cXiYmJCAgIgI+PDy5cuIAmTZpAJlP8McfUMiIiIiIqdQwthUtXRUZGwtfXF69evULt2rWRlZWFESNGoH///vDw8FBqbZ7IEBEREVGpIyadThAECGlpGtyVfpAntaxevXrw9/fHd999h0OHDmHHjh04ffo0OnXqhLi4OKX2wUCGiIiIiPSOqoKM4hKbBEFAjPcIpF+/rvRtGJo3WfLVFJ05cwYxMTEICgrCmzdv8Omnn8LCwgKdOnVSah8MZIiIiIhIrwiCgEdeg5B25Yq2t0IliIyMxIEDB2BpaYkFCxbA1tYWjRs3RlxcHH799Vd8//33Cq/NGhkiIiIi0itCWhqDGD0RFxcHExMT2Nvbw9zcHK9evcLp06cRERHBGhkiIiKi0qw01nFk5/l+a505Dam5ucpvQxAExIwYifRr11S+dmni7u6O5ORkrFixAt988w1++OEHXLx4EYcOHUJERAQ++eQThddmIENERESkp5hiBUjNzSG1sFDL2i5bt6g9SBQEAUJ6ulpvQ9WSU1KAatVEXXf37t3Yv38/bGxssHTpUmRlZWHs2LF48OABT2SIiIiISiumWOU/ndE3+tpQQJ5ifzs7O5ibmyMzMxNSqRQVKlSAsbExkpKS0KxZM6X2wUCGiIiIyACoK8VKF2WnpeFem7YAkPtf0k2JiYlIS0vDuXPnMGnSJJw8eRIHDhyAkZERmjdvrtTaDGSIiIiIDIA6U6x0jcTcHOZNmiDt8mVtb0VlaoQf0ZtAVJ7Usk8//RTnzp3DTz/9hO3bt+PMmTMYNGgQoqOjER4ejr59+yq8DwYyRERERCqmqQJ8fU6rUoZEIkHVkE163+Qg78nSg85dtLwb8eRJLVu2bBnCw8Px119/oWvXrnj//j3evHmDe/fu4d27d0rtg4EMERERkQqxAF8zJBIJJHp+AmWIJ0sFTZw4EQ8fPsS+ffvQunVrREdHo0qVKrh48SLMzMyUWpuBDBEREZEKsQCfxCruZElfmwAUZdmyZdiyZQuqVq2K8PBwODs749ixY3jx4gW6d++u1NoMZIiIiIjURN0F+HlTk8hwZuoI6ekGEcQAQL169fDrr78iPT0dDg4OOHLkCA4fPgxPT0+YmpoqtTYDGSIiIiI1KU0F+NpmqCl9utgEQJ5i/xMnTiAxMREbN26Es7Mz0tPTcezYMTx58gTnz59Hy5YtFd4HAxkiIiIiA1BaC/9zZBtoSp8uNgGQp9jf398f33//Pbp37466devixx9/BAA4OTmx/TIRERERcZ5KXvo+U0cQBMSMGIn0a9e0vRWlHTlyBA4ODnj+/DmePHmCH3/8EV999RXKli2Lzz//HMuWLYOTk5NCazOQISIi0nGGkvdfWmjyZKQ0dL0qjSQSCaqsCYaQnq7trRRJ3tSy1NRUmJmZ4caNGzh27BhOnz6NyMhIbN26FRcvXmQgQ0REZIgMNe+fVMNQ5qmoQt7GBzydUi95U8sAYN26dVi+fDm+/PJL1KlTB05OTqhevToOHDig8D4YyBAREekwtvKlkhjCPBVV4OmUboqMjMTSpUvx6tUrZGVlIS4uDo0aNULNmjWxYMECBAcHY8qUKQqtzUCGiIhIT+h73n9pwZbI2qHrp1OGNBtGHvXq1YO/vz++++47HDp0CN9++y3KlCmDR48eITAwEN9//73CazOQISIi0hNs5Uv0Ydo8nSqpls2QZsPI68yZM4iJiUFQUBDOnDmDQ4cOwcvLCzt37lRqXQYyRERERERKkLeWTRdnw8hDnmL/3bt3Y//+/bCxscHSpUvh4uKCDh064Nq1azhw4AB69Oih8D4YyBARERGRQVN35z95Z9jo4mwYechT7G9nZwdzc3NkZmZCKpViypQpuHv3Lnx9fZUKYgAGMkRERERkwDTd+a+4WjZDmg0jj/LlyyMjIwNnz55Fnz59MHLkSLi5ucHIyAhZWVmQyWQKr81AhoiIiEhNNDlThoom72mJuuj6bBh5yJNaVrduXdja2uL333/H4sWL0axZM5ibmyMxMRH79u3Dp59+qvA+GMgQEZHGccCjeHwjrN/YvUy3qKvzX2mbYSNPatmyZcuwa9cubNmyBZ07d8aaNWtw+/Zt7NmzR6kgBmAgQ0REGsYBj2ToOM9Ed6mr8x9/5sWrUqUKOnTogOTkZFy9ehWffvopbt++jd69eyu9NgMZIiLSKA54JEOn6/NMSpu8pyXqPOF0Xr3KINLGxJAntSxvsb+pqSkuXLgADw8P7Nu3D9OmTUOVKlUU3gcDGSIi0hoOeCwZhyvqJ23OM6Hi8bmkGvKklrm7u8Pd3R3Tpk3D8uXL0aRJE5w6dQqVK1dWKogBGMgQEZEWccAjEakb076069atW/Dy8oK1tTVq1qyJ0NBQhIWFoVGjRkhNTYWlpaXCazOQISIiIiKDJU+qnyAIiPEegfTr1zWws9IhPDwcMpkM9evXx4oVK3D37l0MGzYMMTEx2Lp1K0aOHKnw2lIV7pOIiIiISGMEQUD227clfoitVxLS0xnEqNikSZMwZswY7N+/H2ZmZujTpw8yMjJgZWWF//3vf8iSI02tIJ7IEBEREZHeUXcHxBrhR1jDVwx5iv0BYPTo0Rg9ejQqVqwIFxcXrF27Fvv378fSpUvx9OlTODs7K7QPBjJERER6gjNliP6PugddPujcRW1r6zt5iv1PnDiB0NBQvHv3DhUqVEDfvn0xbtw4PH/+HM7OzgoHMQADGSIiIr3BjktERVNVB0RBEBAzYiTSr11Twa4I+K9rWZUqVeDv74+XL1/Cy8sLjx8/xrFjx9CsWTOlCv4ZyBAREekwdlwiKpkqOyC6bN2i0AwgQRA4R6YYtra2MDY2RmJiIvz9/TF//nx8+eWXePbsGf744w9MnjxZoX0wkCEiItJhHK5IVDRNDboUo7R1O5MntQwAHBwcsHLlSrx69Qq+vr64du0a1qxZg4iICOzbt0/hfTCQISIi0nEcrkj0YUy71F0xMTGYNGkS7O3tcefOnUKfc3JyUnhttl8mIiIiIr2Tk3ZJui0iIgLdunWDiYkJoqOjERAQkFvwv2PHDvTr10/htXkiQ0RERER6R5fSLtkkoHgtW7bE4sWLIZFIsGzZMnh7ewMAvLy8lF6bgQwRERER6SVdSrtUtEmAOqmrAYE8xf5r1qyBk5MT7Ozs8L///Q+3b99GUlIS/vnnH/z000/o1q2bwvtgIENEREREpCR1BVWCICjcRU1dDQjkKfY3NjbG33//DVtbW9StWxcLFy4EAPTq1QudO3dWah8MZIiIiIiIdJAgCHjkNUitgz/VLS0tDfXr10fNmjVx/vx5eHt7IzExEbGxsUqvzUCGiIiIiEgD5D1dyU5LU0kQUyP8iEoGhuaQJ7UsLi4Ob9++hbGxMSwtLbF27VqMHj0adnZ2ePr0KZydnRXeBwMZIiIiIiI1U/Z0pdaZ03IFI3nn7Dzo3EWh2yyOPKll9vb2eP/+PTIzM5GUlISUlBRER0ejRo0aSgUxANsvExERERGpnaCi0xWxJGZmMPv4Y43dXnHevXuH+Ph4ZGZmwtbWFiNHjkRsbCyOHDlSaK6MvHgiQ0RERERqo2ixuqHJznMfiD1dyXuqoq9DP9u1a4f169fj5MmTGDBgAIKCgtCtWzecOHEClpaWSq3NQIaIiIiI1MIQitXVQWpuDqmIDmc5Qz/TLl/WwK7Uo0uXLoiNjUVcXBymTp2KZ8+eYfv27WjXrh2cnJyUWpuBDBERERGphabTqQyNOod+KjNjRp5i/zNnziAkJATv3r3DX3/9hc2bN+O7775Dq1atMGDAAKVOZRjIEBEREZHayVusbmjypollaznVTtkZM/IU+798+RKCIODevXsYNmwYNm7ciO7du6NXr14IDQ3FkCFDFNoDwECGiIiISG/oW71J3jfsYtOpSgN9rXdRRN++fXHp0iXExcWhdu3aOHv2LJycnBAUFIRmzZoptTYDGSIiIiI9wHoT/aar9S6KzJiRJ7UsLCwMt2/fhpmZGWrVqoUnT57g/Pnz+OGHH/Ds2TNFtpyLgQwRERGRHmC9iX5TZ72LvJSdMSNPatmdO3fw7t07BAUFITAwEJUrV8a4cePw9OlTNGnSRO7bzouBDBEREZGe0Zd6E12qC6H/kzNjJv3aNbXflpOTE/r3749JkybB3NwcaWlpkMlkyMjIQDWRpzrFYSBDREREpGf0sd6kNNWF0P/p168f2rRpAwsLC9SvXx83btxAVlYWXr9+jQoVKii1NgMZIiIiIlILXa0LIc05c+YMZs6cifT0dMhkMpibm2PJkiXYu3cv4uPjlVqbgQwRERERqYUu1YVQycTOlpGn2P/EiRN4+vQpDh8+jKysLHTs2BGbN2/G8uXLYWVlhT59+uDRo0fw8/ODg4MDOnXqBE9PT1FrM5AhIiIiIrWRSCSQ6FkaXGkkCAJiRHbFk6fY39/fHwCwbt06rF69Gs2aNcMPP/yATp06oWfPnggNDcWLFy8wceJEuLm5oU+fPgxkiOTxNkP8E5KIlJOdkYV0mQnMsjK0vRUiIoMndvZQthq74kVFReHUqVN49OgRpk6diu3btyMrKwurVq1Cs2bNMGbMGPTs2ROCICAlJUX0ugxkiAA0mx+u7S0QlS69F+Dg7una3gURkUFTdPZQSbNl5EktA4Dq1asjODgYTk5OAFBolsy5c+fg6uqKMmXK4MKFC6LXZSBDpZa5sQzNqpbBxUcJ2t4KEREpQN+m3CuL7YsNiyYev4qespQ0W0ae1DIAiI+Ph6+vLxITExEQEFBolsz58+cRFRWFKlWqwMTERPS6DGSo1JJIJNgxthXSMplWRqRJ2W/T2IaVlMYp96TPtPH4LWn2kCAIiBkxUi2zZZKSkpCZmQk7Ozv4+Phg27Zt+Oeff/DVV1/B2toaGRkZOH/+PCwsLJAuotlADgYyVKpJJBJYmPBpQKRJ2e9lrI8hpXHKPWmCuk5N1FmPoiiJRIIqa4JV3rUMAGxtbWFsbJx7IjNjxgwEBgbi0KFD2Lx5M7Zs2QITExOYmZkhI0P87we+gyMiIiK9pi9T7pWVncbTTE3S1KmJOh+/eR8zqnzsyJta5uDggJUrV+LVq1fw8fEBANy9exeenp6wtLTEy5cv8fPPP6NRo0bw8vISvS4DGSIiItJr+jjlnnSfIZz6SczMYPbxx2pJF5NHVFQU/P39cf78eQBAYGAggoKC8PHHH8PV1RWVKlXCggULUL58eTx48ED0ugxkiIiIiEgvqbNgPm9zBVWfmqjrpERXFUwtq1SpEiQSCYKCglCzZk2YmJjg8uXLsLS0RM+ePUWvy0CGiIiIiPSOJgvmVX3qJzE3h3mTJki7fFlla+qyolLLoqOj4enpCVdXV+zfvx/ffvstxowZI3oYJsBAhoiIiIj0kD6nfkkkElQN2ZR7miQIgqgie10hb7F/wdSyzZs3Y8+ePbh06RK2bt0KMzMz/PHHH/jnn39w9+5d0esykCEiIiIivaaOgvm86V/qnOEjCAJivEcg/fp1td2Gqslb7F8wtSw0NBRSqRSJiYlYunQp6tSpAwsLC1haWsLISHx4wkCGiIiISM9wOGb++0DdDR9KQx2LusTExGDSpEmwt7dHtWrVcPfuXbx+/Rrt2rXD5MmTcfDgQbi7u+P333/HzZs3MXHiRNFrM5AhIiIi0jN8Y61+2qhjqRF+RC9aicuTWhYREQFPT08MHjwYzs7OWLt2Lfr37w8bGxtcvXoVTk5OWLt2LebOnYt27drB09MT3t7eotZmIENERESkB0pbgbi2FaxjUZe8KWwPOndR622pijypZS1btsSnn36KmTNnIjMzEwEBAWjevDnWrl2LpKQkLF++HNHR0fDz88PmzZvh4uIiem0GMkRERER6QFNvrPWFJgaESiQSSNQ8o8jQA9S1a9diwoQJOHLkCA4dOoTevXtj4sSJaNGiBd6/fw8AuH//PsqUKYOEhAT069dP9NoMZIiIiIj0hCbeWJNmqTJA1cXGAd27d4efnx/Kli0Lc3NzdOnSBWZmZqhTpw7atWuH0NBQDBkyBH379kVCQgKmT5+Orl27ilqbgQwRERER6TU2P/iPkJ6uU0EMALi6umLnzp0AAE9PT+zcuRP9+/fHypUrERERgX379uVed/78+fj6669Fr81AhoiIiIj0GpsfFKbOxgHyFPvfunULfn5+cHBwQGxsLDZv3oyLFy9i6NCh+PTTT+Hk5ARBEDBr1ix4eHigSZMmovfBQIaIiIiI9I6h15YoS52NA+Qp9j948CCGDh2Kffv2ISIiAgEBAZg/fz42bdqEJUuW4K+//sKyZcsQHh6OpKQk3L9/H2PHjhW1NgMZIiIiItI7bH5QmCAIiBkxEunXrml7K7mGDBmCadOm4erVqzA3N0e9evXw/PlzREdHo1u3brC0tETfvn1x7NgxZGRkIDExUfTaDGSIiIiISC+VluYHgiCICtgkEgmqrAmGkJ6u1v3Ik1rm6OiIjRs3IisrC87OzgCAqVOn4uLFiyhfvjyA/LNmBgwYIHofDGSIiIiIqNQRGxxomy52IpMntezhw4dYsGABUlNTUbt2bfTt2xfjxo3DrVu3MGXKFAD/zZrx9PTEmjVrMGTIENFrM5AhIiIiolJFEAQ88hqEtCtXtL0Vg+fi4oJZs2bB398f9+/fh5eXFwDgn3/+wbx587B161bMmTMH1tbWcHR0xPTp0+Ht7S1qbak6N05EREREpGuEtDQGMRpka2sLY2NjJCYmIiAgAL/++iukUikSExOxdOlSjB8/HiYmJnj8+DG7lhERERERiVHrzGm1tSlWBV0s4JeXg4MDVq5ciVevXsHHxwdXrlzB9u3bc+fI5Mya6d+/P4KDg0Wvy0CGiIiIiPSKsvUteQdoSs3NIdXxhgEuW7eotJ5HEASlGgLIU+wPAFFRUfD398f58+cBAHfv3kWTJk3w1VdfwcnJCbdu3cKMGTPw4MEDHDp0CJ6enqLWZSBDRERERHqjNNa3iO3OJibAU0XzAHmK/YHCqWUzZsxAYGAgDh06hM2bN2PlypUoV64cvvvuO/z0008MZIiIiIjI8LC+pWi6HOAlJSUhMzMTdnZ28PHxQWBgIJ48eQJfX19YWlqiVq1aWLNmDS5duoSHDx+KXpeBDBERERHpJUXrW7LT0nCvTdvc/zcE2QoEeDXCjyh0/8mbWla9enUEBwfDyckJwH9zZPbs2ZP7+T59+qBPnz7YtWsXfvzxR9HrMpAhIiIiIr2kivqWnIDGkHwowMsbxD3o3EWh9eVNLYuPj4evr29ualmlSpVw9+5dBAUFoWbNmrC2tsaCBQtw6NAhrF69WvS6bL9MRERERKWKxNwc5nK0+dU3OQFeUR8ye3uNf+8FU8uMjY1RoUIFGBsbo3r16nBxccHUqVPx9u1bZGRkiF6XJzJEREREVKpIJBJUDdmk0k5g2iZPupzz6lUa7VpWsNg/NDQ03xyZ7777DqNHj8YXX3wh1z4YyBARERFRqSO2E5g+Une6nLypZSXNkTly5AjGjx+PdDmDKwYyRERERER6LiddLu3yZW1vJZ9bt27Bz88PDg4OiI2NxenTp3H58mVMnjwZzZo1g5OTE06cOIHU1FTcunUL5ubm6NGjB6TSkitgGMgQEZHWGEq3INI8PnaI8lMkXU4VM2VKcvDgQQwdOhT79u1DREQExo4di3r16uHUqVN4/fo1Vq5cCUtLSwDAunXrULZsWVFBDMBAhoiItMgQuwUR6SMxgxR1BYPY4hWVLvehn62Qnq7WIAYAmjdvDm9vb1hZWcHZ2RkxMTFo1aoV0tLS0L59+9wgBgCGDx8u19oMZIiISKN0Nf2BqLTS5UGKpBx5frbyzJSRp9j/woULWLt2LVq3bo3KlSvD3d0dq1evRv/+/REbGytqjeIwkCEiIo0yxG5BpHl5OzSRcgQFBimS7vjQiYs8QzLlmSkjT7F/+/btMWzYMAiCgHLlymHw4MEYOHAgrl69iuXLl4tepygMZIiISOMMuVsQkT770CBFXcEg9v/Ic+JS1M9WEATEjBiJ9GvX1LVFNGvWDHv37sW8efNw6NAh1K9fHytWrEDbtm0xbdo0rFixAnFxcfD390dSUhJ27twpem0GMkREREQE4P8GKZJuKnj6Is+JS1EkEgmqrAmWe6aMPKllDx8+xE8//YSMjAzUqlULXbp0gZmZGerUqYN27dohNDQUQ4YMQXBwMDw9PeXaBwMZIiIiIiIdV9LpS1EnLnlPr1R5iiVPapmLiwtWrVoFAPD09MTJkyfRv39/rFy5MneOjKIYyBARERERaYAy3eEUOX2RmJnB7OOP1Zo6VpKYmBhMmjQJ9vb2uHPnDmbPno2LFy9i3Lhx6NKlC5ycnBRem4EMEREREZGaqbI7XM7pi7pOXFQpIiIC3bp1w7Vr1xAdHY3Q0FDMnz8f69atw40bN/DXX38hPj4evr6+uHLlCgICAuDj4yNqbQYyRERERERqpsrucDm1TPrQzr5ly5ZYvHgxJBIJli1bBplMhnPnzqFOnTqoXLkyLC0tYWlpiZUrV8q9NgMZIiIiIiINUqQ7XN7Tl7xDQZ1Xr5K7WF9Z8hT7//TTTwCAOnXqICgoCL169UJqaiquXLmCr7/+Wql9MJAhIiIiItIgZbvDaTuNTJ5if0EQcj9evnyJ0NBQtGzZEo6OjrC3t1dqHwxkiIiIiEgvZevRYF1l96oPaWRF8fLywq5duxAfH4+srCxkZGQgLCwMUqkUXbqIH8JZFAYyRERERKSXtH0yoUkSiQRVQzYp1PVMEASVpp/Jk1r27NkzzJs3D19++SWqVKmC+Ph49O7dG8bGxggODsaUKVMU3gcDGSIiIiLSG/p6MqEKEokEkiJS0j7U1lkQBMR4j0D69esq24c8qWWVKlXCqFGjMH36dHh7eyM7OxtJSUk4fPgw3N3dldoHAxkiIiIi0hvKnExoU95ifVVSZVtnddi5cyeqV6+O2rVrY9++fRgyZAji4+NhbGwMa2trpdZmIENERESkx5QZsgjoV51JjuJOJvSFKu9zRQZlalKlSpWQkZEBExMTZGRk4ObNm7CwsICNjQ3s7OyUWpuBDBEREZGe0vW/xlPR1FXbU1RbZ0EQEDNiJNKvXVPLbZZk4MCB2LlzJ+7evYtZs2Zh0KBB+Oabb1C2bFnUrVtXqbUZyBARERHpKVUOWST10lZtj0QiQZU1wVor9p8zZw6sra3h6OiI6dOn48GDB8jMzMS7d++wfft2jB49WuF9MJAhIiIiMgCKDFkE1Fe7Qfmpq7Yn789PUz9HeYr9x48fDz8/Pzx+/BhNmjTBtm3b0L17d6SnpyMzM1OpfTCQISIiIjIAyg5ZJPVTR22Prndxe/XqFeLi4vD06VMsW7YMhw8fxrFjxyCVSvH69Wul1mYgQ0RERESkp/Ke9Kh6Xkxx5Ektk0gkkMlkkMlkqFGjBiIjI9G9e3fcu3cPDRs2VGofDGSIiIiIiPRQ3o516pgXUxx5Usvc3NzQvn17fPLJJxg/fjyioqJQo0YNvH79GlZWVkrtg4EMEREREZGe0ZeOdX///TeOHTuGp0+fIjMzE02bNkWVKlVw8eJFpKamKrU2AxkiIiIiIhVQdqaPPHR9fkyOU6dO4eHDh4iNjcX8+fNx8eJFHDt2DC9evED37t2VWpuBDBERERGRkrR5QlLrzGlIzMy0Oi+mOL6+vpgzZw7i4uIwfvx4VK5cGYcPH4anpydMTU2VWpuBDBERERGRkrQ900cd82KKI0+x/99//405c+agbt26iI6ORkREBBo2bIjY2FicO3cOrVq1UngfDGSIiIiIqFRRRwpYdp71FJ3pI+/taXp+TA55iv3zppZ9++23uHTpEoKDg9GwYUNkybFOURjIEBEREVGpoYkUME3M9NH1+TE52rRpg8OHD6NatWoIDQ2FhYUFPv74Y7x79w6PHz9Wam0GMkRERERUamg7BUxV8s6PEUOVM2bkSS2TyWSwsrKCIAgwMjKCtbU1rl27hu7duyNNyVMxBjJEREREVCqpMgUsb6pXtoY6l4ml6hkz8qSWvXz5ElWqVMHz58/x4sULJCQkYNy4cShTpgzMlbzvGcgQEREREQDdewOuDnm/R3WlgGm6ZkWXeXp6wsPDA25ubjAzM4OJiQlkMhni4uJQrlw5pdZmIENERER6rTS8+S6Oqr93vgFXnL7UrNQIP6L0KZQ8qWWhoaHw9/dHVlYWevTogY0bNyIlJQUymQydOnVSah8MZIiIiEiv8c23cvTlDbiuk7dmRZPypr096NxF6fXkSS2ztrbGt99+i/T0dBgZGcHBwQFnz57F8uXLcfz4cXTs2FHhfTCQISIiIr3DN9+qo8tvwNUh75t6VZNIJJCouVtZQWJaSUvMzGDe6GOkXdX8sMyNGzfiypUreP36Nd6+fQtnZ2c0adIEAwYMQEpKilJrM5AhIiIivVPa3nwXR1VvyrXxBpyUp4lW0srasGEDAGDSpEnIyspCnTp1EBISgvPnz2PKlClKrc1AhoiIiPQS33yTsvS9vipbD1pJnzhxAnPmzMHTp0+xbNky+Pn5ITs7G1WqVIGpqalSazOQISIiIqJSyZDqq1RRxC+WPMX+EokEMpkMMpkMtWvXhqWlJRITE+Ho6Kj0PhjIEBEREVGpYaj1Vaoo4hdLnmJ/Nzc3tG/fHp988gl++OEHDB06FMuXL0dQUBDat2+Ptm0VDyYZyBARERFRqWFI9VWCICBmxEikX9N8Eb9YsbGxuHbtGh4+fIjr169j3LhxaNmyJU6ePAlLS0ul1mYgQ0RERESlirbrq8R0GhNDIpGgyppgCOnpKtiVePKklgUFBSE5ORmmpqawsbHBoEGDYGdnBysrK9SvX1+pfTCQISIiIqJST1XBhZjbifEegfTr19V+W+oiT2rZ1KlT4enpiZcvX+Krr77CggULcOnSJRw6dAghISEYMWKEwvtgIENEREREpZo+tDHWV2vWrIGTkxPs7Ozwv//9D8bGxhg7dizu3LmDHj16KLU2AxkiIiIiMhiKnKzoQxtjfWVsbIy///4btra2qFu3Ljw9PXH8+HGkp6ejSZMmSq3NQIaIiIiIDIIqTlZqnTmt1jbG+lCgr0rp6en47rvvMGbMGHh6eqJv3744efIkypcvjxYtWii1NgMZIiIiIjIIgh6crGirQF+V5Cn2T0lJwbp163DlyhXEx8djzpw5ePv2LV6/fo1du3Zh5MiRCu+DgQwRERERGRx5Tlay09Jyh2Ma0pBMdZGn2L9du3ZYv349Tp48iQEDBiAtLQ1WVlZISEjAJ598otQ+pEp9NRERERGRDpKam0NqYSHqQ2ZvD3Ml6zWoaGZmZujVqxeaNGmC5cuXw8TEBEeOHEFycjIcHR2VWpsnMkREREQGINsABjwqS9H7QJeHZAqCoHNpaPKklrVv3x7t27fHjh07EBkZiXnz5mHevHno3bs3du/ejbFjxyq8DwYyRERERAaAKVHKUWZIprpm0OjqzBl5UstCQ0Nx6NAhHDhwAL/99hvGjRuHM2fOICEhAUFBQUrtg4EMERERkZ6SmJvDvEkTpF2+rO2tlFqcQfNhffv2xbFjx2BjY4Pk5GSsWLECa9asweLFi3HkyBEMGzZM4bUZyBARERHpKV1OidKGvEX7mqIPndK0KSwsDKdOnULDhg3h5OSEMWPG4MaNG3jz5g26d++u1NoMZIiIiIj0mDIpUaRaqp5BYwgzZ+7cuYPx48djzJgx6NevHypWrIjjx4/Dy8sL5cuXV2ptBjJEREREZHA01fwg7+3kdEpTJZetWxQ6cVNnkwB5iv1TU1Nx9OhR/Pvvvzh58iQ6d+6Mnj174vTp0zhw4AB69Oih8D4YyBARERGRwTGU5gd5T9zENhVQd5MAeYr9fXx8MHHiRDx79gxdu3aFv78/du7cicjISKWCGICBDBEREREZCENufqCvTQUuXboEY2NjREZGomzZsujZsyfatGkDIyMjZGVlQSaTKbw2AxkiIiIiMgjaaH6Qt8GAOtPZshVsKlAj/IhK63YA+VLL3NzcULVqVTx48ADt2rXDjRs3YGVlhcTERGzatIldy4iIiIiIAO02P9BUOltJTQXyBlcPOndR+e3Lk1oGAMHBwcjKysKUKVNgbW2N27dvY8+ePUoFMQADGSIiIiIihWkjna2kpgK6lGIXExODc+fO4cmTJ1i+fDkkEgnWrFmDVq1aITU1FZaWlgqvzUCGiIiIiEhBmkpnkzeFzXn1Kp3oWhYREQFLS0usXbsWS5Yswd69e/Hll1/i2bNn+OOPPzB58mSF98FAhoiIiIhICZpOZ9N2RzZ5UstatmyJxYsXY/bs2RgyZAgAYM2aNYiIiMC+ffuU2gcDGSIiIiIiNRIEAWnvlTuxEYwEmDb+GO+u6NdwzLVr12LMmDE4cuQI5syZg9atW+P06dP46aefkJGRgeHDhyMzMxOTJk2Cvb09ateujVmzZolam4EMEREREZGaCIKAoQeH4urLq8ov1k2AaUfF2xWrSlYaAJEZYd27d4efnx/Kli0LCwsL9O3bF0OHDoWlpSW6desGe3t7hIeHw9PTE4MHD8aAAQNE70Oq2PaJiIiIiPSPIAh4m/lWYx+v01+rJogBAIkE70x040MsV1dXLFq0CJmZmXj79i28vLwgk8kwadIk/PXXXwgJCYGZmRm++eYbVKhQAdbW1qLX5okMEREREZUKKj0dUcDBfgdhbqTamS7akJKcgmrjxBX7A0D16tURHBwMJycnAEDVqlWRkJCAcuXKISUlBb///jt++OEHjB07FtWrVxe9LgMZIiIiIiqWKuo7dEXa+zStBTEA4BHqobXbVqWsNPnmyMTHx8PX1xeJiYlYsGABZDIZ7t+/j6ioKHh5eaFOnToYNGgQ5s+fDzc3N9HrMpAhIiIioiJp+wRDnU70P6GR0xFBEDA6fDSuv7yu9tvSVUlJScjMzISdnR369++PDRs24OnTp3j+/DlWrlyJ2NhYuLu7w97eHtevi7+fGMgQERERUZG0fYKhTuZG5rAw1kzL5E0emxQ61RIEAelZ6pkFowx5U8tsbW1hbGyMxMRE7NixA7dv3wYAVKxYEd999x1mz56NyMhI2NvbI12O2TcMZIiIiIioRJo6wVCntPdpcN/unvv/ukwQBIw6MgoRryK0vZVC5E0tc3BwwMqVK/Hq1Sv4+PgAAC5cuIBRo0ZBJpPBxsYGv/32Gxo1agQvLy/R6zKQISIiIqISafIEQxNyAhpSv6ioKPj7++P8+fMAgNOnT2PMmDFo3rw5nj17hkqVKmHBggUoX748Hjx4IHpdBjJERERkUAypOF3bDO1+NDcyR2PHxrjy4oq2t1Kq5E0tCwgIwPnz55GZmYmKFSvC3t4eJiYmuHz5MiwtLdGzZ0/R6zKQISIiIoNhyMXppDyJRIL13dfrRYBmSE0CCqaW1apVC5GRkTh06BBCQkLw4sULfPvttxgzZgw8PT1Fr8tAhoiIiAyGIRenk2pIJBK9SZGTp0mAJhsDyFvsXzC1LCkpCRMmTEBSUhJatmyJzMxMBAcH4/Tp03j8+LHodRnIEBERkUEyhOJ0bctbHE/56VIKo6YbA8hb7F8wtaxatWr4+++/YW5ujt9++w2bNm2Co6MjbGxskJ2dLXpdBjJERERkkAytOJ10B1MY5ZN3joyPjw9OnjyJxo0b4+LFi3j27Bm6dOmCn3/+GZcvX8bYsWNFr8tAhoiIiIhKpCunD7pAl1MYD/Y7qPaTSHlTy6pXr47g4GA4OTkBACwtLZGdnQ1HR0e8efMGM2bMQPfu3WFtbY1Tp06JXpeBDBERERGViClmRdOFFMa8KYAeoR5qvz15U8vi4+Ph6+ubm1pmaWkJmUyGx48fw8LCAmXKlEFUVBRq1KiBFy9eiF5XKu/GiYiIiKh0yGlXTLrNTGaGj8p9pO1tFKtgalnFihUhCALi4+Nx/Phx2NjYwNbWFm/evIGdnZ3odXkiQ0REekuXim1JN/DxoFr61K5Yk/KegPCkqmQFi/2fP38OCwsLGBkZYcKECYiPj8fMmTMhkUhgZCQ+PGEgQ0REeonFtkSaoU/tijWFgzXlU3CODABcuHAB5cqVg7GxMSpUqID169dj2rRpmDRpkuh1GcgQEZFe0uViWyIybLp6UqWpWTLKzpFZtGgR1qxZA2dnZ7x+/RopKSno3LkzatSogcOHD6NJkyai1mUgQ0REek8Xim1JN3DuCWmKuk6qFE2Z1eQsGWXnyEyYMAHnz5+Hm5sb7t+/j+DgYLx//x4uLi7YsWMHZs2aJWpdBjJERKT3OC+EiAyBoabMFiz2X7BgAW7fvo2zZ8/i3LlzWLBgASIjI7F582aMGDFC9LoMZIiIiIiIRFJnkxFVpcyqe5aMsnNkvvvuO3z33XcYNmwY1q1bB2tra1SrVg29evVCWFiY6HUZyBARERERiaDJExN5U2Y1OUtG2Tky8fHxOHz4cG775RMnTuD27duIiYmBo6Oj6HUZyBARERERiaDLTUZyZslcf3ld21sppGBq2enTp5Geno4HDx7g4MGDOHXqFFJTU/Ho0SNUq1YN2dnZkEpLHnfJQIaIiIgMkq51lCL9l/cxpY4mI4Y6n6Zgsf+6detgZWWFd+/e4bPPPsPkyZMBAMuXL8fu3btFBTEAAxkiIiIyUIb0RpB0jzqajBjqfJqCc2TWrFmDf/75B4cOHUJ4eHhugX9UVBQWLlwoel0GMkREVIg6i1lVRdf3R9phqG8ESfeo6zVoRacVGpkFowxl58jUq1cPGzZswG+//QZvb28IgoAJEyYgNDQU3t7eotdlIENERPkYavtPKh10dVAhGQZDTf2Sl7JzZLp06YLVq1cjISEBXl5eWLZsGXbu3ImKFSti+/btcHV1FbUuAxkiIspHl4tZicRQ16BCIp74KSZvsf/MmTPRvHlzfPzxx3jx4gUsLS1haWmJbt264fbt2zA1NRW9LgMZIiIqljqKWVWFE9yJSBv0IfVL3ZSZIxMVFYVnz56hbdu2ePv2Lb7//nvcuHEDMTExSEhIwL///it6XQYyRERULHUUsxKReuhDbZs+EwQBo46MQsSrCG1vReuUmSOzY8cOBAQE4N9//4WlpSXKli0Ld3d3lClTBs+ePcOZM2dEr8tAhoiIiEjPsbaNdFnBOTKzZ8/Gu3fv4OTkhGrVqiEzMxPZ2dnYt28fzM3FZwEwkCEiIiLSAx86cWFtm2Yd7HdQZ9NuNUHe1LKCxf5hYWFwc3ODmZkZ3r17B09PT7Rp0wYWFhaiC/0BBjJEREREOk+eExddrm3TZ3nr8jxCPbS7GS2TN7Ws4ByZypUr499//0WtWrXw9OlTnDlzBjNnzkR6ejrKli0rel0GMkREREQ6jicu2mcmM8NH5T7C9ZfXtb0VvVNwjszdu3fx7t073L17F23btkVYWBgOHjyI9PR02NraokePHpBKpSWuy0CGiIj0HgucydDlfYwXdeLC+Saky0pKLfPw8EDLli3x/v17BAcHiwpiAAYyRERkAPjGjUqToroJcr4J6bKCxf7nzp3DyZMnIZVK8fDhQwDArFmzkJWVhcGDB4tel4EMERHpJb5xI/o/EokE67uv5+lkAYIglPqZL+qgzBwZAJBKpbh58ybGjh2L169fw8LCAlu3bkXDhg3RpEkT/PLLL6LWZSBDRER6iW/cqDTJmzrGx7w4nPuiPsrMkQkICIClpSUaNmwIiUSCgQMHonv37vDz88O9e/fQtm1b0esykCEiIr0lkUg4sJNKHaZSkr4pao6MpaUl7t69ixo1asDFxQW3b99GlSpV0L17d9HrMpAhIiIi0nFMpVROaZ/7omqqniMzbtw4pKeno0qVKggMDMSgQYNErctAhoiIiEjHMZVSfpz7oj6qniNTv359TJs2DY0aNYKXl5fodRnIEBEREekBplLKh6dYuqPgHJmYmBikpqbiypUr+Prrr/HmzRuMHTsW9evXx82bN0Wvy0CGiIiIiAyOLp1ilfbGAwVTy0JDQ9GyZUs4OjrC3t4eZcqUgYmJCUxNTWFuLj4FkIEMERERESlNEASdCBp0UXpWeqkNYoDCqWUODg7YunUrsrOz0aVLF2RkZOD333/HnTt3MGbMGNHrMpAhKoUEQUBapnz5rVR6vM3MgpBtDIk0U9tbISI9IQgChh4ciqsvr2p7KzrPEBoPyFvsXzC1LDAwEK1bt8b9+/fxyy+/4OHDh7C0tISlpSXWrFkDb29vUesykCEqZQRBgOfKc7j0KEHbWyGdNg/W9WZpexNEOoenDkVLe5/GIEYkQ2g8IG+xf8HUMqlUChMTE6Snp8PW1hbTp0+Hn58fLCwskJiYKHpdBjJEpUxaZhaDGCIiBfDUQZwT/U/o/YmDqgmCgNHho3H95XVtb0UrCs6ROX/+PMLCwpCZmQkPDw/UrVsX69evR/Xq1eHj4yN6XQYyRKXYxdmdYWEi0/Y2SMe8zUyD+/b22t4Gkc7hqYM45kbm7K5WhE0em/A28y3Ss9K1vRWlyZtaVr16dQQHB8PJyQkAcPHiRTx79gx+fn74+eef8ccff6BLly7o06cPatasKXpdBjJEpZiFiQwWJnwZoAIkMtbHEJWApw755Z3ZwtS7ohlS5zJ5U8vi4+Ph6+ubm1rWsGFDVK5cGRkZGQgLC8PXX3+N58+fw9TUFAcPHkSvXr1Erct3MERERERy4qlD8XICGqIcBVPL1q9fj27duuH+/fs4fPgwLC0tYWZmhnv37iE5ORnZ2dmQSqUlrstAhoiIiFTC0AvhDfl7UxaHT9KHlFTsX6NGDbRr1w7x8fEIDg4WFcQADGSIiIhIBVgIX7rp0vBJXVWaC/6LmyNjYWGBr776Cr///jsaNmyI3r174/Dhw6LXZSBDRERESmMhPEkkEqbbFSHnpFIikWBV51Wlsti/4ByZvXv3YvDgwfjtt98QHByMsmXLYu7cuXByckJsbKzodRnIEBERkUoZaiF83oJ2Kp6hpxjKw5AK/PNSdo5M5cqVsWrVKrx8+RIdOnTAwYMHcefOHTx48AADBw4UvS4DGSIiIlIpFsKXXkwxpKLkLfafOXMmGjRogDZt2uD58+dwdXXFb7/9hnr16qF8+fL466+/8N1334lal4EMERERkZx44lA0phgW72C/gwZzUqnMHJmoqCh89NFHsLKyQmJiIjZt2oRnz57B2toaxsbGMDcXfx8xkCEiIiKSE1PMSmaoKYbyyJuO6BHqod3NqJAyc2R27NiBbdu24fbt29izZw+GDRuGo0eP4tmzZ7CxscGbN29EryuutxkRERFRKZfTYphILDOZGT4q95G2t6F1BefIbNiwAf369UOPHj3w7NkzuLi4wMTEBACQlSU+SOKJDBEREZEIbDFcsrwnEDy1ohx5i/39/f0xf/581KpVC2fPnsXbt29Rrlw5pKWlQSqVQiaTiV6XgQwRERGRSGwx/GEcjElFyTtHxtfXF+vWrcPly5dx6NAhnDx5Em/evEFgYCAaNWoELy8v0esykCEiIiIileCple4TBEHpWTbKzpFJSkrChAkTkJSUhJYtW+L+/fuYOXMmrK2tkZKSInpdBjJEREREpDI8tdJdqmqPrewcmWrVquHvv/+Gubk5fvvtN3z11VeYMGEC3rx5A2tra9HrMpAhIiIiItJhqhoyqq322AWL/U+ePInGjRvj4sWLePbsGcqWLYv09HRcuXIF/v7+otdlIENEREREpKPUNWRUmbk2ysyRAQBLS0tkZ2fD0dERb968QWhoKDIzM7Fjxw78/PPPGDBggKh1GcgQEVGxmOdOYvGxQoZOVaci8lLXKYoyc22UmSMTEBAAS0tLyGQyPH78GBYWFggNDYWNjQ2CgoIQExMjel0GMkREVCy2TyUiUt+piLyUHTIqCAJGh4/G9ZfXVbirkhVMLfPw8ICFhQXs7Ozw9u1bfPHFF2jbti1GjBiBevXqiV6XgQwREeXD9qlERPlpq7ZE1SQSCVZ1XqXxrmUFi/1v3bqFR48ewdraGi1btoSLiws6d+4Ma2trfP7556LXZSBDRET5sH0qKSLvIETSPdpKizIUee87ZU9FFLltXRsyKm9qWd45Mj4+Ppg5cyakUimmTp2KGzdu4NKlS1i9ejWOHDmCFi1aiF6XgQwRERXC9qlEhkNX0qIMhbmRuUZfHw3hlLzgHJnFixfj7t27OHv2LLp27Yp//vkHK1asQExMDE6fPo3w8HBR6zKQISIiIjJghpIWVVoVd0ouCAJGHRmFiFcRWtqZeAVTy27fvg0LCwvY2NjAzs4O3bp1Q1paGmQyGQYOHCh6XQYyRERERKWEptOiDEXe9C5dSdFLz0rXiyAGKJxalpGRgW+++QZly5ZF3bp1ceLECbRu3Rrr16+Hp6cnvvrqK1HrMpAhIiIi0hBt1KrkvT1Np0UZIl2pU8lLmZkwipC32L9gatmSJUvw7t07PH/+HCEhIShXrhw2bNiAixcvIjY2VvS6DGSIiIiINIC1KvpL1+tUlJkJowh5i/0LppYlJibC3NwcgiCgevXqePLkCUxMTGBmZoaMjAzR6zKQISIiItIA1qroL13s5qitmTCKKDhHJiMjA927d8fDhw/RqlUrLFy4EMbGxkhKSkKZMmVEr8tAhoiIiEjDNFmrwtbYqqHpbo4lpSGqaiaMIuRNLatevTqCg4Ph5OQEAIiNjUX37t3x9u1bhIWF4e7du+jRowfGjx+PatXEr8tAhoiIiEjDtFWroksnClQ8Xe9IJm9qWXx8PHx9fZGYmIgFCxZg586daNmyJZ4+fYru3bvj5MmTOHfuHG7evInKlSuLXpeBDBEREVEpwZMZ0oa8qWX9+/fH6tWrcerUKSQmJuLChQswNTXFo0ePIJFIIAiC6HWlatwzEREREWlZTqE6kbbkLfbfsWMHoqOjERERAUdHRzRq1AhOTk4wNjaGkZERLCzEn1TyRIaIiIhUiulLRdPW/aKLher0YfpUyC9GwTkyAHD79m107twZzs7O8PLywr59+/DmzRv88MMPotdlIENEREQqxfQl3aPpQnVS3iaPTXIHn4IgaKT4X9k5MiNGjMC5c+dgYmKCQ4cOYfPmzbC2toaTkxOmTp2KiAhxtUEMZIiIiEhpuj5ng0gbw0g1SZMNApSZI+Pv74+DBw/Cw8MDR48eRd26dVGnTh3cuXMHMpkMUqn4yhcGMkRERKQ0pi+VjG2QtYfDSLUrb7G/r68vkpOTce/ePbx79w63b9/GqFGjEBQUlNuWWSwGMkRERKQSTF8iVVDHyUlpG0Z6sN9Btc4pUnaOzMKFCzF69GjUr18fnTt3xogRI1CvXj3Ur18fFy9eFL0uAxkiIiIi0gmaODnR5DBSTcp74ucR6qHW21JmjkxAQAAmTJiAe/fuwc3NDTKZDGXKlEFERARcXFzw/Plz0euy/TIRERER6YTSdnKiSmYyM3xU7iNtb6NIeVPLfHx8sHTpUjx9+hTnz59HbGwsmjRpgqpVq8LY2BhlypQRvS5PZIiIiIg0jLVERct7v6jy5CTvaQXrlDQvb7F/QEAArl69igYNGuCLL77AxYsXYWJighs3buDhw4dwc3MTvS4DGSIiIiIN45vpkpkbmaus5opd9bSr4ByZVatWYevWrQgKCsLevXuxfPlyTJ8+HWPGjIGnp6fodRnIEBEREWkA30xrj6q66mlqTouuU3aOzKJFi9C5c2ccPXoU3377Ldq2bYvs7Gw0a9YM5ubiT+EYyBARERFpAFtUlyxvCpiu3U+anNOi65SZIxMQEABPT08kJibizZs3GD16NOrWrYs2bdrAwsICrq6uotdlIENERESkIWxRLR7T7wxHwWL/06dPY+nSpahSpQoqVqyIM2fOYPLkyfDz80OnTp1Er8tAhoiIiIh0gr6k36l7TouuU3aOzJIlS3Dv3j20adMG9vb2OHHiBMLDw2FiYoLDhw/Dz88PUmnJzZUZyBARERGRTtDl9DtNzmnRdcrOkbly5Qo++ugj9OzZEyEhIXB3d0fDhg0RHh6OSpUqiQpiAAYyRERERKRDdDX9Tl9Oi3RRwdSy9evXo0yZMli4cCE6deqEf/75B2fPnsWLFy9QoUIFnsgQEREREamKzp4WCQKg4T0lJ6eg4rjqoq9fsNjfwcEBpqamEAQBLi4u+OGHHwAAa9aswYYNG3giQ0RERESkSio5LRIEIPOtajYkCMCGT4EnF1Wznkjv3wlyXb/gHBkHBwds3boVMpkM/fr1AwDs2bMHO3bswOjRo0Wvy0CGiIiIiEgTBAFY0w14/I+2d6JRBefIBAYGonXr1rh//z5++eUXDB06FGFhYbC0tERISAi8vLxErctAhoiIiIgIUO1pSVEy3pa6IAYonFomlUphYmKC9PR02NraIiYmBpaWljh+/DimTZsmel0GMkREREREmj4tmX4fMFFBmtqGvsCTf1WyJXUpWOx/4sQJ7N27F0lJSXBxcQEAXLnyXxOFBg0aiF6XgQwRERERGb6STlv08bREIgGG7gYyNdyAIDkF+KmG6KsXnCNjYmKCJ0+eoHXr1oiOjoa9vT2srKyQnZ2dex0xGMgQERERkWGT97RFFaclRcl4Cyyu+d//5/xXH8lZ7F9wjkyDBg3Qr18/JCQkoFOnTqhUqRKqVq2KyMhIeHp64urVq6LWFdfbjIiIiIhIX2XKedpiYgGYWKr+w7Is4NxSfd+njiqYWmZlZYWbN2/C2dkZDg4OuHnzJm7evIkuXbrA1tZW9Lo8kSEiIiKi0qO405a8pyUZaiz4H7xL86lgqiZnalnBYv+rV6+iQYMG+OKLL3Dx4kVERETg4cOHiI2Nxfz580Wvy0CGiIiIiNTfsUub8gYmOactH6LPaV+aoOQcmVWrVmHr1q0ICgrC3r170adPH8yZMwdxcXGYOnUqBg0aJGpdBjJEREREpV0pnW+Sj7HFf2lfj89reycGp+AcmUWLFqFz5844evQovv32W0yePBlLly7Fixcv8PDhQ9HrMpAhIvp/7d17cNT19f/x1yYhIYkECBAEglwKNqAQDYlcBLUql4gWikA1IBh1FAbxQquQiRa0hDipFrAqCAUhVaEEuVgHYdQCisiXgGCBYCPNBQjhFi4SoCTs7u8Pf6FrBPxs9vLZT/b5mNkZDXr2DJ9h2LPvc94HAIKduzMk9ZHNJj2y1hqnUk6nlDtEKttmdiaG1G4tGz58uE6dOqXKyko9/vjj2rt3r5xOp8LCwtSsWTPDcS1fyJyrspudAmAp56oump0CACCQ+erGLjO5zr9cjc32821nvuBuW1/1ecsUMdJPW8skaevWrYqLi1NSUpKSkpIUFhamOXPmKD093XBcyxcyydM/NTsFAACA+sPIDAm8x9O2vqe/kRr4+Xm5Oexfu7Vs06ZNeuKJJ3TLLbeovLxc+/bt05o1a9SmTRtNmDBBaWlphuJaspCJbBCq5HZNta30pNmpAAAAwEp8eSNZXXi6iHN2ovdyMcrNYf/arWVbtmxRdXW1WrVqpdjYWNntdoWHh6u4uFiDBg0yHNeShYzNZlPeuN46X01bGeCuc1UXlTz9M7PTAID6oz7c9hVoH+59KZBvJDPa1ud0SrlDpbJ8n6fkDbX3yDRt2lQjRozQ0qVL9d///lePPfaYDh8+rH379mnatGmG41qykJF+KGaiwi2bPgAAqA+47csa6tuNZDabNGaVefto3Gwt69ixoxYsWKD4+HhJUr9+/TRv3jz16dNHbdu21ccff6yRI0eqcePGysvL00MPPWQoLpUAAABAXXHblzUE8o1krhcRBPJpkSs3W8sqKiqUmZl5qbVs9OjRuv/++1VZWanRo0fL6XRq8ODB6ty5syoqKgzHpZABAADwBivf9mX0Vi8r88eNZHVpM2wQKcWnSAet0SZWF7Vby1544QUVFBQoKSlJ0dHRKi0tVY8ePXTs2DFVVlYajkshAwAA4A3c9hXcaDO8otrD/jt27FCXLl00ZMgQrVixQn379pXD4VBERIRatmxpOC6FDAAAAP4nmAb/vcnT28fqsdp7ZHJzc5Wfn68vvvhCbdq0Ufv27XXvvfdqzpw5evjhhw3HpZABAADA/9T3FjN/MGO3iz95uEdm//79Onv2rHbs2KEJEyaooKBA7777rtq3b69XX321fu+RAQAAQS5QrjyuL6cX9e1WL7OZsdvFnzzcI7NixQr16tVLcXFxio2N1V/+8hc5HA45HA5VV1cbjkshAwAArIVZBO8L5Fu9rMJiu138qfaw/4YNG7R69WqdPn1a48aNU2pqqjIyMhQaGqrS0lLDcSlkAACAtXDlsW/441av+u6xT/xbDDqd5uyS8XCPTHh4uMrKytSnTx8VFxdr0qRJ+vWvf60PPvhAL7/8suG4FDIAAMC6zL7yOBiuLcYPAqWdsYbTKeUOkcq2+f+9Pdwjc8MNN2jYsGE6efKk7rrrLpWUlGjGjBlat26d5s+fbzguhQwAALAurjyGP9DO6JHarWWTJ0/W3r17ZbPZdPjwYTVq1EinT59WSEiIBgwYYDguhQwAAADqF2+fnnC1skdqD/u/8MIL+u6771RVVaW7775boaGhSkhI0NGjR92KSyEDAACA+sPXpydmtzPWsNDlArX3yEjSlClT9NFHHyk0NFSS9NJLL2n48OFuxaWQAQAAQP0RLJdB2GzSmFWWGPavvUdm06ZNeuKJJ3TLLbeovLxcJ0+e1LRp07Rjxw4tX77ccEFDIQMAAOAN9WWnjNW5PgdvnZ64XurA5Q4e75HZsmWLqqur1apVK8XGxmr+/Pm6cOGCJOnFF1+kkAEAAPArPuAGHm9dBsHCUI/UHvaPjY3VsGHDtHPnTi1cuFCDBg3SkiVLFBoaqtjYWMNxKWQAAADqig+4wcHMhaFm7Yq5Gg/3yEREROivf/2r/vjHP2rz5s0aP3689u7dq/vvv18XL140HJdCBgAAoK7M/ICLy3NtA7N6u5+Zu2KuxsM9MsOGDVO3bt1UXV2t4cOHX9ojk5+fr/fff99wXAoZAAAAT9hs7LIJVLT7BYTarWXTp09XSkqKduzYofbt2+vAgQMKCQlRaGionn/+eW3evNlQXAoZAAAA1B/1td3v6W+kBgFSMLvZWlZ72H/FihXq1auX4uLiFBsbq65du0qSzp8/r5YtWxqOSyEDAACA+qM+tfu5tsnNTjQ3F1dutpbV3iPTrFkzLV26VA6HQ/3791ejRo00d+5cnThxQgsWLDAcl0IGAAAA9Ut9afcLi5TiU6SDgb/08mpq75FZvHixbr75Zh05ckQzZ87UiBEjNH36dOXn5+vdd9/V+PHjDcUN8WXSAAAAAOrA6ZTeGWT5Ikb6aWvZ8ePHtWnTJu3atUupqanq0qWLOnXqpL/97W/68ssvDcelkAEAAAD8yemUqs5e/XX2uHTg/8zO1CtqWssGDRqkjIwMORwObdu2Ta+99pp27dolSerRo4eysrJ0zz33GI5LaxkAAADgL06ntHCge0VKIA36S24P+9duLevSpYtyc3P1+uuvKz09XYsWLdKyZcvUrl07zZ07V2lpaYbiUsgAAAAA7nI663ahQNU5909aAmnQX3J72L92a1n//v01f/58nTx5Umlpafrzn/+swsJCFRcXa+zYsYbjUsgAAAAA7qjLqcrl/H6fFB515ffIHSqVWX9GxnWPzOTJk9WhQwcNGDBAFRUVatGihTp37qzrr79e0dHRKigoMByXQgYAAABwR3UdTlUuJzzq6rerPfbJ1U99nE6p+rznebjLzdayjh07asGCBYqPj1dRUZHsdru2bNmiEydOaM2aNQoLC1NUVJTCw8PldBo/7aGQAQAAAOrqaqcql+O6G6bKg103TqeUO0Qq21b3GHXlZmtZRUWFMjMzderUKeXl5Wn//v0KCQlR586dFR8fr/3796t58+YqKirStddeazguhQwAAFLd+93hf558+AO87edOVa6mpqCp51xbyzIyMpSTk6PCwkI1aNBAhw4dUkJCgt577z116NBBJ06cMByXQgYAAG/1uwPBLJi+DPCkmG4QJbXtJR3Y4r18AlztYf9vv/1WUVFRiomJUZMmTbR8+XKVlpbqwIEDeuuttwzHpZABAMBb/e5AsOLLAONsNumRtZ4XfRa6DKBmj8zx48eVkZGhqqoqPfvss2revLkSEhKUk5OjV199VUeOHNGkSZMM75KhkAEAwJW7/e7wP9cZAwQGvgxwj81W93Y0V5e7DMAfFwB4uEcmPDxcPXr00Pr167V69Wq1b99ec+fOVVRUlMrKygzHpZABAMCVJ/3uAILjy4BAKaZtth9a1WqKGX9dAODhHpmSkhLt3r1blZWVGjRokNLT09W0aVOdOXNGdrvdcFwKGQAAAHgPXwb4j0Va+mrvkZk4caLGjh2rzMxMtWzZUqNHj1ZWVpaqqqrUqFEjw3EpZAAAAIC6MvMWvaqrtPQ9/Y3UwEcFpYd7ZEpKSvTNN9/o/PnzWrNmjdLS0pSWlqbS0lL179/fcFwKGQAAAKCuAqHFTPqhpU/6Xz6zE333Xh7skVm2bJnat2+vmTNnqmPHjrrnnnuUn5+v559/XgUFBcrJyTEcN8TdvAEAAGBRTqdUddYHryC5drlGzRXKgaZBpNQmxewsfsK1tWzkyJE6fvy4Bg8erIqKCq1Zs0ZOp1NxcXFKS0vTypUrDcflRAYAACAYWGSewhK8dYWyp1wvHQiUk6HLcB32z8vLU3Z2tpYvX669e/fqnnvu0YoVK9SwYUN9+umnmjp1quG4FDIAAADBgCuSvctbVyh7wiLLNV33yNQM+8+cOVNbt26VJA0bNkxJSUl64403NHz4cMNxKWQAAACCjbevSA6U64iDzdVOhny5T8aDPTJFRUVas2aNPv/8c5WWluof//iH7Ha78vLytH37dpWVlWnJkiWG4lLIAAAABBuuSDaX0+nbtjRf75PxYI9MXl6eiouLJUmdOnXSTTfdpPPnfyi4zpw5o7i4OMNxKWQAAAAAfwnCWSXXYf+MjAylp6fr3LlzCg0NVevWrbV582bFxMTo4sWLuv322w3HpZABAACA9wTbDWbuutruF1/wxT4ZD/bISNLvfvc7zZo1S/3799ehQ4fUr18/2Ww2NWnSRJ999pmGDRtmKC6FDAAAALyHWRnjvD2rVMN1ZskX+2Q82COTlZWlN954Q3369NHevXs1ZcoUSVJGRobsdruysrIMx2WPDAAAADwTqHtVAl3NrJK3X9HNA+p5uLaWZWZm6he/+IUKCgo0cOBAxcfHa+vWrWrdurV69uyp2bNnG47LiQwAAAA8Eyh7VazA9bTEl214oz8ImFvLXIf9s7KytG3bNrVr105fffWVDh48qIMHD7JHBgAAACYJhL0qVmPVNjw3W8tc98hkZmYqIyNDISEhmjRpkk6cOFHnPTK0lgEAAAD+EoRteEVFRXr00Ue1ZcsPiztfffVVPfbYY1q7dq0OHTqkoqIiPf/88/rwww/disuJDAAAAOAvgdyG56P9M66tZdnZ2fr2228VFRWlmJgYNWnSRBcvXlSDBg109OhRzZs3T48//rihuJzIAAAAAP5U04bn7quBD244c1V93idLNGvvkXE6nfr8889VVlamzp07q7y8XA6HQ7GxsUpISDAclxMZAABgXewsMY7fK2vz9yLNq+2f8XCPTG5uriQpNTVVixcvVnFxsXJycvTMM89o1qxZuu222wzFpZABAADWZdVhaeBKnM7Lt535e5Hm1fbPeLBHJjs7W6dOnVJZWZn27dunRYsWqbS0VAMHDlR5eblatGhhOC6FDAAAsJaaYekDW8zOBPAuo6cuvlqk6XRKuUOlsnyvhq3dWta3b19VVFTo3nvvVcuWLVVYWKjbb79dJSUlOnfO+MkhhQwAALCWQB6WDmSu+0vw8650MuJL/j51qc1mk8as+vn9M17eI9OwYUM5nU5FR0eradOmhuNSyAAAAOthZwl8yd/zKJdT+9TFtRA1uyD18h6ZlJQU7dmzR7m5uXrxxRcNx6WQAYLYuSq72SkAgaHKLjkjFGW7YHYmAGqYcSJSw+yTkctpECm1SfF625c/FBUVKSsr60d7ZAoLC7V582YNGDBAYWFhWrNmjTp16qTJkydr69athuJSyABBLHn6p2anAASQd1TSMM3sJABIgXEiUsNX8yiXE0inLl70c3tkcnJyZLfbZbfb5XA4DMelkAGCTGSDUCW3a6ptpSfNTgUAgMurDqATkfAo/7Ux1tOLLFxbyzIyMlRVVaVnn31WzZs3V0JCgnJycvTSSy8pKipKERERhuNSyABBxmazKW9cb52vpq0MuKTqnPSn+vPtJ1Cv+PNEpIbryYi/9++M/uDnh+3N5uawf+3Wsq1bt8pms8lut2vx4sVKTEzUv/71L3Xp0sWtNChkgCBks9kUFc4ff+B/QiXmY4DA5M8TkcupRy1eXuPmsH/t1rJFixbpmmuu0YULF/Sb3/xGmzdvVklJicrKyjRjxgzDcUPczRsAAACo12pavOAVtffIVFRUKDExUddcc41Wr16tVq1aafDgwerdu7cmTpxoOC5fyQIAAACugmVXkdNZtzY2N1vLOnbsqAULFig+Pl6S1KdPHy1cuFBPPfWUiouLNXHiRN1+++3Ky8vT3r17DcelkAEAAABqs8KuIk+uqHY6pdwhUtk29/9fN1vLKioqlJmZeam17IEHHtCNN96oU6dOacOGDVqxYoXWrVunNWvW6O233zYcl0IGAAAg2Ph7gN1dgZ5fIAikK6p/Ru3WspycHPXq1Us7d+7UunXrdOedd+qDDz5QWFiYSktLDcelkAEAAAg2DLAHHndPVwJxaecV1B72P3XqlCIjI+V0OtWxY0d16dJFnTp10vjx4zV37lyNHz/eUFwKGQAAgGBQT3eU1Auenq7U5Ypqp1PKHSqV5dftPd1wpT0yrVq1Uu/evSVJPXr0UFZWlh566CHDcSlkAAAAgoGVBthd97gEAzMWgNps0phVfhn2r71H5oknnlB4eLgqKyu1cuVK2Ww2LVu2TO3atdPcuXOVlpZmKC6FDAAAQLCwwgB7IPJkqN4I15kgo6crrsWev4s+D/fIxMXF6dy5czp8+LASExOVl5enwsJCFRcXa+zYsYbjUsgAAAAAV+LvoXqjC0At1CpYe9g/NTVVMTExioiIUFJSkk6fPq2SkhJVVVXp66+/NhyXQgYAAAC4EjPavoxwt1WwrjtjLsfDPTLV1dX65ptvLp2+rFixQjk5ObLZbOrXr5/huBQyAAAAgBF1Gao3wrVNzBdXT3uyM+ZyPNgjk5WVpT179ig1NVVLlixRcnKy4uLiNHDgQDkcDrVp08ZwXAoZAAAABC6zd8q4vr/Rti9P1MNLDlxbyzIzM5WZmamFCxdq/fr1io6OVrdu3VRWVqZjx45p+/bthuNSyAAAACBw1cMP9j9hoXmXuqg97J+fn6/y8nJJUq9evS4VLyUlJXrhhRcMx6WQAQAAQGCp5x/sf8LXV2P7cWfM5bjukZk8ebImTpyopUuXKi0tTSEhIUpJSVFKSooGDx6sRx55xHBcChkAAAAElkDaeeOvnTbeuBr7StdEe7Iz5nI82CNTVFSkb7/9VikpKSotLdW7776rPn36aOLEiSopKVFISIjhuBQyAAAACDyBuPPG7Hmdq/H2QP/VeLBHJi8vT02bNlVycrJWr16tESNGKCIiQi1btpTT6V5cChkAAADAiGCY1/GB2ntkoqOjNWrUKHXt2lXvvfeeYmNjFR4erl27dunBBx/UkiVLDMWlkAEAAACuxIrzOk9/IzXw4WmWh3tkYmJiJEmtWrXSmTNn9Mgjj2jo0KFKTEzUww8/bDguhQwAAABwJYE0r3M1rrM8sxN9+14e7JHJzs7WzJkztXHjRq1fv14jRoy49Ov79u3T9u3bNXDgQENxjU/TAAAAAMGoZl4nkF/RzX84OQpAtVvLdu/eLbvdrpiYGDkcDhUUFKi8vFwJCQn66KOPDMflRAYAAAAww5VuGaur0R9472ayq3Gztaz2Hpnc3FwNHjxY0dHROnjwoMaMGaN33nlH9913n+x2u+G4FDIAAACwLm8XA/7iz1vGvM3N1jLXPTIZGRlq06aN8vPz1bVrVx06dEiStHDhQo0cOVILFiwwHJdCBgAAANbkdEoLB0oH/s/sTHAVrntkJKmwsFAXLlxQYWGh+vbtqw0bNui5555TZWWltm/frjvuuMNQXAoZAABcBfKeCAA/VnWOIsYCareWrV69Wv369VPDhg114cIF2Ww2nTx5Utdff/2lm82MoJABAMAVeyIAa/r9Pik8yuwsjHM6pdyhUlm+2Zn4XO1h/6+++kobN25USEiISkpKlJSUpKSkJEVGRio9PV1ffPGFobgUMgAAWHFPBABrs9mkMav8M5zvbR7ukQkJCdGePXs0btw4nThxQuvWrdPEiRPVs2dPtW/f3nBcChkAAKyyJwLAj7nuTuE01X883CMTHR2tbt26yWaz6YEHHlBsbKzGjRsnu92u2NhYw3ENFTJO5w/Jfv/9924lDQAAAPiMM1xqnhwU7VmB5Pv/X8jU1Ag/p/atZTUWLFigZs2aaejQoUpPT5fdbtewYcMM52GokDlz5owkqW3btoYDAwAAAKi/Kioq1LhxY0P/XWZmpnbs2KHs7GxFRkaqsLBQoaGhmj17tkpKSjRjxgydPXtWzz33nOH3tzkNlFIOh0OHDh1So0aNZLPZDAf3le+//***********************************************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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x700 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x700 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x700 with 1 Axes>"], "image/png": "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**********************************************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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["# Elbow Method to determine the optimal number of clusters\n", "def elbow_method(X, max_clusters=10):\n", "    inertia = []\n", "    for n in range(1, max_clusters+1):\n", "        agg_clustering = AgglomerativeClustering(n_clusters=n, linkage='ward')\n", "        labels = agg_clustering.fit_predict(X)\n", "        # Use silhouette score as a proxy for inertia since inertia isn't defined for Agglomerative\n", "        if n > 1:\n", "            score = silhouette_score(X, labels)\n", "        else:\n", "            score = 0  # Si<PERSON>houette score is undefined for 1 cluster\n", "        inertia.append(score)\n", "\n", "    plt.plot(range(1, max_clusters+1), inertia, marker='o')\n", "    plt.title('Elbow Method (Using <PERSON><PERSON><PERSON><PERSON> Score as Inertia)')\n", "    plt.xlabel('Number of clusters')\n", "    plt.ylabel('Silhouette Score')\n", "    plt.show()\n", "\n", "elbow_method(X_scaled)\n", "\n", "# Silhouette Score Evaluation for a specific number of clusters\n", "def silhouette_evaluation(X, n_clusters):\n", "    agg_clustering = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')\n", "    labels = agg_clustering.fit_predict(X)\n", "\n", "    silhouette_avg = silhouette_score(X, labels)\n", "    print(f\"Silhouette Score for {n_clusters} clusters: {silhouette_avg}\")\n", "\n", "    # Plot <PERSON><PERSON><PERSON><PERSON>\n", "    plt.hist(silhouette_score(X, labels, metric='euclidean'), bins=10)\n", "    plt.title(f'<PERSON><PERSON><PERSON><PERSON> Score Histogram for {n_clusters} Clusters')\n", "    plt.xlabel('Silhouette Score')\n", "    plt.ylabel('Frequency')\n", "    plt.show()\n", "\n", "# Example: Evaluating silhouette score for 3 clusters\n", "silhouette_evaluation(X_scaled, 3)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 944}, "id": "lsiyo8E5T0rN", "outputId": "7752ba55-5255-4076-9418-90808abef3b3"}, "execution_count": 5, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Silhouette Score for 3 clusters: 0.32148866683112387\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}