{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "WBnQDgdPmLxK"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn import linear_model\n", "from sklearn.metrics import r2_score\n", "%matplotlib inline"]}, {"cell_type": "code", "source": ["df=pd.read_csv('/content/test.csv')\n", "df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 424}, "id": "JxdbM0FAopC7", "outputId": "22dde825-408a-42e1-eb77-f462132dbd45"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["      x          y\n", "0    77  79.775152\n", "1    21  23.177279\n", "2    22  25.609262\n", "3    20  17.857388\n", "4    36  41.849864\n", "..   ..        ...\n", "295  71  68.545888\n", "296  46  47.334876\n", "297  55  54.090637\n", "298  62  63.297171\n", "299  47  52.459467\n", "\n", "[300 rows x 2 columns]"], "text/html": ["\n", "  <div id=\"df-78aa7d29-4ed3-4605-8666-5a7ecce3ecba\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>77</td>\n", "      <td>79.775152</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>21</td>\n", "      <td>23.177279</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>22</td>\n", "      <td>25.609262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>20</td>\n", "      <td>17.857388</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>36</td>\n", "      <td>41.849864</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>295</th>\n", "      <td>71</td>\n", "      <td>68.545888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>296</th>\n", "      <td>46</td>\n", "      <td>47.334876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>297</th>\n", "      <td>55</td>\n", "      <td>54.090637</td>\n", "    </tr>\n", "    <tr>\n", "      <th>298</th>\n", "      <td>62</td>\n", "      <td>63.297171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>299</th>\n", "      <td>47</td>\n", "      <td>52.459467</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>300 rows × 2 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-78aa7d29-4ed3-4605-8666-5a7ecce3ecba')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-78aa7d29-4ed3-4605-8666-5a7ecce3ecba button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-78aa7d29-4ed3-4605-8666-5a7ecce3ecba');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-5af1f798-384e-4d17-b05b-5d16c902ad36\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-5af1f798-384e-4d17-b05b-5d16c902ad36')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-5af1f798-384e-4d17-b05b-5d16c902ad36 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_21f56229-b2a2-4ea5-94ab-0a970175770e\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_21f56229-b2a2-4ea5-94ab-0a970175770e button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "df", "summary": "{\n  \"name\": \"df\",\n  \"rows\": 300,\n  \"fields\": [\n    {\n      \"column\": \"x\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 28,\n        \"min\": 0,\n        \"max\": 100,\n        \"num_unique_values\": 97,\n        \"samples\": [\n          38,\n          88,\n          6\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 29.071480855972098,\n        \"min\": -3.467883789,\n        \"max\": 105.5918375,\n        \"num_unique_values\": 300,\n        \"samples\": [\n          92.88772282,\n          79.50341495,\n          97.00148372\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 5}]}, {"cell_type": "markdown", "source": ["Scatter plot"], "metadata": {"id": "eOaJw2L7t6Wt"}}, {"cell_type": "code", "source": ["plt.scatter(df['x'], df['y'])\n", "plt.xlabel('value of x')\n", "plt.ylabel('value of y')\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 430}, "id": "2wLYkcFNpBvf", "outputId": "cfc8e55f-554b-408c-ff6b-2e68403fd368"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["Regressor line"], "metadata": {"id": "__cUPqept19N"}}, {"cell_type": "code", "source": ["reg=linear_model.LinearRegression()\n", "reg.fit(df[['x']],df['y'])\n", "plt.scatter(df['x'], df['y'])\n", "plt.plot(df['x'], reg.predict(df[['x']]), color='red')\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 430}, "id": "dCP2g6fEpW5P", "outputId": "7f483d45-a115-4e67-95f2-7510b96e5c6c"}, "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["da_corr=df.corr()\n", "sns.heatmap(da_corr,annot=True)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "Xix18WYurkSN", "outputId": "83d32597-e1d1-44e5-d342-eee85ef2b039"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<Axes: >"]}, "metadata": {}, "execution_count": 16}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAhUAAAGiCAYAAABQwzQuAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAwpElEQVR4nO3de3RU9bn/8U+uMym5sGhibmIowZNUkURDiMFbqZFwaQsY/SG1glF6frSAhfRASQkXuTRaFUGgFdtaIbRHakHQg02MUVI5RoIBFIvcFSRXUAgkhpBk5veHP0dnJ9jM7o4z1PfLtdcq22d/852sZXnW8zz7O35Op9MpAACAf5G/tzcAAAD+PZBUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAAAAS5BUAADgI/7+97/r+9//vuLi4uTn56fNmzf/02e2bdum6667TjabTQMGDNAzzzzTKWb16tXq16+f7Ha7MjIyVFlZ6fbvz58/r6lTp+qb3/ymQkNDlZOTo/r6eo/3T1IBAICPaG5uVkpKilavXt2t+Pfff1+jR4/WsGHDtGfPHs2YMUOTJ09WSUmJK2bDhg3Ky8vTggULtGvXLqWkpCg7O1sNDQ2umJkzZ+rFF1/Uc889p/LyctXU1Oj222/3eP9+fKEYAAC+x8/PT88//7zGjh170Zhf/OIX2rp1q959913XvbvuuktnzpxRcXGxJCkjI0Pp6elatWqVJMnhcKhv376aPn265syZo8bGRkVFRenPf/6z7rjjDknS/v379e1vf1sVFRW6/vrru71nKhUAAPSg1tZWnT171u1qbW21ZO2KigplZWW53cvOzlZFRYUk6cKFC6qqqnKL8ff3V1ZWliumqqpKbW1tbjHJycm64oorXDHdFWj2g1it7dRRb28B8DkhcTd5ewuAT2q/UN2j61v5d1LhqnV68MEH3e4tWLBACxcu/JfXrqurU3R0tNu96OhonT17Vi0tLTp9+rQ6Ojq6jNm/f79rjeDgYPXu3btTTF1dnUf78ZmkAgAAn+HosGyp/Px85eXlud2z2WyWre9LSCoAAOhBNputx5KImJiYTm9p1NfXKzw8XCEhIQoICFBAQECXMTExMa41Lly4oDNnzrhVK74Y013MVAAAYOR0WHf1oMzMTJWVlbndKy0tVWZmpiQpODhYaWlpbjEOh0NlZWWumLS0NAUFBbnFHDhwQMePH3fFdBeVCgAAjBw9mwxcTFNTkw4fPuz68/vvv689e/aoT58+uuKKK5Sfn6/q6mqtW7dOkjRlyhStWrVKs2fP1n333adXX31Vf/nLX7R161bXGnl5eZo0aZIGDx6sIUOGaPny5WpublZubq4kKSIiQvfff7/y8vLUp08fhYeHa/r06crMzPTozQ+JpAIAgE6cPVxhuJi33npLw4YNc/35s1mMSZMm6ZlnnlFtba2OHz/u+vff+ta3tHXrVs2cOVMrVqzQ5Zdfrt///vfKzs52xYwfP14nT57U/PnzVVdXp9TUVBUXF7sNbz7++OPy9/dXTk6OWltblZ2drd/85jce799nzqng7Q+gM97+ALrW029/XKj5h2VrBcddbdlavo5KBQAARl5qf1zqSCoAADDyUvvjUsfbHwAAwBJUKgAAMLLw8KuvE5IKAACMaH+YQvsDAABYgkoFAABGvP1hCkkFAAAG3jr86lJH+wMAAFiCSgUAAEa0P0whqQAAwIj2hykkFQAAGHFOhSnMVAAAAEtQqQAAwIj2hykkFQAAGDGoaQrtDwAAYAkqFQAAGNH+MIWkAgAAI9ofptD+AAAAlqBSAQCAgdPJORVmkFQAAGDETIUptD8AAIAlqFQAAGDEoKYpJBUAABjR/jCFpAIAACO+UMwUZioAAIAlqFQAAGBE+8MUkgoAAIwY1DSF9gcAALAElQoAAIxof5hCUgEAgBHtD1NofwAAAEtQqQAAwIhKhSkkFQAAGPAtpebQ/gAAAJagUgEAgBHtD1NIKgAAMOKVUlNIKgAAMKJSYQozFQAAwBJUKgAAMKL9YQpJBQAARrQ/TKH9AQAALEGlAgAAI9ofppBUAABgRPvDFNofAADAElQqAAAwolJhCkkFAABGzFSYQvsDAABYgkoFAABGtD9MIakAAMCI9ocpJBUAABhRqTCFmQoAAGAJKhUAABjR/jCFpAIAACPaH6bQ/gAAAJagUgEAgBGVClNIKgAAMHI6vb2DSxLtDwAAfMjq1avVr18/2e12ZWRkqLKy8qKxbW1tWrRokRITE2W325WSkqLi4mK3mHPnzmnGjBlKSEhQSEiIhg4dqp07d7rF1NfX695771VcXJy+8Y1vaMSIETp06JDHeyepAADAyOGw7vLAhg0blJeXpwULFmjXrl1KSUlRdna2GhoauowvKCjQmjVrtHLlSu3bt09TpkzRuHHjtHv3blfM5MmTVVpaqqKiIu3du1fDhw9XVlaWqqurJUlOp1Njx47V0aNHtWXLFu3evVsJCQnKyspSc3OzR/v3czp9o8bTduqot7cA+JyQuJu8vQXAJ7VfqO7R9Vv+NM+ytfzvKFBra6vbPZvNJpvN1ik2IyND6enpWrVqlSTJ4XCob9++mj59uubMmdMpPi4uTnPnztXUqVNd93JychQSEqL169erpaVFYWFh2rJli0aPHu2KSUtL08iRI7VkyRIdPHhQSUlJevfdd3X11Ve7fm5MTIx+9atfafLkyd3/rN2OBAAAHissLFRERITbVVhY2CnuwoULqqqqUlZWluuev7+/srKyVFFR0eXara2tstvtbvdCQkK0fft2SVJ7e7s6Ojq+NOazhOeLMf7+/rLZbK6Y7iKpAADAyOmw7MrPz1djY6PblZ+f3+lHnjp1Sh0dHYqOjna7Hx0drbq6ui63mZ2drWXLlunQoUNyOBwqLS3Vpk2bVFtbK0kKCwtTZmamFi9erJqaGnV0dGj9+vWqqKhwxSQnJ+uKK65Qfn6+Tp8+rQsXLujhhx/WiRMnXDHdRVIBAICRhTMVNptN4eHhbldXrQ8zVqxYoSuvvFLJyckKDg7WtGnTlJubK3//z/96LyoqktPpVHx8vGw2m5544glNmDDBFRMUFKRNmzbp4MGD6tOnj77xjW/otdde08iRI93W6Q6SCgAAjJxO665uioyMVEBAgOrr693u19fXKyYmpstnoqKitHnzZjU3N+vYsWPav3+/QkND1b9/f1dMYmKiysvL1dTUpA8//FCVlZVqa2tzi0lLS9OePXt05swZ1dbWqri4WB999JFbTHeQVAAA4AOCg4OVlpamsrIy1z2Hw6GysjJlZmZ+6bN2u13x8fFqb2/Xxo0bNWbMmE4xvXr1UmxsrE6fPq2SkpIuYyIiIhQVFaVDhw7prbfe6jLmy3D4FQAARl46UTMvL0+TJk3S4MGDNWTIEC1fvlzNzc3Kzc2VJE2cOFHx8fGuQc8dO3aourpaqampqq6u1sKFC+VwODR79mzXmiUlJXI6nUpKStLhw4c1a9YsJScnu9aUpOeee05RUVG64oortHfvXv3sZz/T2LFjNXz4cI/2T1IBAICRl5KK8ePH6+TJk5o/f77q6uqUmpqq4uJi1/Dm8ePH3eYczp8/r4KCAh09elShoaEaNWqUioqK1Lt3b1fMZ4OhJ06cUJ8+fZSTk6OlS5cqKCjIFVNbW6u8vDzV19crNjZWEydO1Lx5nr9WyzkVgA/jnAqgaz1+TsUf/suytULuf9SytXwdlQoAAIycfKGYGSQVAAAYOB0+UcS/5PD2BwAAsASVCgAAjLw0qHmpI6kAAMCImQpTaH8AAABLUKkAAMCIQU1TSCoAADBipsIUkgoAAIxIKkxhpgIAAFiCSgUAAEa+8Q0WlxySCgAAjGh/mEL7A5Kkt/bs1dTZCzTsB3dr4A0jVfb3N7y9JaBH/WTKJB0++Kaazh7RG9tfVPrg1IvGBgYGqmDuDB1473/VdPaIqt4qVfbw77jFhIb20mOPPqgjh3boXONhvV6+RYPTUnr2QwA+hqQCkqSWlvNKGtBfc3/+********************************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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": [" Predicted Y"], "metadata": {"id": "q78VKPiqtoj8"}}, {"cell_type": "code", "source": ["reg=linear_model.LinearRegression()\n", "reg.fit(df[['x']],df['y'])\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 75}, "id": "g7DsCRqup489", "outputId": "1816c038-a1bd-49c6-a94a-73d855e242eb"}, "execution_count": null, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["LinearRegression()"], "text/html": ["<style>#sk-container-id-1 {color: black;background-color: white;}#sk-container-id-1 pre{padding: 0;}#sk-container-id-1 div.sk-toggleable {background-color: white;}#sk-container-id-1 label.sk-toggleable__label {cursor: pointer;display: block;width: 100%;margin-bottom: 0;padding: 0.3em;box-sizing: border-box;text-align: center;}#sk-container-id-1 label.sk-toggleable__label-arrow:before {content: \"▸\";float: left;margin-right: 0.25em;color: #696969;}#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {color: black;}#sk-container-id-1 div.sk-estimator:hover label.sk-toggleable__label-arrow:before {color: black;}#sk-container-id-1 div.sk-toggleable__content {max-height: 0;max-width: 0;overflow: hidden;text-align: left;background-color: #f0f8ff;}#sk-container-id-1 div.sk-toggleable__content pre {margin: 0.2em;color: black;border-radius: 0.25em;background-color: #f0f8ff;}#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {max-height: 200px;max-width: 100%;overflow: auto;}#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {content: \"▾\";}#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 input.sk-hidden--visually {border: 0;clip: rect(1px 1px 1px 1px);clip: rect(1px, 1px, 1px, 1px);height: 1px;margin: -1px;overflow: hidden;padding: 0;position: absolute;width: 1px;}#sk-container-id-1 div.sk-estimator {font-family: monospace;background-color: #f0f8ff;border: 1px dotted black;border-radius: 0.25em;box-sizing: border-box;margin-bottom: 0.5em;}#sk-container-id-1 div.sk-estimator:hover {background-color: #d4ebff;}#sk-container-id-1 div.sk-parallel-item::after {content: \"\";width: 100%;border-bottom: 1px solid gray;flex-grow: 1;}#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {background-color: #d4ebff;}#sk-container-id-1 div.sk-serial::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: 0;}#sk-container-id-1 div.sk-serial {display: flex;flex-direction: column;align-items: center;background-color: white;padding-right: 0.2em;padding-left: 0.2em;position: relative;}#sk-container-id-1 div.sk-item {position: relative;z-index: 1;}#sk-container-id-1 div.sk-parallel {display: flex;align-items: stretch;justify-content: center;background-color: white;position: relative;}#sk-container-id-1 div.sk-item::before, #sk-container-id-1 div.sk-parallel-item::before {content: \"\";position: absolute;border-left: 1px solid gray;box-sizing: border-box;top: 0;bottom: 0;left: 50%;z-index: -1;}#sk-container-id-1 div.sk-parallel-item {display: flex;flex-direction: column;z-index: 1;position: relative;background-color: white;}#sk-container-id-1 div.sk-parallel-item:first-child::after {align-self: flex-end;width: 50%;}#sk-container-id-1 div.sk-parallel-item:last-child::after {align-self: flex-start;width: 50%;}#sk-container-id-1 div.sk-parallel-item:only-child::after {width: 0;}#sk-container-id-1 div.sk-dashed-wrapped {border: 1px dashed gray;margin: 0 0.4em 0.5em 0.4em;box-sizing: border-box;padding-bottom: 0.4em;background-color: white;}#sk-container-id-1 div.sk-label label {font-family: monospace;font-weight: bold;display: inline-block;line-height: 1.2em;}#sk-container-id-1 div.sk-label-container {text-align: center;}#sk-container-id-1 div.sk-container {/* jupyter's `normalize.less` sets `[hidden] { display: none; }` but bootstrap.min.css set `[hidden] { display: none !important; }` so we also need the `!important` here to be able to override the default hidden behavior on the sphinx rendered scikit-learn.org. See: https://github.com/scikit-learn/scikit-learn/issues/21755 */display: inline-block !important;position: relative;}#sk-container-id-1 div.sk-text-repr-fallback {display: none;}</style><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LinearRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label sk-toggleable__label-arrow\">LinearRegression</label><div class=\"sk-toggleable__content\"><pre>LinearRegression()</pre></div></div></div></div></div>"]}, "metadata": {}, "execution_count": 10}]}, {"cell_type": "code", "source": ["pp=reg.predict([[190]])\n", "pp"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bgFwh2skqCKk", "outputId": "16df31e2-9037-43ba-9e33-ed7a72800c09"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.10/dist-packages/sklearn/base.py:439: UserWarning: X does not have valid feature names, but LinearRegression was fitted with feature names\n", "  warnings.warn(\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["array([192.2619067])"]}, "metadata": {}, "execution_count": 11}]}, {"cell_type": "markdown", "source": ["Find Y for new values"], "metadata": {"id": "kg1FJ2n3tq31"}}, {"cell_type": "code", "source": ["new_values=[100,200,300]\n", "new_df=pd.DataFrame({'x':new_values})\n", "predicted_y=reg.predict(new_df[['x']])\n", "for x,y in zip(new_values predicted_y):\n", "  print(f\"For x = {x}, predicted y = {y}\")\n", "  new_df['y']=predicted_y\n", "\n", "new_df"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 196}, "id": "Mw8ZaWX0qME0", "outputId": "f94d1667-b32c-4f18-c9c1-e101a4b724ba"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["For x = 100, predicted y = 100.97172473829062\n", "For x = 200, predicted y = 202.4052602502424\n", "For x = 300, predicted y = 303.83879576219425\n"]}, {"output_type": "execute_result", "data": {"text/plain": ["     x           y\n", "0  100  100.971725\n", "1  200  202.405260\n", "2  300  303.838796"], "text/html": ["\n", "  <div id=\"df-65c58375-63be-4a9f-9985-600f5e1e09a3\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>100</td>\n", "      <td>100.971725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>200</td>\n", "      <td>202.405260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>300</td>\n", "      <td>303.838796</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-65c58375-63be-4a9f-9985-600f5e1e09a3')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-65c58375-63be-4a9f-9985-600f5e1e09a3 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-65c58375-63be-4a9f-9985-600f5e1e09a3');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-cc1037d2-2f99-422e-aab3-0cb32d4ba530\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cc1037d2-2f99-422e-aab3-0cb32d4ba530')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-cc1037d2-2f99-422e-aab3-0cb32d4ba530 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "  <div id=\"id_6f631a7f-e859-45fc-b115-50fbfe66b0d0\">\n", "    <style>\n", "      .colab-df-generate {\n", "        background-color: #E8F0FE;\n", "        border: none;\n", "        border-radius: 50%;\n", "        cursor: pointer;\n", "        display: none;\n", "        fill: #1967D2;\n", "        height: 32px;\n", "        padding: 0 0 0 0;\n", "        width: 32px;\n", "      }\n", "\n", "      .colab-df-generate:hover {\n", "        background-color: #E2EBFA;\n", "        box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "        fill: #174EA6;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate {\n", "        background-color: #3B4455;\n", "        fill: #D2E3FC;\n", "      }\n", "\n", "      [theme=dark] .colab-df-generate:hover {\n", "        background-color: #434B5C;\n", "        box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "        filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "        fill: #FFFFFF;\n", "      }\n", "    </style>\n", "    <button class=\"colab-df-generate\" onclick=\"generateWithVariable('new_df')\"\n", "            title=\"Generate code using this dataframe.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "       width=\"24px\">\n", "    <path d=\"M7,19H8.4L18.45,9,17,7.55,7,17.6ZM5,21V16.75L18.45,3.32a2,2,0,0,1,2.83,0l1.4,1.43a1.91,1.91,0,0,1,.58,1.4,1.91,1.91,0,0,1-.58,1.4L9.25,21ZM18.45,9,17,7.55Zm-12,3A5.31,5.31,0,0,0,4.9,8.1,5.31,5.31,0,0,0,1,6.5,5.31,5.31,0,0,0,4.9,4.9,5.31,5.31,0,0,0,6.5,1,5.31,5.31,0,0,0,8.1,4.9,5.31,5.31,0,0,0,12,6.5,5.46,5.46,0,0,0,6.5,12Z\"/>\n", "  </svg>\n", "    </button>\n", "    <script>\n", "      (() => {\n", "      const buttonEl =\n", "        document.querySelector('#id_6f631a7f-e859-45fc-b115-50fbfe66b0d0 button.colab-df-generate');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      buttonEl.onclick = () => {\n", "        google.colab.notebook.generateWithVariable('new_df');\n", "      }\n", "      })();\n", "    </script>\n", "  </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "new_df", "summary": "{\n  \"name\": \"new_df\",\n  \"rows\": 3,\n  \"fields\": [\n    {\n      \"column\": \"x\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 100,\n        \"min\": 100,\n        \"max\": 300,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          100,\n          200,\n          300\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 101.43353551195182,\n        \"min\": 100.97172473829062,\n        \"max\": 303.83879576219425,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          100.97172473829062,\n          202.4052602502424,\n          303.83879576219425\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 15}]}, {"cell_type": "markdown", "source": ["Find squarred error and Mean Squarred error"], "metadata": {"id": "K3ko5uh9ttnk"}}, {"cell_type": "code", "source": ["from sklearn.metrics import mean_squared_error\n", "y_re=[303,666,700]\n", "y_pred=new_df['y']\n", "squared_errors=(y_re-y_pred)**2\n", "mse=mean_squared_error(y_re,y_pred)\n", "print(\"squared errors:\",squared_errors)\n", "print(\"mean squared error:\", mse)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ykj4nIAlrSNH", "outputId": "6488c755-77e3-4b0a-df30-0cf1ea474b26"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Squared errors: 0     40815.424005\n", "1    214920.082724\n", "2    156943.699743\n", "Name: y, dtype: float64\n", "Mean squared error: 137559.73549067162\n"]}]}, {"cell_type": "markdown", "source": ["Find absolute error and Mean absolute error\n"], "metadata": {"id": "4QT8oR0XtxVk"}}, {"cell_type": "code", "source": ["absolute_errors=np.abs(y_re-y_pred)\n", "print(\"absolute errors:\", absolute_errors)\n", "\n", "mae=np.mean((y_pred-y_re) ** 2)\n", "print(\"mean absolute error:\", mae)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Nx3qUMIXst8Z", "outputId": "35948f9f-436b-472f-93b1-67e2d7975376"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["absolute errors: 0    202.028275\n", "1    463.594740\n", "2    396.161204\n", "Name: y, dtype: float64\n", "mean absolute error: 137559.73549067162\n"]}]}, {"cell_type": "code", "source": ["reg.fit(df[['x']], df['y'])\n", "y_pred = reg.predict(df[['x']])\n", "r2 = r2_score(df['y'], y_pred)\n", "print(\"R-squared:\", r2)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-T__sMU7tjzy", "outputId": "31fd1700-6f19-43e2-d94d-82c984258747"}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["R-squared: 0.9891203611402716\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "JnwzhNiFu8xP"}, "execution_count": null, "outputs": []}]}