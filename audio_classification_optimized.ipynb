{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🎵 Audio Classification: Scream Detection\n",
    "\n",
    "## Optimized Deep Learning Pipeline for Audio Classification\n",
    "\n",
    "This notebook implements an advanced audio classification system that detects screaming vs non-screaming sounds using:\n",
    "- Custom CNN architecture designed for audio spectrograms\n",
    "- Professional data preprocessing and augmentation\n",
    "- Modern training techniques with proper validation\n",
    "- Comprehensive evaluation and visualization\n",
    "\n",
    "### Key Improvements:\n",
    "- ✅ Standardized audio preprocessing\n",
    "- ✅ Custom CNN model for audio spectrograms\n",
    "- ✅ Advanced data augmentation\n",
    "- ✅ Proper train/validation/test splits\n",
    "- ✅ Learning rate scheduling and early stopping\n",
    "- ✅ Comprehensive evaluation metrics"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📚 Import Libraries and Setup\n",
    "\n",
    "Import all necessary libraries and set up reproducible random seeds for consistent results."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import core libraries\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import os\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set random seeds for reproducibility\n",
    "import torch\n",
    "import random\n",
    "np.random.seed(42)\n",
    "torch.manual_seed(42)\n",
    "random.seed(42)\n",
    "if torch.cuda.is_available():\n",
    "    torch.cuda.manual_seed(42)\n",
    "\n",
    "print(\"✅ Core libraries imported and random seeds set\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import specialized libraries\n",
    "import torchaudio\n",
    "import torch.nn as nn\n",
    "import torch.nn.functional as F\n",
    "import matplotlib.pyplot as plt\n",
    "from pathlib import Path\n",
    "from torch.utils.data import Dataset, DataLoader\n",
    "from sklearn.metrics import classification_report, confusion_matrix\n",
    "from sklearn.model_selection import train_test_split\n",
    "import seaborn as sns\n",
    "\n",
    "print(\"✅ All libraries imported successfully\")\n",
    "print(f\"PyTorch version: {torch.__version__}\")\n",
    "print(f\"Device available: {'CUDA' if torch.cuda.is_available() else 'CPU'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## ⚙️ Configuration Management\n",
    "\n",
    "Centralized configuration class for easy parameter management and experimentation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class Config:\n",
    "    # Audio processing parameters\n",
    "    SAMPLE_RATE = 22050  # Standardized sample rate\n",
    "    DURATION = 3.0       # Fixed duration in seconds\n",
    "    N_MELS = 128         # Mel frequency bands\n",
    "    N_FFT = 2048         # FFT window size\n",
    "    HOP_LENGTH = 512     # Hop length for STFT\n",
    "    \n",
    "    # Training parameters\n",
    "    BATCH_SIZE = 32\n",
    "    LEARNING_RATE = 0.001\n",
    "    EPOCHS = 50\n",
    "    EARLY_STOPPING_PATIENCE = 10\n",
    "    \n",
    "    # Data paths (update for your environment)\n",
    "    POSITIVE_PATH = '/kaggle/input/ml-review-2-2/positive'\n",
    "    NEGATIVE_PATH = '/kaggle/input/ml-review-2-2/negative'\n",
    "\n",
    "print(\"✅ Configuration class defined\")\n",
    "print(f\"Sample Rate: {Config.SAMPLE_RATE} Hz\")\n",
    "print(f\"Audio Duration: {Config.DURATION} seconds\")\n",
    "print(f\"Mel Bands: {Config.N_MELS}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📁 Enhanced Audio Data Loading\n",
    "\n",
    "Improved audio loading with error handling, standardization, and preprocessing."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def load_audio_files(path: str, label: str, max_files: int = None):\n",
    "    \\\"\\\"\\\"Load and preprocess audio files with standardization.\\\"\\\"\\\"\\n\",
    "    dataset = []\\n\",
    "    walker = sorted(str(p) for p in Path(path).glob('*.wav'))\\n\",
    "    \\n\",
    "    if max_files:\\n\",
    "        walker = walker[:max_files]\\n\",
    "    \\n\",
    "    print(f\\\"Loading {len(walker)} files from {path}\\\")\\n\",
    "    \\n\",
    "    for i, file_path in enumerate(walker):\\n\",
    "        try:\\n\",
    "            # Load audio\\n\",
    "            waveform, sample_rate = torchaudio.load(file_path)\\n\",
    "            \\n\",
    "            # Convert to mono if stereo\\n\",
    "            if waveform.shape[0] > 1:\\n\",
    "                waveform = torch.mean(waveform, dim=0, keepdim=True)\\n\",
    "            \\n\",
    "            # Resample to standard sample rate\\n\",
    "            if sample_rate != Config.SAMPLE_RATE:\\n\",
    "                resampler = torchaudio.transforms.Resample(sample_rate, Config.SAMPLE_RATE)\\n\",
    "                waveform = resampler(waveform)\\n\",
    "            \\n\",
    "            # Standardize duration\\n\",
    "            target_length = int(Config.SAMPLE_RATE * Config.DURATION)\\n\",
    "            current_length = waveform.shape[1]\\n\",
    "            \\n\",
    "            if current_length > target_length:\\n\",
    "                waveform = waveform[:, :target_length]\\n\",
    "            elif current_length < target_length:\\n\",
    "                padding = target_length - current_length\\n\",
    "                waveform = F.pad(waveform, (0, padding))\\n\",
    "            \\n\",
    "            entry = {\\n\",
    "                'waveform': waveform,\\n\",
    "                'sample_rate': Config.SAMPLE_RATE,\\n\",
    "                'label': label,\\n\",
    "                'file_path': file_path\\n\",
    "            }\\n\",
    "            dataset.append(entry)\\n\",
    "            \\n\",
    "        except Exception as e:\\n\",
    "            print(f\\\"Error loading {file_path}: {e}\\\")\\n\",
    "            continue\\n\",
    "    \\n\",
    "    print(f\\\"Successfully loaded {len(dataset)} files\\\")\\n\",
    "    return dataset\\n\",
    "\\n\",
    "# Load datasets\\n\",
    "print(\\\"Loading audio datasets...\\\")\\n\",
    "screaming_dataset = load_audio_files(Config.POSITIVE_PATH, 'yes')\\n\",
    "not_screaming_dataset = load_audio_files(Config.NEGATIVE_PATH, 'not')\\n\",
    "\\n\",
    "print(f\\\"Screaming samples: {len(screaming_dataset)}\\\")\\n\",
    "print(f\\\"Non-screaming samples: {len(not_screaming_dataset)}\\\")\\n\",
    "\\n\",
    "# Combine datasets\\n\",
    "all_data = screaming_dataset + not_screaming_dataset\\n\",
    "print(f\\\"Total samples: {len(all_data)}\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 Custom Dataset and Data Augmentation\n",
    "\n",
    "PyTorch Dataset class with mel-spectrogram conversion and advanced augmentation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class AudioDataset(Dataset):\\n\",
    "    def __init__(self, data, transform=None):\\n\",
    "        self.data = data\\n\",
    "        self.transform = transform\\n\",
    "    \\n\",
    "    def __len__(self):\\n\",
    "        return len(self.data)\\n\",
    "    \\n\",
    "    def __getitem__(self, idx):\\n\",
    "        item = self.data[idx]\\n\",
    "        waveform = item['waveform']\\n\",
    "        label = 1 if item['label'] == 'yes' else 0\\n\",
    "        \\n\",
    "        # Convert to mel-spectrogram\\n\",
    "        mel_spec = torchaudio.transforms.MelSpectrogram(\\n\",
    "            sample_rate=Config.SAMPLE_RATE,\\n\",
    "            n_mels=Config.N_MELS,\\n\",
    "            n_fft=Config.N_FFT,\\n\",
    "            hop_length=Config.HOP_LENGTH\\n\",
    "        )(waveform)\\n\",
    "        \\n\",
    "        # Log scale and normalize\\n\",
    "        mel_spec = torch.log(mel_spec + 1e-8)\\n\",
    "        mel_spec = (mel_spec - mel_spec.mean()) / (mel_spec.std() + 1e-8)\\n\",
    "        \\n\",
    "        if self.transform:\\n\",
    "            mel_spec = self.transform(mel_spec)\\n\",
    "        \\n\",
    "        return mel_spec.squeeze(0), label\\n\",
    "\\n\",
    "print(\\\"✅ AudioDataset class defined\\\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class AudioAugmentation:\\n\",
    "    @staticmethod\\n\",
    "    def time_mask(spec, max_mask_pct=0.1):\\n\",
    "        \\\"\\\"\\\"Apply time masking to spectrogram\\\"\\\"\\\"\\n\",
    "        _, time_steps = spec.shape\\n\",
    "        mask_size = int(time_steps * max_mask_pct)\\n\",
    "        mask_start = torch.randint(0, time_steps - mask_size, (1,))\\n\",
    "        spec[:, mask_start:mask_start + mask_size] = spec.mean()\\n\",
    "        return spec\\n\",
    "    \\n\",
    "    @staticmethod\\n\",
    "    def freq_mask(spec, max_mask_pct=0.1):\\n\",
    "        \\\"\\\"\\\"Apply frequency masking to spectrogram\\\"\\\"\\\"\\n\",
    "        freq_bins, _ = spec.shape\\n\",
    "        mask_size = int(freq_bins * max_mask_pct)\\n\",
    "        mask_start = torch.randint(0, freq_bins - mask_size, (1,))\\n\",
    "        spec[mask_start:mask_start + mask_size, :] = spec.mean()\\n\",
    "        return spec\\n\",
    "\\n\",
    "def get_augmentation_transform():\\n\",
    "    \\\"\\\"\\\"Returns augmentation transform for training\\\"\\\"\\\"\\n\",
    "    def augment(spec):\\n\",
    "        if torch.rand(1) > 0.5:\\n\",
    "            spec = AudioAugmentation.time_mask(spec)\\n\",
    "        if torch.rand(1) > 0.5:\\n\",
    "            spec = AudioAugmentation.freq_mask(spec)\\n\",
    "        return spec\\n\",
    "    return augment\\n\",
    "\\n\",
    "print(\\\"✅ Data augmentation functions defined\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🏗️ Custom CNN Model Architecture\n",
    "\n",
    "Purpose-built CNN model designed specifically for audio spectrograms."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class AudioCNN(nn.Module):\\n\",
    "    def __init__(self, num_classes=2):\\n\",
    "        super(AudioCNN, self).__init__()\\n\",
    "        \\n\",
    "        # Convolutional layers with batch normalization\\n\",
    "        self.conv1 = nn.Conv2d(1, 32, kernel_size=3, padding=1)\\n\",
    "        self.bn1 = nn.BatchNorm2d(32)\\n\",
    "        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)\\n\",
    "        self.bn2 = nn.BatchNorm2d(64)\\n\",
    "        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)\\n\",
    "        self.bn3 = nn.BatchNorm2d(128)\\n\",
    "        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)\\n\",
    "        self.bn4 = nn.BatchNorm2d(256)\\n\",
    "        \\n\",
    "        self.pool = nn.MaxPool2d(2, 2)\\n\",
    "        self.dropout = nn.Dropout(0.3)\\n\",
    "        self.global_pool = nn.AdaptiveAvgPool2d((1, 1))\\n\",
    "        \\n\",
    "        # Fully connected layers\\n\",
    "        self.fc1 = nn.Linear(256, 128)\\n\",
    "        self.fc2 = nn.Linear(128, num_classes)\\n\",
    "        \\n\",
    "    def forward(self, x):\\n\",
    "        # Add channel dimension if needed\\n\",
    "        if x.dim() == 3:\\n\",
    "            x = x.unsqueeze(1)\\n\",
    "        \\n\",
    "        x = self.pool(F.relu(self.bn1(self.conv1(x))))\\n\",
    "        x = self.dropout(x)\\n\",
    "        x = self.pool(F.relu(self.bn2(self.conv2(x))))\\n\",
    "        x = self.dropout(x)\\n\",
    "        x = self.pool(F.relu(self.bn3(self.conv3(x))))\\n\",
    "        x = self.dropout(x)\\n\",
    "        x = self.pool(F.relu(self.bn4(self.conv4(x))))\\n\",
    "        \\n\",
    "        x = self.global_pool(x)\\n\",
    "        x = x.view(x.size(0), -1)\\n\",
    "        x = F.relu(self.fc1(x))\\n\",
    "        x = self.dropout(x)\\n\",
    "        x = self.fc2(x)\\n\",
    "        \\n\",
    "        return x\\n\",
    "\\n\",
    "print(\\\"✅ AudioCNN model architecture defined\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Data Preparation and Splitting\n",
    "\n",
    "Proper train/validation/test splits with data loaders."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Split data into train/validation/test\\n\",
    "train_data, temp_data = train_test_split(all_data, test_size=0.3, random_state=42, \\n\",
    "                                        stratify=[item['label'] for item in all_data])\\n\",
    "val_data, test_data = train_test_split(temp_data, test_size=0.5, random_state=42,\\n\",
    "                                      stratify=[item['label'] for item in temp_data])\\n\",
    "\\n\",
    "print(f\\\"Train samples: {len(train_data)}\\\")\\n\",
    "print(f\\\"Validation samples: {len(val_data)}\\\")\\n\",
    "print(f\\\"Test samples: {len(test_data)}\\\")\\n\",
    "\\n\",
    "# Create datasets\\n\",
    "train_dataset = AudioDataset(train_data, transform=get_augmentation_transform())\\n\",
    "val_dataset = AudioDataset(val_data)\\n\",
    "test_dataset = AudioDataset(test_data)\\n\",
    "\\n\",
    "# Create data loaders\\n\",
    "train_loader = DataLoader(train_dataset, batch_size=Config.BATCH_SIZE, shuffle=True, num_workers=2)\\n\",
    "val_loader = DataLoader(val_dataset, batch_size=Config.BATCH_SIZE, shuffle=False, num_workers=2)\\n\",
    "test_loader = DataLoader(test_dataset, batch_size=Config.BATCH_SIZE, shuffle=False, num_workers=2)\\n\",
    "\\n\",
    "print(\\\"✅ Data loaders created successfully\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🚀 Training Functions\n",
    "\n",
    "Professional training pipeline with monitoring and validation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def train_epoch(model, train_loader, criterion, optimizer, device):\\n\",
    "    model.train()\\n\",
    "    running_loss = 0.0\\n\",
    "    correct = 0\\n\",
    "    total = 0\\n\",
    "    \\n\",
    "    for batch_idx, (data, target) in enumerate(train_loader):\\n\",
    "        data, target = data.to(device), target.to(device)\\n\",
    "        \\n\",
    "        optimizer.zero_grad()\\n\",
    "        output = model(data)\\n\",
    "        loss = criterion(output, target)\\n\",
    "        loss.backward()\\n\",
    "        optimizer.step()\\n\",
    "        \\n\",
    "        running_loss += loss.item()\\n\",
    "        _, predicted = output.max(1)\\n\",
    "        total += target.size(0)\\n\",
    "        correct += predicted.eq(target).sum().item()\\n\",
    "        \\n\",
    "        if batch_idx % 10 == 0:\\n\",
    "            print(f'Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}, Acc: {100.*correct/total:.2f}%')\\n\",
    "    \\n\",
    "    epoch_loss = running_loss / len(train_loader)\\n\",
    "    epoch_acc = 100. * correct / total\\n\",
    "    return epoch_loss, epoch_acc\\n\",
    "\\n\",
    "def validate_epoch(model, val_loader, criterion, device):\\n\",
    "    model.eval()\\n\",
    "    val_loss = 0.0\\n\",
    "    correct = 0\\n\",
    "    total = 0\\n\",
    "    \\n\",
    "    with torch.no_grad():\\n\",
    "        for data, target in val_loader:\\n\",
    "            data, target = data.to(device), target.to(device)\\n\",
    "            output = model(data)\\n\",
    "            val_loss += criterion(output, target).item()\\n\",
    "            \\n\",
    "            _, predicted = output.max(1)\\n\",
    "            total += target.size(0)\\n\",
    "            correct += predicted.eq(target).sum().item()\\n\",
    "    \\n\",
    "    val_loss /= len(val_loader)\\n\",
    "    val_acc = 100. * correct / total\\n\",
    "    return val_loss, val_acc\\n\",
    "\\n\",
    "print(\\\"✅ Training functions defined\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 Main Training Loop\n",
    "\n",
    "Complete training pipeline with early stopping and model checkpointing."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Set device and initialize model\\n\",
    "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\\n\",
    "print(f\\\"Using device: {device}\\\")\\n\",
    "\\n\",
    "model = AudioCNN(num_classes=2).to(device)\\n\",
    "criterion = nn.CrossEntropyLoss()\\n\",
    "optimizer = torch.optim.Adam(model.parameters(), lr=Config.LEARNING_RATE, weight_decay=1e-4)\\n\",
    "scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)\\n\",
    "\\n\",
    "# Training loop\\n\",
    "best_val_loss = float('inf')\\n\",
    "patience_counter = 0\\n\",
    "train_losses, val_losses = [], []\\n\",
    "train_accs, val_accs = [], []\\n\",
    "\\n\",
    "print(\\\"Starting training...\\\")\\n\",
    "for epoch in range(Config.EPOCHS):\\n\",
    "    print(f'\\\\nEpoch {epoch+1}/{Config.EPOCHS}')\\n\",
    "    print('-' * 50)\\n\",
    "    \\n\",
    "    # Training\\n\",
    "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\\n\",
    "    \\n\",
    "    # Validation\\n\",
    "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\\n\",
    "    \\n\",
    "    # Learning rate scheduling\\n\",
    "    scheduler.step(val_loss)\\n\",
    "    \\n\",
    "    # Store metrics\\n\",
    "    train_losses.append(train_loss)\\n\",
    "    val_losses.append(val_loss)\\n\",
    "    train_accs.append(train_acc)\\n\",
    "    val_accs.append(val_acc)\\n\",
    "    \\n\",
    "    print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')\\n\",
    "    print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')\\n\",
    "    \\n\",
    "    # Early stopping\\n\",
    "    if val_loss < best_val_loss:\\n\",
    "        best_val_loss = val_loss\\n\",
    "        patience_counter = 0\\n\",
    "        torch.save(model.state_dict(), 'best_audio_model.pth')\\n\",
    "        print(\\\"New best model saved!\\\")\\n\",
    "    else:\\n\",
    "        patience_counter += 1\\n\",
    "        \\n\",
    "    if patience_counter >= Config.EARLY_STOPPING_PATIENCE:\\n\",
    "        print(f\\\"Early stopping triggered after {epoch+1} epochs\\\")\\n\",
    "        break\\n\",
    "\\n\",
    "# Load best model\\n\",
    "model.load_state_dict(torch.load('best_audio_model.pth'))\\n\",
    "print(\\\"✅ Training completed!\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Visualization and Evaluation\n",
    "\n",
    "Comprehensive model evaluation with visualizations."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Plot training history\\n\",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\\n\",
    "\\n\",
    "# Plot losses\\n\",
    "ax1.plot(train_losses, label='Train Loss')\\n\",
    "ax1.plot(val_losses, label='Validation Loss')\\n\",
    "ax1.set_title('Training and Validation Loss')\\n\",
    "ax1.set_xlabel('Epoch')\\n\",
    "ax1.set_ylabel('Loss')\\n\",
    "ax1.legend()\\n\",
    "ax1.grid(True)\\n\",
    "\\n\",
    "# Plot accuracies\\n\",
    "ax2.plot(train_accs, label='Train Accuracy')\\n\",
    "ax2.plot(val_accs, label='Validation Accuracy')\\n\",
    "ax2.set_title('Training and Validation Accuracy')\\n\",
    "ax2.set_xlabel('Epoch')\\n\",
    "ax2.set_ylabel('Accuracy (%)')\\n\",
    "ax2.legend()\\n\",
    "ax2.grid(True)\\n\",
    "\\n\",
    "plt.tight_layout()\\n\",
    "plt.show()"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Evaluate on test set\\n\",
    "def evaluate_model(model, test_loader, device):\\n\",
    "    model.eval()\\n\",
    "    y_true = []\\n\",
    "    y_pred = []\\n\",
    "    \\n\",
    "    with torch.no_grad():\\n\",
    "        for data, target in test_loader:\\n\",
    "            data, target = data.to(device), target.to(device)\\n\",
    "            output = model(data)\\n\",
    "            _, predicted = output.max(1)\\n\",
    "            \\n\",
    "            y_true.extend(target.cpu().numpy())\\n\",
    "            y_pred.extend(predicted.cpu().numpy())\\n\",
    "    \\n\",
    "    return y_true, y_pred\\n\",
    "\\n\",
    "# Get predictions\\n\",
    "y_true, y_pred = evaluate_model(model, test_loader, device)\\n\",
    "\\n\",
    "# Print classification report\\n\",
    "print(\\\"Classification Report:\\\")\\n\",
    "print(classification_report(y_true, y_pred, target_names=['Non-Scream', 'Scream']))\\n\",
    "\\n\",
    "# Plot confusion matrix\\n\",
    "cm = confusion_matrix(y_true, y_pred)\\n\",
    "plt.figure(figsize=(8, 6))\\n\",
    "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \\n\",
    "            xticklabels=['Non-Scream', 'Scream'], yticklabels=['Non-Scream', 'Scream'])\\n\",
    "plt.title('Confusion Matrix')\\n\",
    "plt.ylabel('True Label')\\n\",
    "plt.xlabel('Predicted Label')\\n\",
    "plt.show()\\n\",
    "\\n\",
    "# Calculate final accuracy\\n\",
    "test_acc = 100 * sum([1 for i, j in zip(y_true, y_pred) if i == j]) / len(y_true)\\n\",
    "print(f\\\"\\\\nFinal Test Accuracy: {test_acc:.2f}%\\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎵 Prediction Function\n",
    "\n",
    "Easy-to-use function for predicting new audio files."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def predict_audio_file(model, file_path, device):\\n\",
    "    \\\"\\\"\\\"Predict whether an audio file contains screaming\\\"\\\"\\\"\\n\",
    "    try:\\n\",
    "        # Load and preprocess audio\\n\",
    "        waveform, sample_rate = torchaudio.load(file_path)\\n\",
    "        \\n\",
    "        # Convert to mono if stereo\\n\",
    "        if waveform.shape[0] > 1:\\n\",
    "            waveform = torch.mean(waveform, dim=0, keepdim=True)\\n\",
    "        \\n\",
    "        # Resample if needed\\n\",
    "        if sample_rate != Config.SAMPLE_RATE:\\n\",
    "            resampler = torchaudio.transforms.Resample(sample_rate, Config.SAMPLE_RATE)\\n\",
    "            waveform = resampler(waveform)\\n\",
    "        \\n\",
    "        # Standardize duration\\n\",
    "        target_length = int(Config.SAMPLE_RATE * Config.DURATION)\\n\",
    "        current_length = waveform.shape[1]\\n\",
    "        \\n\",
    "        if current_length > target_length:\\n\",
    "            waveform = waveform[:, :target_length]\\n\",
    "        elif current_length < target_length:\\n\",
    "            padding = target_length - current_length\\n\",
    "            waveform = F.pad(waveform, (0, padding))\\n\",
    "        \\n\",
    "        # Convert to mel-spectrogram\\n\",
    "        mel_spec = torchaudio.transforms.MelSpectrogram(\\n\",
    "            sample_rate=Config.SAMPLE_RATE,\\n\",
    "            n_mels=Config.N_MELS,\\n\",
    "            n_fft=Config.N_FFT,\\n\",
    "            hop_length=Config.HOP_LENGTH\\n\",
    "        )(waveform)\\n\",
    "        \\n\",
    "        mel_spec = torch.log(mel_spec + 1e-8)\\n\",
    "        mel_spec = (mel_spec - mel_spec.mean()) / (mel_spec.std() + 1e-8)\\n\",
    "        mel_spec = mel_spec.unsqueeze(0).to(device)\\n\",
    "        \\n\",
    "        # Make prediction\\n\",
    "        model.eval()\\n\",
    "        with torch.no_grad():\\n\",
    "            output = model(mel_spec)\\n\",
    "            probabilities = F.softmax(output, dim=1)\\n\",
    "            predicted_class = output.argmax(dim=1).item()\\n\",
    "            confidence = probabilities[0][predicted_class].item()\\n\",
    "        \\n\",
    "        result = {\\n\",
    "            'prediction': 'Scream' if predicted_class == 1 else 'Non-Scream',\\n\",
    "            'confidence': confidence,\\n\",
    "            'probabilities': {\\n\",
    "                'Non-Scream': probabilities[0][0].item(),\\n\",
    "                'Scream': probabilities[0][1].item()\\n\",
    "            }\\n\",
    "        }\\n\",
    "        return result\\n\",
    "        \\n\",
    "    except Exception as e:\\n\",
    "        return {'error': str(e)}\\n\",
    "\\n\",
    "print(\\\"✅ Prediction function ready!\\\")\\n\",
    "print(\\\"\\\\nTo use: result = predict_audio_file(model, 'audio_file.wav', device)\\\")\\n\",
    "print(\\\"Model saved as 'best_audio_model.pth'\\\")"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
