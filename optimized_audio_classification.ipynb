{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🎵 Audio Classification: Scream Detection\n",
    "\n",
    "## Optimized Deep Learning Pipeline for Audio Classification\n",
    "\n",
    "This notebook implements an advanced audio classification system that detects screaming vs non-screaming sounds using:\n",
    "- Custom CNN architecture designed for audio spectrograms\n",
    "- Professional data preprocessing and augmentation\n",
    "- Modern training techniques with proper validation\n",
    "- Comprehensive evaluation and visualization\n",
    "\n",
    "### Key Improvements:\n",
    "- ✅ Standardized audio preprocessing\n",
    "- ✅ Custom CNN model for audio spectrograms\n",
    "- ✅ Advanced data augmentation\n",
    "- ✅ Proper train/validation/test splits\n",
    "- ✅ Learning rate scheduling and early stopping\n",
    "- ✅ Comprehensive evaluation metrics"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📚 Import Libraries and Setup\n",
    "\n",
    "Import all necessary libraries and set up reproducible random seeds for consistent results."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "\"\"\"\n",
    "Audio Classification Project: Scream Detection\n",
    "Optimized version with improved data handling, model architecture, and training procedures.\n",
    "\"\"\"\n",
    "\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import os\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set random seeds for reproducibility\n",
    "import torch\n",
    "import random\n",
    "np.random.seed(42)\n",
    "torch.manual_seed(42)\n",
    "random.seed(42)\n",
    "if torch.cuda.is_available():\n",
    "    torch.cuda.manual_seed(42)\n",
    "\n",
    "print(\"✅ Libraries imported and random seeds set for reproducibility\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Import required libraries\n",
    "import torchaudio\n",
    "import torch\n",
    "import torch.nn as nn\n",
    "import torch.nn.functional as F\n",
    "import matplotlib.pyplot as plt\n",
    "from pathlib import Path\n",
    "from torch.utils.data import Dataset, DataLoader\n",
    "from torchvision import transforms\n",
    "from sklearn.metrics import classification_report, confusion_matrix\n",
    "from sklearn.model_selection import train_test_split\n",
    "import seaborn as sns\n",
    "\n",
    "print(\"✅ All libraries imported successfully\")\n",
    "print(f\"PyTorch version: {torch.__version__}\")\n",
    "print(f\"Device available: {'CUDA' if torch.cuda.is_available() else 'CPU'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## ⚙️ Configuration Management\n",
    "\n",
    "Centralized configuration class for easy parameter management and experimentation."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Configuration class for better parameter management\n",
    "class Config:\n",
    "    # Audio processing parameters\n",
    "    SAMPLE_RATE = 22050  # Standardized sample rate\n",
    "    DURATION = 3.0  # Fixed duration in seconds\n",
    "    N_MELS = 128  # Increased mel bands for better frequency resolution\n",
    "    N_FFT = 2048  # Increased FFT size\n",
    "    HOP_LENGTH = 512\n",
    "    \n",
    "    # Training parameters\n",
    "    BATCH_SIZE = 32\n",
    "    LEARNING_RATE = 0.001\n",
    "    EPOCHS = 50\n",
    "    EARLY_STOPPING_PATIENCE = 10\n",
    "    \n",
    "    # Data paths (update these for your local environment)\n",
    "    POSITIVE_PATH = '/kaggle/input/ml-review-2-2/positive'\n",
    "    NEGATIVE_PATH = '/kaggle/input/ml-review-2-2/negative'\n",
    "\n",
    "print(\"✅ Configuration class defined\")\n",
    "print(f\"Sample Rate: {Config.SAMPLE_RATE} Hz\")\n",
    "print(f\"Audio Duration: {Config.DURATION} seconds\")\n",
    "print(f\"Mel Bands: {Config.N_MELS}\")\n",
    "print(f\"Batch Size: {Config.BATCH_SIZE}\")"
   ]
  }\n",
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
