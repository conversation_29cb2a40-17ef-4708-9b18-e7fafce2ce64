{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Linear and non- Linear SVM"], "metadata": {"id": "IMeNQmAmrxtl"}}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "BFDwcUo-rtLY"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import classification_report,accuracy_score\n", "from sklearn.datasets import make_blobs\n", "from sklearn.datasets import make_moons"]}, {"cell_type": "markdown", "source": ["1. Linear and Non-Linear SVM Without Kernel\n", "(a) Linear SVM Without Kernel"], "metadata": {"id": "_UHLVZIKfqLq"}}, {"cell_type": "code", "source": ["# Generate a dataset\n", "x,y=make_blobs(n_samples=139,centers=2,random_state=9)\n", "\n", "# Split the dataset into training and testing sets\n", "X_train,X_test,y_train,y_test=train_test_split(x,y,test_size=0.3,random_state=11)\n", "\n", "# Initialize and train a linear SVM classifier\n", "svm_classifier=SVC()  # Linear kernel is the default\n", "svm_classifier.fit(X_train,y_train)\n", "\n", "# Predict y for the test set\n", "predicted_y=svm_classifier.predict(X_test)\n", "\n", "# Print evaluation metrics\n", "print(\"Classification Report for Linear SVM:\")\n", "print(classification_report(y_test,predicted_y))\n", "print(\"Model Accuracy:\",accuracy_score(y_test,predicted_y))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "AM7Y4TZAkLdl", "outputId": "766c565d-b0b4-4e27-bbff-dfecf383a1cd"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report for Linear SVM:\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00        21\n", "           1       1.00      1.00      1.00        21\n", "\n", "    accuracy                           1.00        42\n", "   macro avg       1.00      1.00      1.00        42\n", "weighted avg       1.00      1.00      1.00        42\n", "\n", "Model Accuracy: 1.0\n"]}]}, {"cell_type": "code", "source": ["# Function to visualize decision boundaries\n", "def decision_boundary(X,y,model):\n", "    step_size=0.02\n", "    x_range=np.arange(X[:,0].min() - 1,X[:,0].max() + 1,step_size)\n", "    y_range=np.arange(X[:,1].min() - 1,X[:,1].max() + 1,step_size)\n", "    xx,yy=np.meshgrid(x_range,y_range)\n", "\n", "    grid_predictions=model.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    grid_predictions=grid_predictions.reshape(xx.shape)\n", "\n", "    plt.contourf(xx,yy,grid_predictions,alpha=0.75)\n", "    plt.scatter(X[:,0],X[:,1],c=y,edgecolor='k',s=50)\n", "    plt.title('Decision Boundary for Linear SVM')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "decision_boundary(X_test,y_test,svm_classifier)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "mvvPiwlfkxMG", "outputId": "45f3f304-a777-4667-95ca-b12ed73e1334"}, "execution_count": 4, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["(b) Non-Linear SVM Without Kernel"], "metadata": {"id": "dgMeOGbCk4tM"}}, {"cell_type": "code", "source": ["# Generate a dataset that is not linearly separable\n", "x,y=make_moons(n_samples=139,noise=0.3,random_state=42)\n", "\n", "# Divide the data into training and testing sets\n", "train_data,test_data,train_labels,test_labels=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize the SVM classifier (RBF kernel by default)\n", "rbf_svm=SVC()  # Default kernel is RBF,suitable for non-linear separation\n", "rbf_svm.fit(train_data,train_labels)\n", "\n", "# Predict the labels for the test set\n", "predictions=rbf_svm.predict(test_data)\n", "\n", "# Print the classification metrics\n", "print(\"Classification Report for Non-Linear SVM:\")\n", "print(classification_report(test_labels,predictions))\n", "print(\"Model Accuracy:\",accuracy_score(test_labels,predictions))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9e5l2cqik87b", "outputId": "d687cfc2-c35b-4319-c4e2-69d2a5b7e03f"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report for Non-Linear SVM:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.90      0.90      0.90        21\n", "           1       0.90      0.90      0.90        21\n", "\n", "    accuracy                           0.90        42\n", "   macro avg       0.90      0.90      0.90        42\n", "weighted avg       0.90      0.90      0.90        42\n", "\n", "Model Accuracy: 0.9047619047619048\n"]}]}, {"cell_type": "code", "source": ["\n", "# Function to visualize the decision boundary\n", "def display_decision_boundary(X,y,classifier):\n", "    step=0.02\n", "    x0_range=np.arange(X[:,0].min() - 1,X[:,0].max() + 1,step)\n", "    x1_range=np.arange(X[:,1].min() - 1,X[:,1].max() + 1,step)\n", "    grid_x,grid_y=np.meshgrid(x0_range,x1_range)\n", "\n", "    grid_predictions=classifier.predict(np.c_[grid_x.ravel(),grid_y.ravel()])\n", "    grid_predictions=grid_predictions.reshape(grid_x.shape)\n", "\n", "    plt.contourf(grid_x,grid_y,grid_predictions,alpha=0.75)\n", "    plt.scatter(X[:,0],X[:,1],c=y,edgecolor='k',s=55,marker='o')\n", "    plt.title('Decision Boundary for Non-Linear SVM')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "display_decision_boundary(test_data,test_labels,rbf_svm)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "QTco-xV5lm8Z", "outputId": "5238b697-788f-4a0b-baf6-b167b76ab8d2"}, "execution_count": 6, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["2. Linear SVM with Linear Kernel\n"], "metadata": {"id": "6dCxgMuQmxc5"}}, {"cell_type": "code", "source": ["# Generate a dataset\n", "x,y=make_blobs(n_samples=139,centers=2,random_state=9)\n", "\n", "# Split the dataset into training and testing sets\n", "X_train,X_test,y_train,y_test=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize and train an SVM model with a linear kernel\n", "svm_model_linear=SVC(kernel='linear')\n", "svm_model_linear.fit(X_train,y_train)\n", "\n", "# Predict labels for the test data\n", "predicted_targets=svm_model_linear.predict(X_test)\n", "\n", "# Print the classification metrics\n", "print(\"Classification Report for SVM with Explicit Linear Kernel:\")\n", "print(classification_report(y_test,predicted_targets))\n", "print(\"Model Accuracy:\",accuracy_score(y_test,predicted_targets))\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XDqboGYSm0V7", "outputId": "15a4d230-aae5-4ae7-909b-daaf06d88809"}, "execution_count": 8, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report for SVM with Explicit Linear Kernel:\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00        19\n", "           1       1.00      1.00      1.00        23\n", "\n", "    accuracy                           1.00        42\n", "   macro avg       1.00      1.00      1.00        42\n", "weighted avg       1.00      1.00      1.00        42\n", "\n", "Model Accuracy: 1.0\n"]}]}, {"cell_type": "code", "source": ["# Define function to visualize decision boundary\n", "def visualize_boundary(test_data,test_labels,classifier):\n", "    h=0.02\n", "    x_min,x_max=test_data[:,0].min() - 1,test_data[:,0].max() + 1\n", "    y_min,y_max=test_data[:,1].min() - 1,test_data[:,1].max() + 1\n", "    xx,yy=np.meshgrid(np.arange(x_min,x_max,h),np.arange(y_min,y_max,h))\n", "\n", "    Z=classifier.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    Z=Z.reshape(xx.shape)\n", "    plt.contourf(xx,yy,Z,alpha=0.8)\n", "    plt.scatter(test_data[:,0],test_data[:,1],c=test_labels,edgecolors='k',marker='o')\n", "    plt.title('SVM Decision Boundary with Linear Kernel')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "visualize_boundary(X_test,y_test,svm_model_linear)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "7REpJ2ofnaIw", "outputId": "c987c1db-d4bc-463f-c765-ab232938a132"}, "execution_count": 9, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["3. Non-Linear SVM with Various Kernels\n", "(a) <PERSON><PERSON><PERSON> (RBF Kernel)"], "metadata": {"id": "Jv4XZJaxn6a9"}}, {"cell_type": "code", "source": ["# Generate a dataset\n", "x,y=make_moons(n_samples=139,noise=0.3,random_state=42)\n", "\n", "# Split the dataset into training and testing sets\n", "X_train,X_test,y_train,y_test=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize and train an SVM model with an RBF (Gaussian) kernel\n", "svm_rbf=SVC(kernel='rbf')\n", "svm_rbf.fit(X_train,y_train)\n", "\n", "# Predict the labels for the test data\n", "predicted_y=svm_rbf.predict(X_test)\n", "\n", "# Display the classification report and accuracy\n", "print(\"Classification Report for SVM with Gaussian (RBF) Kernel:\")\n", "print(classification_report(y_test,predicted_y))\n", "print(\"Model Accuracy:\",accuracy_score(y_test,predicted_y))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_nEix6azn4B-", "outputId": "484a7c80-f9a6-4e63-bf73-310d15a50317"}, "execution_count": 10, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Classification Report for SVM with Gaussian (RBF) Kernel:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.90      0.90      0.90        21\n", "           1       0.90      0.90      0.90        21\n", "\n", "    accuracy                           0.90        42\n", "   macro avg       0.90      0.90      0.90        42\n", "weighted avg       0.90      0.90      0.90        42\n", "\n", "Model Accuracy: 0.9047619047619048\n"]}]}, {"cell_type": "code", "source": ["# Function to visualize the decision boundary\n", "def visualize_decision_boundary(data,labels,model):\n", "    step=0.02\n", "    x_min,x_max=data[:,0].min() - 1,data[:,0].max() + 1\n", "    y_min,y_max=data[:,1].min() - 1,data[:,1].max() + 1\n", "    xx,yy=np.meshgrid(np.arange(x_min,x_max,step),np.arange(y_min,y_max,step))\n", "\n", "    Z=model.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    Z=Z.reshape(xx.shape)\n", "    plt.contourf(xx,yy,Z,alpha=0.8)\n", "    plt.scatter(data[:,0],data[:,1],c=labels,edgecolor='k',marker='o')\n", "    plt.title('Decision Boundary for SVM with Gaussian Kernel')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "visualize_decision_boundary(X_test,y_test,svm_rbf)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "6StScrzBonsl", "outputId": "76f0a75f-64a0-4824-f0c9-48d3353a980a"}, "execution_count": 11, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["(b) Gaussian Kernel Radial Basis Function (RBF Kernel)"], "metadata": {"id": "tHue5pWJpcuD"}}, {"cell_type": "code", "source": ["# Generate a dataset\n", "x,y=make_moons(n_samples=139,noise=0.3,random_state=42)\n", "\n", "# Split the dataset into training and testing sets\n", "train_data,test_data,train_labels,test_labels=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize and train an SVM model using the RBF kernel\n", "svm_rbf_kernel=SVC(kernel='rbf')\n", "svm_rbf_kernel.fit(train_data,train_labels)\n", "\n", "# Predict the labels for the test set\n", "test_predictions=svm_rbf_kernel.predict(test_data)\n", "\n", "# Output the classification report and accuracy score\n", "print(\"SVM with RBF Kernel - Classification Report:\")\n", "print(classification_report(test_labels,test_predictions))\n", "print(\"Accuracy Score:\",accuracy_score(test_labels,test_predictions))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "UnK9n15Ooq0V", "outputId": "2af662f0-4e44-421d-e8ef-1b702a0f1e93"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM with RBF Kernel - Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.90      0.90      0.90        21\n", "           1       0.90      0.90      0.90        21\n", "\n", "    accuracy                           0.90        42\n", "   macro avg       0.90      0.90      0.90        42\n", "weighted avg       0.90      0.90      0.90        42\n", "\n", "Accuracy Score: 0.9047619047619048\n"]}]}, {"cell_type": "code", "source": ["# Function to plot the decision boundary\n", "def plot_boundary(test_features,true_labels,trained_model):\n", "    step_size=0.02\n", "    x_min,x_max=test_features[:,0].min() - 1,test_features[:,0].max() + 1\n", "    y_min,y_max=test_features[:,1].min() - 1,test_features[:,1].max() + 1\n", "    xx,yy=np.meshgrid(np.arange(x_min,x_max,step_size),np.arange(y_min,y_max,step_size))\n", "\n", "    grid_predictions=trained_model.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    grid_predictions=grid_predictions.reshape(xx.shape)\n", "    plt.contourf(xx,yy,grid_predictions,alpha=0.8)\n", "    plt.scatter(test_features[:,0],test_features[:,1],c=true_labels,edgecolors='k',marker='o')\n", "    plt.title('SVM Decision Boundary with RBF Kernel')\n", "    plt.show()\n", "\n", "# Plot the decision boundary\n", "plot_boundary(test_data,test_labels,svm_rbf_kernel)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "_oZdqHfDp2tm", "outputId": "36de5bf1-a615-47dc-e5c0-f995a8c1677d"}, "execution_count": 13, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["(c) <PERSON><PERSON><PERSON><PERSON>"], "metadata": {"id": "6d5yO0vBqTPR"}}, {"cell_type": "code", "source": ["# Generate a dataset\n", "x,y=make_moons(n_samples=139,noise=0.3,random_state=42)\n", "\n", "# Split the dataset into training and testing subsets\n", "train_data,test_data,train_labels,test_labels=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize and train an SVM model using a Sigmoid kernel\n", "svm_sigmoid_kernel=SVC(kernel='sigmoid')\n", "svm_sigmoid_kernel.fit(train_data,train_labels)\n", "\n", "# Predict the labels for the test dataset\n", "predictions=svm_sigmoid_kernel.predict(test_data)\n", "\n", "# Print out the classification report and accuracy score\n", "print(\"SVM with Sigmoid Kernel - Classification Report:\")\n", "print(classification_report(test_labels,predictions))\n", "print(\"Accuracy Score:\",accuracy_score(test_labels,predictions))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lK52pfNoqaLr", "outputId": "f2d57819-d7ec-473d-b095-aeeb33e8c5c8"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM with Sigmoid Kernel - Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.61      0.81      0.69        21\n", "           1       0.71      0.48      0.57        21\n", "\n", "    accuracy                           0.64        42\n", "   macro avg       0.66      0.64      0.63        42\n", "weighted avg       0.66      0.64      0.63        42\n", "\n", "Accuracy Score: 0.6428571428571429\n"]}]}, {"cell_type": "code", "source": ["# Function to visualize the decision boundary\n", "def plot_decision_boundary(features,true_labels,model):\n", "    step=0.02\n", "    x_min,x_max=features[:,0].min() - 1,features[:,0].max() + 1\n", "    y_min,y_max=features[:,1].min() - 1,features[:,1].max() + 1\n", "    xx,yy=np.meshgrid(np.arange(x_min,x_max,step),np.arange(y_min,y_max,step))\n", "\n", "    grid_predictions=model.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    grid_predictions=grid_predictions.reshape(xx.shape)\n", "    plt.contourf(xx,yy,grid_predictions,alpha=0.8)\n", "    plt.scatter(features[:,0],features[:,1],c=true_labels,edgecolors='k',marker='o')\n", "    plt.title('SVM Decision Boundary with <PERSON><PERSON><PERSON><PERSON>')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "plot_decision_boundary(test_data,test_labels,svm_sigmoid_kernel)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "7zCY8DvOqo7M", "outputId": "c0128f7f-f3db-4e46-dc39-2aa569fe8b97"}, "execution_count": 15, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "markdown", "source": ["(d) Polynomial Kernel"], "metadata": {"id": "_LVwBebsrBNC"}}, {"cell_type": "code", "source": ["# Generate a dataset that is non-linearly separable\n", "x,y=make_moons(n_samples=139,noise=0.3,random_state=42)\n", "\n", "# Split the dataset into training and testing portions\n", "X_train,X_test,y_train,y_test=train_test_split(x,y,test_size=0.3,random_state=42)\n", "\n", "# Initialize and train an SVM with a polynomial kernel\n", "svm_poly_kernel=SVC(kernel='poly',degree=3)  # Polynomial kernel of degree 3\n", "svm_poly_kernel.fit(X_train,y_train)\n", "\n", "# Predict the outcomes for the test dataset\n", "predicted_labels=svm_poly_kernel.predict(X_test)\n", "\n", "# Print out the classification metrics and accuracy\n", "print(\"SVM with Polynomial Kernel - Classification Report:\")\n", "print(classification_report(y_test,predicted_labels))\n", "print(\"Model Accuracy:\",accuracy_score(y_test,predicted_labels))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2yXU25herAKW", "outputId": "cd00bac1-fe36-4021-97e6-e898434c1c8e"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["SVM with Polynomial Kernel - Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.86      0.90      0.88        21\n", "           1       0.90      0.86      0.88        21\n", "\n", "    accuracy                           0.88        42\n", "   macro avg       0.88      0.88      0.88        42\n", "weighted avg       0.88      0.88      0.88        42\n", "\n", "Model Accuracy: 0.8809523809523809\n"]}]}, {"cell_type": "code", "source": ["# Function to plot the decision boundary\n", "def visualize_decision_boundary(data,labels,classifier):\n", "    step_size=0.02\n", "    x_min,x_max=data[:,0].min() - 1,data[:,0].max() + 1\n", "    y_min,y_max=data[:,1].min() - 1,data[:,1].max() + 1\n", "    xx,yy=np.meshgrid(np.arange(x_min,x_max,step_size),np.arange(y_min,y_max,step_size))\n", "\n", "    Z=classifier.predict(np.c_[xx.ravel(),yy.ravel()])\n", "    Z=Z.reshape(xx.shape)\n", "    plt.contourf(xx,yy,Z,alpha=0.8)\n", "    plt.scatter(data[:,0],data[:,1],c=labels,edgecolor='k',marker='o')\n", "    plt.title('SVM Decision Boundary with Polynomial Kernel')\n", "    plt.show()\n", "\n", "# Visualize the decision boundary\n", "visualize_decision_boundary(X_test,y_test,svm_poly_kernel)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "byLx6mLireol", "outputId": "20cf7f80-a403-43e8-f0f5-925b54e0cbb2"}, "execution_count": 17, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}